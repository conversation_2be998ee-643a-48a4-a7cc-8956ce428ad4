import "./chunk-GUQP5DHG.js";
import {
  Fragment,
  Teleport,
  Transition,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createTextVNode,
  createVNode,
  customRef,
  defineComponent,
  getCurrentInstance,
  getCurrentScope,
  guardReactiveProps,
  h,
  inject,
  mergeModels,
  mergeProps,
  nextTick,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  onScopeDispose,
  onUnmounted,
  openBlock,
  reactive,
  readonly,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDynamicComponent,
  shallowRef,
  toDisplayString,
  toHandlers,
  toRef,
  toValue,
  unref,
  useModel,
  vModelDynamic,
  vModelText,
  watch,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers
} from "./chunk-6UQYPIB4.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/@quidgest+chatbot@0.4.0_@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescri_g2hwnjozoq74pw6f5spmpnxydm/node_modules/@quidgest/chatbot/dist/index.mjs
function ho(t7, e) {
  return function() {
    return t7.apply(e, arguments);
  };
}
var { toString: Ns } = Object.prototype;
var { getPrototypeOf: zn } = Object;
var { iterator: It, toStringTag: mo } = Symbol;
var Bt = /* @__PURE__ */ ((t7) => (e) => {
  const n = Ns.call(e);
  return t7[n] || (t7[n] = n.slice(8, -1).toLowerCase());
})(/* @__PURE__ */ Object.create(null));
var ge = (t7) => (t7 = t7.toLowerCase(), (e) => Bt(e) === t7);
var Ot = (t7) => (e) => typeof e === t7;
var { isArray: Ze } = Array;
var st = Ot("undefined");
function Is(t7) {
  return t7 !== null && !st(t7) && t7.constructor !== null && !st(t7.constructor) && ae(t7.constructor.isBuffer) && t7.constructor.isBuffer(t7);
}
var go = ge("ArrayBuffer");
function Bs(t7) {
  let e;
  return typeof ArrayBuffer < "u" && ArrayBuffer.isView ? e = ArrayBuffer.isView(t7) : e = t7 && t7.buffer && go(t7.buffer), e;
}
var Os = Ot("string");
var ae = Ot("function");
var bo = Ot("number");
var Pt = (t7) => t7 !== null && typeof t7 == "object";
var Ps = (t7) => t7 === true || t7 === false;
var kt = (t7) => {
  if (Bt(t7) !== "object")
    return false;
  const e = zn(t7);
  return (e === null || e === Object.prototype || Object.getPrototypeOf(e) === null) && !(mo in t7) && !(It in t7);
};
var zs = ge("Date");
var Ms = ge("File");
var Us = ge("Blob");
var js = ge("FileList");
var Vs = (t7) => Pt(t7) && ae(t7.pipe);
var Gs = (t7) => {
  let e;
  return t7 && (typeof FormData == "function" && t7 instanceof FormData || ae(t7.append) && ((e = Bt(t7)) === "formdata" || // detect form-data instance
  e === "object" && ae(t7.toString) && t7.toString() === "[object FormData]"));
};
var $s = ge("URLSearchParams");
var [Hs, Zs, Ws, Js] = ["ReadableStream", "Request", "Response", "Headers"].map(ge);
var Qs = (t7) => t7.trim ? t7.trim() : t7.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
function ut(t7, e, { allOwnKeys: n = false } = {}) {
  if (t7 === null || typeof t7 > "u")
    return;
  let r, o;
  if (typeof t7 != "object" && (t7 = [t7]), Ze(t7))
    for (r = 0, o = t7.length; r < o; r++)
      e.call(null, t7[r], r, t7);
  else {
    const s = n ? Object.getOwnPropertyNames(t7) : Object.keys(t7), c = s.length;
    let i;
    for (r = 0; r < c; r++)
      i = s[r], e.call(null, t7[i], i, t7);
  }
}
function vo(t7, e) {
  e = e.toLowerCase();
  const n = Object.keys(t7);
  let r = n.length, o;
  for (; r-- > 0; )
    if (o = n[r], e === o.toLowerCase())
      return o;
  return null;
}
var Ne = typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : typeof window < "u" ? window : global;
var yo = (t7) => !st(t7) && t7 !== Ne;
function Cn() {
  const { caseless: t7 } = yo(this) && this || {}, e = {}, n = (r, o) => {
    const s = t7 && vo(e, o) || o;
    kt(e[s]) && kt(r) ? e[s] = Cn(e[s], r) : kt(r) ? e[s] = Cn({}, r) : Ze(r) ? e[s] = r.slice() : e[s] = r;
  };
  for (let r = 0, o = arguments.length; r < o; r++)
    arguments[r] && ut(arguments[r], n);
  return e;
}
var Ys = (t7, e, n, { allOwnKeys: r } = {}) => (ut(e, (o, s) => {
  n && ae(o) ? t7[s] = ho(o, n) : t7[s] = o;
}, { allOwnKeys: r }), t7);
var Xs = (t7) => (t7.charCodeAt(0) === 65279 && (t7 = t7.slice(1)), t7);
var Ks = (t7, e, n, r) => {
  t7.prototype = Object.create(e.prototype, r), t7.prototype.constructor = t7, Object.defineProperty(t7, "super", {
    value: e.prototype
  }), n && Object.assign(t7.prototype, n);
};
var ec = (t7, e, n, r) => {
  let o, s, c;
  const i = {};
  if (e = e || {}, t7 == null) return e;
  do {
    for (o = Object.getOwnPropertyNames(t7), s = o.length; s-- > 0; )
      c = o[s], (!r || r(c, t7, e)) && !i[c] && (e[c] = t7[c], i[c] = true);
    t7 = n !== false && zn(t7);
  } while (t7 && (!n || n(t7, e)) && t7 !== Object.prototype);
  return e;
};
var tc = (t7, e, n) => {
  t7 = String(t7), (n === void 0 || n > t7.length) && (n = t7.length), n -= e.length;
  const r = t7.indexOf(e, n);
  return r !== -1 && r === n;
};
var nc = (t7) => {
  if (!t7) return null;
  if (Ze(t7)) return t7;
  let e = t7.length;
  if (!bo(e)) return null;
  const n = new Array(e);
  for (; e-- > 0; )
    n[e] = t7[e];
  return n;
};
var rc = /* @__PURE__ */ ((t7) => (e) => t7 && e instanceof t7)(typeof Uint8Array < "u" && zn(Uint8Array));
var oc = (t7, e) => {
  const r = (t7 && t7[It]).call(t7);
  let o;
  for (; (o = r.next()) && !o.done; ) {
    const s = o.value;
    e.call(t7, s[0], s[1]);
  }
};
var sc = (t7, e) => {
  let n;
  const r = [];
  for (; (n = t7.exec(e)) !== null; )
    r.push(n);
  return r;
};
var cc = ge("HTMLFormElement");
var ic = (t7) => t7.toLowerCase().replace(
  /[-_\s]([a-z\d])(\w*)/g,
  function(n, r, o) {
    return r.toUpperCase() + o;
  }
);
var er = (({ hasOwnProperty: t7 }) => (e, n) => t7.call(e, n))(Object.prototype);
var ac = ge("RegExp");
var wo = (t7, e) => {
  const n = Object.getOwnPropertyDescriptors(t7), r = {};
  ut(n, (o, s) => {
    let c;
    (c = e(o, s, t7)) !== false && (r[s] = c || o);
  }), Object.defineProperties(t7, r);
};
var lc = (t7) => {
  wo(t7, (e, n) => {
    if (ae(t7) && ["arguments", "caller", "callee"].indexOf(n) !== -1)
      return false;
    const r = t7[n];
    if (ae(r)) {
      if (e.enumerable = false, "writable" in e) {
        e.writable = false;
        return;
      }
      e.set || (e.set = () => {
        throw Error("Can not rewrite read-only method '" + n + "'");
      });
    }
  });
};
var uc = (t7, e) => {
  const n = {}, r = (o) => {
    o.forEach((s) => {
      n[s] = true;
    });
  };
  return Ze(t7) ? r(t7) : r(String(t7).split(e)), n;
};
var fc = () => {
};
var dc = (t7, e) => t7 != null && Number.isFinite(t7 = +t7) ? t7 : e;
function pc(t7) {
  return !!(t7 && ae(t7.append) && t7[mo] === "FormData" && t7[It]);
}
var hc = (t7) => {
  const e = new Array(10), n = (r, o) => {
    if (Pt(r)) {
      if (e.indexOf(r) >= 0)
        return;
      if (!("toJSON" in r)) {
        e[o] = r;
        const s = Ze(r) ? [] : {};
        return ut(r, (c, i) => {
          const a = n(c, o + 1);
          !st(a) && (s[i] = a);
        }), e[o] = void 0, s;
      }
    }
    return r;
  };
  return n(t7, 0);
};
var mc = ge("AsyncFunction");
var gc = (t7) => t7 && (Pt(t7) || ae(t7)) && ae(t7.then) && ae(t7.catch);
var ko = ((t7, e) => t7 ? setImmediate : e ? ((n, r) => (Ne.addEventListener("message", ({ source: o, data: s }) => {
  o === Ne && s === n && r.length && r.shift()();
}, false), (o) => {
  r.push(o), Ne.postMessage(n, "*");
}))(`axios@${Math.random()}`, []) : (n) => setTimeout(n))(
  typeof setImmediate == "function",
  ae(Ne.postMessage)
);
var bc = typeof queueMicrotask < "u" ? queueMicrotask.bind(Ne) : typeof process < "u" && process.nextTick || ko;
var vc = (t7) => t7 != null && ae(t7[It]);
var b = {
  isArray: Ze,
  isArrayBuffer: go,
  isBuffer: Is,
  isFormData: Gs,
  isArrayBufferView: Bs,
  isString: Os,
  isNumber: bo,
  isBoolean: Ps,
  isObject: Pt,
  isPlainObject: kt,
  isReadableStream: Hs,
  isRequest: Zs,
  isResponse: Ws,
  isHeaders: Js,
  isUndefined: st,
  isDate: zs,
  isFile: Ms,
  isBlob: Us,
  isRegExp: ac,
  isFunction: ae,
  isStream: Vs,
  isURLSearchParams: $s,
  isTypedArray: rc,
  isFileList: js,
  forEach: ut,
  merge: Cn,
  extend: Ys,
  trim: Qs,
  stripBOM: Xs,
  inherits: Ks,
  toFlatObject: ec,
  kindOf: Bt,
  kindOfTest: ge,
  endsWith: tc,
  toArray: nc,
  forEachEntry: oc,
  matchAll: sc,
  isHTMLForm: cc,
  hasOwnProperty: er,
  hasOwnProp: er,
  // an alias to avoid ESLint no-prototype-builtins detection
  reduceDescriptors: wo,
  freezeMethods: lc,
  toObjectSet: uc,
  toCamelCase: ic,
  noop: fc,
  toFiniteNumber: dc,
  findKey: vo,
  global: Ne,
  isContextDefined: yo,
  isSpecCompliantForm: pc,
  toJSONObject: hc,
  isAsyncFn: mc,
  isThenable: gc,
  setImmediate: ko,
  asap: bc,
  isIterable: vc
};
function O(t7, e, n, r, o) {
  Error.call(this), Error.captureStackTrace ? Error.captureStackTrace(this, this.constructor) : this.stack = new Error().stack, this.message = t7, this.name = "AxiosError", e && (this.code = e), n && (this.config = n), r && (this.request = r), o && (this.response = o, this.status = o.status ? o.status : null);
}
b.inherits(O, Error, {
  toJSON: function() {
    return {
      // Standard
      message: this.message,
      name: this.name,
      // Microsoft
      description: this.description,
      number: this.number,
      // Mozilla
      fileName: this.fileName,
      lineNumber: this.lineNumber,
      columnNumber: this.columnNumber,
      stack: this.stack,
      // Axios
      config: b.toJSONObject(this.config),
      code: this.code,
      status: this.status
    };
  }
});
var xo = O.prototype;
var _o = {};
[
  "ERR_BAD_OPTION_VALUE",
  "ERR_BAD_OPTION",
  "ECONNABORTED",
  "ETIMEDOUT",
  "ERR_NETWORK",
  "ERR_FR_TOO_MANY_REDIRECTS",
  "ERR_DEPRECATED",
  "ERR_BAD_RESPONSE",
  "ERR_BAD_REQUEST",
  "ERR_CANCELED",
  "ERR_NOT_SUPPORT",
  "ERR_INVALID_URL"
  // eslint-disable-next-line func-names
].forEach((t7) => {
  _o[t7] = { value: t7 };
});
Object.defineProperties(O, _o);
Object.defineProperty(xo, "isAxiosError", { value: true });
O.from = (t7, e, n, r, o, s) => {
  const c = Object.create(xo);
  return b.toFlatObject(t7, c, function(a) {
    return a !== Error.prototype;
  }, (i) => i !== "isAxiosError"), O.call(c, t7.message, e, n, r, o), c.cause = t7, c.name = t7.name, s && Object.assign(c, s), c;
};
var yc = null;
function Dn(t7) {
  return b.isPlainObject(t7) || b.isArray(t7);
}
function Ao(t7) {
  return b.endsWith(t7, "[]") ? t7.slice(0, -2) : t7;
}
function tr(t7, e, n) {
  return t7 ? t7.concat(e).map(function(o, s) {
    return o = Ao(o), !n && s ? "[" + o + "]" : o;
  }).join(n ? "." : "") : e;
}
function wc(t7) {
  return b.isArray(t7) && !t7.some(Dn);
}
var kc = b.toFlatObject(b, {}, null, function(e) {
  return /^is[A-Z]/.test(e);
});
function zt(t7, e, n) {
  if (!b.isObject(t7))
    throw new TypeError("target must be an object");
  e = e || new FormData(), n = b.toFlatObject(n, {
    metaTokens: true,
    dots: false,
    indexes: false
  }, false, function(g, m) {
    return !b.isUndefined(m[g]);
  });
  const r = n.metaTokens, o = n.visitor || u, s = n.dots, c = n.indexes, a = (n.Blob || typeof Blob < "u" && Blob) && b.isSpecCompliantForm(e);
  if (!b.isFunction(o))
    throw new TypeError("visitor must be a function");
  function l(p) {
    if (p === null) return "";
    if (b.isDate(p))
      return p.toISOString();
    if (!a && b.isBlob(p))
      throw new O("Blob is not supported. Use a Buffer instead.");
    return b.isArrayBuffer(p) || b.isTypedArray(p) ? a && typeof Blob == "function" ? new Blob([p]) : Buffer.from(p) : p;
  }
  function u(p, g, m) {
    let v = p;
    if (p && !m && typeof p == "object") {
      if (b.endsWith(g, "{}"))
        g = r ? g : g.slice(0, -2), p = JSON.stringify(p);
      else if (b.isArray(p) && wc(p) || (b.isFileList(p) || b.endsWith(g, "[]")) && (v = b.toArray(p)))
        return g = Ao(g), v.forEach(function(E, D) {
          !(b.isUndefined(E) || E === null) && e.append(
            // eslint-disable-next-line no-nested-ternary
            c === true ? tr([g], D, s) : c === null ? g : g + "[]",
            l(E)
          );
        }), false;
    }
    return Dn(p) ? true : (e.append(tr(m, g, s), l(p)), false);
  }
  const f = [], d = Object.assign(kc, {
    defaultVisitor: u,
    convertValue: l,
    isVisitable: Dn
  });
  function h7(p, g) {
    if (!b.isUndefined(p)) {
      if (f.indexOf(p) !== -1)
        throw Error("Circular reference detected in " + g.join("."));
      f.push(p), b.forEach(p, function(v, y) {
        (!(b.isUndefined(v) || v === null) && o.call(
          e,
          v,
          b.isString(y) ? y.trim() : y,
          g,
          d
        )) === true && h7(v, g ? g.concat(y) : [y]);
      }), f.pop();
    }
  }
  if (!b.isObject(t7))
    throw new TypeError("data must be an object");
  return h7(t7), e;
}
function nr(t7) {
  const e = {
    "!": "%21",
    "'": "%27",
    "(": "%28",
    ")": "%29",
    "~": "%7E",
    "%20": "+",
    "%00": "\0"
  };
  return encodeURIComponent(t7).replace(/[!'()~]|%20|%00/g, function(r) {
    return e[r];
  });
}
function Mn(t7, e) {
  this._pairs = [], t7 && zt(t7, this, e);
}
var Eo = Mn.prototype;
Eo.append = function(e, n) {
  this._pairs.push([e, n]);
};
Eo.toString = function(e) {
  const n = e ? function(r) {
    return e.call(this, r, nr);
  } : nr;
  return this._pairs.map(function(o) {
    return n(o[0]) + "=" + n(o[1]);
  }, "").join("&");
};
function xc(t7) {
  return encodeURIComponent(t7).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
}
function Co(t7, e, n) {
  if (!e)
    return t7;
  const r = n && n.encode || xc;
  b.isFunction(n) && (n = {
    serialize: n
  });
  const o = n && n.serialize;
  let s;
  if (o ? s = o(e, n) : s = b.isURLSearchParams(e) ? e.toString() : new Mn(e, n).toString(r), s) {
    const c = t7.indexOf("#");
    c !== -1 && (t7 = t7.slice(0, c)), t7 += (t7.indexOf("?") === -1 ? "?" : "&") + s;
  }
  return t7;
}
var rr = class {
  constructor() {
    this.handlers = [];
  }
  /**
   * Add a new interceptor to the stack
   *
   * @param {Function} fulfilled The function to handle `then` for a `Promise`
   * @param {Function} rejected The function to handle `reject` for a `Promise`
   *
   * @return {Number} An ID used to remove interceptor later
   */
  use(e, n, r) {
    return this.handlers.push({
      fulfilled: e,
      rejected: n,
      synchronous: r ? r.synchronous : false,
      runWhen: r ? r.runWhen : null
    }), this.handlers.length - 1;
  }
  /**
   * Remove an interceptor from the stack
   *
   * @param {Number} id The ID that was returned by `use`
   *
   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise
   */
  eject(e) {
    this.handlers[e] && (this.handlers[e] = null);
  }
  /**
   * Clear all interceptors from the stack
   *
   * @returns {void}
   */
  clear() {
    this.handlers && (this.handlers = []);
  }
  /**
   * Iterate over all the registered interceptors
   *
   * This method is particularly useful for skipping over any
   * interceptors that may have become `null` calling `eject`.
   *
   * @param {Function} fn The function to call for each interceptor
   *
   * @returns {void}
   */
  forEach(e) {
    b.forEach(this.handlers, function(r) {
      r !== null && e(r);
    });
  }
};
var Do = {
  silentJSONParsing: true,
  forcedJSONParsing: true,
  clarifyTimeoutError: false
};
var _c = typeof URLSearchParams < "u" ? URLSearchParams : Mn;
var Ac = typeof FormData < "u" ? FormData : null;
var Ec = typeof Blob < "u" ? Blob : null;
var Cc = {
  isBrowser: true,
  classes: {
    URLSearchParams: _c,
    FormData: Ac,
    Blob: Ec
  },
  protocols: ["http", "https", "file", "blob", "url", "data"]
};
var Un = typeof window < "u" && typeof document < "u";
var Sn = typeof navigator == "object" && navigator || void 0;
var Dc = Un && (!Sn || ["ReactNative", "NativeScript", "NS"].indexOf(Sn.product) < 0);
var Sc = typeof WorkerGlobalScope < "u" && // eslint-disable-next-line no-undef
self instanceof WorkerGlobalScope && typeof self.importScripts == "function";
var qc = Un && window.location.href || "http://localhost";
var Rc = Object.freeze(Object.defineProperty({
  __proto__: null,
  hasBrowserEnv: Un,
  hasStandardBrowserEnv: Dc,
  hasStandardBrowserWebWorkerEnv: Sc,
  navigator: Sn,
  origin: qc
}, Symbol.toStringTag, { value: "Module" }));
var oe = {
  ...Rc,
  ...Cc
};
function Tc(t7, e) {
  return zt(t7, new oe.classes.URLSearchParams(), Object.assign({
    visitor: function(n, r, o, s) {
      return oe.isNode && b.isBuffer(n) ? (this.append(r, n.toString("base64")), false) : s.defaultVisitor.apply(this, arguments);
    }
  }, e));
}
function Fc(t7) {
  return b.matchAll(/\w+|\[(\w*)]/g, t7).map((e) => e[0] === "[]" ? "" : e[1] || e[0]);
}
function Lc(t7) {
  const e = {}, n = Object.keys(t7);
  let r;
  const o = n.length;
  let s;
  for (r = 0; r < o; r++)
    s = n[r], e[s] = t7[s];
  return e;
}
function So(t7) {
  function e(n, r, o, s) {
    let c = n[s++];
    if (c === "__proto__") return true;
    const i = Number.isFinite(+c), a = s >= n.length;
    return c = !c && b.isArray(o) ? o.length : c, a ? (b.hasOwnProp(o, c) ? o[c] = [o[c], r] : o[c] = r, !i) : ((!o[c] || !b.isObject(o[c])) && (o[c] = []), e(n, r, o[c], s) && b.isArray(o[c]) && (o[c] = Lc(o[c])), !i);
  }
  if (b.isFormData(t7) && b.isFunction(t7.entries)) {
    const n = {};
    return b.forEachEntry(t7, (r, o) => {
      e(Fc(r), o, n, 0);
    }), n;
  }
  return null;
}
function Nc(t7, e, n) {
  if (b.isString(t7))
    try {
      return (e || JSON.parse)(t7), b.trim(t7);
    } catch (r) {
      if (r.name !== "SyntaxError")
        throw r;
    }
  return (n || JSON.stringify)(t7);
}
var ft = {
  transitional: Do,
  adapter: ["xhr", "http", "fetch"],
  transformRequest: [function(e, n) {
    const r = n.getContentType() || "", o = r.indexOf("application/json") > -1, s = b.isObject(e);
    if (s && b.isHTMLForm(e) && (e = new FormData(e)), b.isFormData(e))
      return o ? JSON.stringify(So(e)) : e;
    if (b.isArrayBuffer(e) || b.isBuffer(e) || b.isStream(e) || b.isFile(e) || b.isBlob(e) || b.isReadableStream(e))
      return e;
    if (b.isArrayBufferView(e))
      return e.buffer;
    if (b.isURLSearchParams(e))
      return n.setContentType("application/x-www-form-urlencoded;charset=utf-8", false), e.toString();
    let i;
    if (s) {
      if (r.indexOf("application/x-www-form-urlencoded") > -1)
        return Tc(e, this.formSerializer).toString();
      if ((i = b.isFileList(e)) || r.indexOf("multipart/form-data") > -1) {
        const a = this.env && this.env.FormData;
        return zt(
          i ? { "files[]": e } : e,
          a && new a(),
          this.formSerializer
        );
      }
    }
    return s || o ? (n.setContentType("application/json", false), Nc(e)) : e;
  }],
  transformResponse: [function(e) {
    const n = this.transitional || ft.transitional, r = n && n.forcedJSONParsing, o = this.responseType === "json";
    if (b.isResponse(e) || b.isReadableStream(e))
      return e;
    if (e && b.isString(e) && (r && !this.responseType || o)) {
      const c = !(n && n.silentJSONParsing) && o;
      try {
        return JSON.parse(e);
      } catch (i) {
        if (c)
          throw i.name === "SyntaxError" ? O.from(i, O.ERR_BAD_RESPONSE, this, null, this.response) : i;
      }
    }
    return e;
  }],
  /**
   * A timeout in milliseconds to abort a request. If set to 0 (default) a
   * timeout is not created.
   */
  timeout: 0,
  xsrfCookieName: "XSRF-TOKEN",
  xsrfHeaderName: "X-XSRF-TOKEN",
  maxContentLength: -1,
  maxBodyLength: -1,
  env: {
    FormData: oe.classes.FormData,
    Blob: oe.classes.Blob
  },
  validateStatus: function(e) {
    return e >= 200 && e < 300;
  },
  headers: {
    common: {
      Accept: "application/json, text/plain, */*",
      "Content-Type": void 0
    }
  }
};
b.forEach(["delete", "get", "head", "post", "put", "patch"], (t7) => {
  ft.headers[t7] = {};
});
var Ic = b.toObjectSet([
  "age",
  "authorization",
  "content-length",
  "content-type",
  "etag",
  "expires",
  "from",
  "host",
  "if-modified-since",
  "if-unmodified-since",
  "last-modified",
  "location",
  "max-forwards",
  "proxy-authorization",
  "referer",
  "retry-after",
  "user-agent"
]);
var Bc = (t7) => {
  const e = {};
  let n, r, o;
  return t7 && t7.split(`
`).forEach(function(c) {
    o = c.indexOf(":"), n = c.substring(0, o).trim().toLowerCase(), r = c.substring(o + 1).trim(), !(!n || e[n] && Ic[n]) && (n === "set-cookie" ? e[n] ? e[n].push(r) : e[n] = [r] : e[n] = e[n] ? e[n] + ", " + r : r);
  }), e;
};
var or = Symbol("internals");
function Ke(t7) {
  return t7 && String(t7).trim().toLowerCase();
}
function xt(t7) {
  return t7 === false || t7 == null ? t7 : b.isArray(t7) ? t7.map(xt) : String(t7);
}
function Oc(t7) {
  const e = /* @__PURE__ */ Object.create(null), n = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
  let r;
  for (; r = n.exec(t7); )
    e[r[1]] = r[2];
  return e;
}
var Pc = (t7) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t7.trim());
function Kt(t7, e, n, r, o) {
  if (b.isFunction(r))
    return r.call(this, e, n);
  if (o && (e = n), !!b.isString(e)) {
    if (b.isString(r))
      return e.indexOf(r) !== -1;
    if (b.isRegExp(r))
      return r.test(e);
  }
}
function zc(t7) {
  return t7.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (e, n, r) => n.toUpperCase() + r);
}
function Mc(t7, e) {
  const n = b.toCamelCase(" " + e);
  ["get", "set", "has"].forEach((r) => {
    Object.defineProperty(t7, r + n, {
      value: function(o, s, c) {
        return this[r].call(this, e, o, s, c);
      },
      configurable: true
    });
  });
}
var le = class {
  constructor(e) {
    e && this.set(e);
  }
  set(e, n, r) {
    const o = this;
    function s(i, a, l) {
      const u = Ke(a);
      if (!u)
        throw new Error("header name must be a non-empty string");
      const f = b.findKey(o, u);
      (!f || o[f] === void 0 || l === true || l === void 0 && o[f] !== false) && (o[f || a] = xt(i));
    }
    const c = (i, a) => b.forEach(i, (l, u) => s(l, u, a));
    if (b.isPlainObject(e) || e instanceof this.constructor)
      c(e, n);
    else if (b.isString(e) && (e = e.trim()) && !Pc(e))
      c(Bc(e), n);
    else if (b.isObject(e) && b.isIterable(e)) {
      let i = {}, a, l;
      for (const u of e) {
        if (!b.isArray(u))
          throw TypeError("Object iterator must return a key-value pair");
        i[l = u[0]] = (a = i[l]) ? b.isArray(a) ? [...a, u[1]] : [a, u[1]] : u[1];
      }
      c(i, n);
    } else
      e != null && s(n, e, r);
    return this;
  }
  get(e, n) {
    if (e = Ke(e), e) {
      const r = b.findKey(this, e);
      if (r) {
        const o = this[r];
        if (!n)
          return o;
        if (n === true)
          return Oc(o);
        if (b.isFunction(n))
          return n.call(this, o, r);
        if (b.isRegExp(n))
          return n.exec(o);
        throw new TypeError("parser must be boolean|regexp|function");
      }
    }
  }
  has(e, n) {
    if (e = Ke(e), e) {
      const r = b.findKey(this, e);
      return !!(r && this[r] !== void 0 && (!n || Kt(this, this[r], r, n)));
    }
    return false;
  }
  delete(e, n) {
    const r = this;
    let o = false;
    function s(c) {
      if (c = Ke(c), c) {
        const i = b.findKey(r, c);
        i && (!n || Kt(r, r[i], i, n)) && (delete r[i], o = true);
      }
    }
    return b.isArray(e) ? e.forEach(s) : s(e), o;
  }
  clear(e) {
    const n = Object.keys(this);
    let r = n.length, o = false;
    for (; r--; ) {
      const s = n[r];
      (!e || Kt(this, this[s], s, e, true)) && (delete this[s], o = true);
    }
    return o;
  }
  normalize(e) {
    const n = this, r = {};
    return b.forEach(this, (o, s) => {
      const c = b.findKey(r, s);
      if (c) {
        n[c] = xt(o), delete n[s];
        return;
      }
      const i = e ? zc(s) : String(s).trim();
      i !== s && delete n[s], n[i] = xt(o), r[i] = true;
    }), this;
  }
  concat(...e) {
    return this.constructor.concat(this, ...e);
  }
  toJSON(e) {
    const n = /* @__PURE__ */ Object.create(null);
    return b.forEach(this, (r, o) => {
      r != null && r !== false && (n[o] = e && b.isArray(r) ? r.join(", ") : r);
    }), n;
  }
  [Symbol.iterator]() {
    return Object.entries(this.toJSON())[Symbol.iterator]();
  }
  toString() {
    return Object.entries(this.toJSON()).map(([e, n]) => e + ": " + n).join(`
`);
  }
  getSetCookie() {
    return this.get("set-cookie") || [];
  }
  get [Symbol.toStringTag]() {
    return "AxiosHeaders";
  }
  static from(e) {
    return e instanceof this ? e : new this(e);
  }
  static concat(e, ...n) {
    const r = new this(e);
    return n.forEach((o) => r.set(o)), r;
  }
  static accessor(e) {
    const r = (this[or] = this[or] = {
      accessors: {}
    }).accessors, o = this.prototype;
    function s(c) {
      const i = Ke(c);
      r[i] || (Mc(o, c), r[i] = true);
    }
    return b.isArray(e) ? e.forEach(s) : s(e), this;
  }
};
le.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
b.reduceDescriptors(le.prototype, ({ value: t7 }, e) => {
  let n = e[0].toUpperCase() + e.slice(1);
  return {
    get: () => t7,
    set(r) {
      this[n] = r;
    }
  };
});
b.freezeMethods(le);
function en(t7, e) {
  const n = this || ft, r = e || n, o = le.from(r.headers);
  let s = r.data;
  return b.forEach(t7, function(i) {
    s = i.call(n, s, o.normalize(), e ? e.status : void 0);
  }), o.normalize(), s;
}
function qo(t7) {
  return !!(t7 && t7.__CANCEL__);
}
function We(t7, e, n) {
  O.call(this, t7 ?? "canceled", O.ERR_CANCELED, e, n), this.name = "CanceledError";
}
b.inherits(We, O, {
  __CANCEL__: true
});
function Ro(t7, e, n) {
  const r = n.config.validateStatus;
  !n.status || !r || r(n.status) ? t7(n) : e(new O(
    "Request failed with status code " + n.status,
    [O.ERR_BAD_REQUEST, O.ERR_BAD_RESPONSE][Math.floor(n.status / 100) - 4],
    n.config,
    n.request,
    n
  ));
}
function Uc(t7) {
  const e = /^([-+\w]{1,25})(:?\/\/|:)/.exec(t7);
  return e && e[1] || "";
}
function jc(t7, e) {
  t7 = t7 || 10;
  const n = new Array(t7), r = new Array(t7);
  let o = 0, s = 0, c;
  return e = e !== void 0 ? e : 1e3, function(a) {
    const l = Date.now(), u = r[s];
    c || (c = l), n[o] = a, r[o] = l;
    let f = s, d = 0;
    for (; f !== o; )
      d += n[f++], f = f % t7;
    if (o = (o + 1) % t7, o === s && (s = (s + 1) % t7), l - c < e)
      return;
    const h7 = u && l - u;
    return h7 ? Math.round(d * 1e3 / h7) : void 0;
  };
}
function Vc(t7, e) {
  let n = 0, r = 1e3 / e, o, s;
  const c = (l, u = Date.now()) => {
    n = u, o = null, s && (clearTimeout(s), s = null), t7.apply(null, l);
  };
  return [(...l) => {
    const u = Date.now(), f = u - n;
    f >= r ? c(l, u) : (o = l, s || (s = setTimeout(() => {
      s = null, c(o);
    }, r - f)));
  }, () => o && c(o)];
}
var Ct = (t7, e, n = 3) => {
  let r = 0;
  const o = jc(50, 250);
  return Vc((s) => {
    const c = s.loaded, i = s.lengthComputable ? s.total : void 0, a = c - r, l = o(a), u = c <= i;
    r = c;
    const f = {
      loaded: c,
      total: i,
      progress: i ? c / i : void 0,
      bytes: a,
      rate: l || void 0,
      estimated: l && i && u ? (i - c) / l : void 0,
      event: s,
      lengthComputable: i != null,
      [e ? "download" : "upload"]: true
    };
    t7(f);
  }, n);
};
var sr = (t7, e) => {
  const n = t7 != null;
  return [(r) => e[0]({
    lengthComputable: n,
    total: t7,
    loaded: r
  }), e[1]];
};
var cr = (t7) => (...e) => b.asap(() => t7(...e));
var Gc = oe.hasStandardBrowserEnv ? /* @__PURE__ */ ((t7, e) => (n) => (n = new URL(n, oe.origin), t7.protocol === n.protocol && t7.host === n.host && (e || t7.port === n.port)))(
  new URL(oe.origin),
  oe.navigator && /(msie|trident)/i.test(oe.navigator.userAgent)
) : () => true;
var $c = oe.hasStandardBrowserEnv ? (
  // Standard browser envs support document.cookie
  {
    write(t7, e, n, r, o, s) {
      const c = [t7 + "=" + encodeURIComponent(e)];
      b.isNumber(n) && c.push("expires=" + new Date(n).toGMTString()), b.isString(r) && c.push("path=" + r), b.isString(o) && c.push("domain=" + o), s === true && c.push("secure"), document.cookie = c.join("; ");
    },
    read(t7) {
      const e = document.cookie.match(new RegExp("(^|;\\s*)(" + t7 + ")=([^;]*)"));
      return e ? decodeURIComponent(e[3]) : null;
    },
    remove(t7) {
      this.write(t7, "", Date.now() - 864e5);
    }
  }
) : (
  // Non-standard browser env (web workers, react-native) lack needed support.
  {
    write() {
    },
    read() {
      return null;
    },
    remove() {
    }
  }
);
function Hc(t7) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(t7);
}
function Zc(t7, e) {
  return e ? t7.replace(/\/?\/$/, "") + "/" + e.replace(/^\/+/, "") : t7;
}
function To(t7, e, n) {
  let r = !Hc(e);
  return t7 && (r || n == false) ? Zc(t7, e) : e;
}
var ir = (t7) => t7 instanceof le ? { ...t7 } : t7;
function ze(t7, e) {
  e = e || {};
  const n = {};
  function r(l, u, f, d) {
    return b.isPlainObject(l) && b.isPlainObject(u) ? b.merge.call({ caseless: d }, l, u) : b.isPlainObject(u) ? b.merge({}, u) : b.isArray(u) ? u.slice() : u;
  }
  function o(l, u, f, d) {
    if (b.isUndefined(u)) {
      if (!b.isUndefined(l))
        return r(void 0, l, f, d);
    } else return r(l, u, f, d);
  }
  function s(l, u) {
    if (!b.isUndefined(u))
      return r(void 0, u);
  }
  function c(l, u) {
    if (b.isUndefined(u)) {
      if (!b.isUndefined(l))
        return r(void 0, l);
    } else return r(void 0, u);
  }
  function i(l, u, f) {
    if (f in e)
      return r(l, u);
    if (f in t7)
      return r(void 0, l);
  }
  const a = {
    url: s,
    method: s,
    data: s,
    baseURL: c,
    transformRequest: c,
    transformResponse: c,
    paramsSerializer: c,
    timeout: c,
    timeoutMessage: c,
    withCredentials: c,
    withXSRFToken: c,
    adapter: c,
    responseType: c,
    xsrfCookieName: c,
    xsrfHeaderName: c,
    onUploadProgress: c,
    onDownloadProgress: c,
    decompress: c,
    maxContentLength: c,
    maxBodyLength: c,
    beforeRedirect: c,
    transport: c,
    httpAgent: c,
    httpsAgent: c,
    cancelToken: c,
    socketPath: c,
    responseEncoding: c,
    validateStatus: i,
    headers: (l, u, f) => o(ir(l), ir(u), f, true)
  };
  return b.forEach(Object.keys(Object.assign({}, t7, e)), function(u) {
    const f = a[u] || o, d = f(t7[u], e[u], u);
    b.isUndefined(d) && f !== i || (n[u] = d);
  }), n;
}
var Fo = (t7) => {
  const e = ze({}, t7);
  let { data: n, withXSRFToken: r, xsrfHeaderName: o, xsrfCookieName: s, headers: c, auth: i } = e;
  e.headers = c = le.from(c), e.url = Co(To(e.baseURL, e.url, e.allowAbsoluteUrls), t7.params, t7.paramsSerializer), i && c.set(
    "Authorization",
    "Basic " + btoa((i.username || "") + ":" + (i.password ? unescape(encodeURIComponent(i.password)) : ""))
  );
  let a;
  if (b.isFormData(n)) {
    if (oe.hasStandardBrowserEnv || oe.hasStandardBrowserWebWorkerEnv)
      c.setContentType(void 0);
    else if ((a = c.getContentType()) !== false) {
      const [l, ...u] = a ? a.split(";").map((f) => f.trim()).filter(Boolean) : [];
      c.setContentType([l || "multipart/form-data", ...u].join("; "));
    }
  }
  if (oe.hasStandardBrowserEnv && (r && b.isFunction(r) && (r = r(e)), r || r !== false && Gc(e.url))) {
    const l = o && s && $c.read(s);
    l && c.set(o, l);
  }
  return e;
};
var Wc = typeof XMLHttpRequest < "u";
var Jc = Wc && function(t7) {
  return new Promise(function(n, r) {
    const o = Fo(t7);
    let s = o.data;
    const c = le.from(o.headers).normalize();
    let { responseType: i, onUploadProgress: a, onDownloadProgress: l } = o, u, f, d, h7, p;
    function g() {
      h7 && h7(), p && p(), o.cancelToken && o.cancelToken.unsubscribe(u), o.signal && o.signal.removeEventListener("abort", u);
    }
    let m = new XMLHttpRequest();
    m.open(o.method.toUpperCase(), o.url, true), m.timeout = o.timeout;
    function v() {
      if (!m)
        return;
      const E = le.from(
        "getAllResponseHeaders" in m && m.getAllResponseHeaders()
      ), S = {
        data: !i || i === "text" || i === "json" ? m.responseText : m.response,
        status: m.status,
        statusText: m.statusText,
        headers: E,
        config: t7,
        request: m
      };
      Ro(function(T) {
        n(T), g();
      }, function(T) {
        r(T), g();
      }, S), m = null;
    }
    "onloadend" in m ? m.onloadend = v : m.onreadystatechange = function() {
      !m || m.readyState !== 4 || m.status === 0 && !(m.responseURL && m.responseURL.indexOf("file:") === 0) || setTimeout(v);
    }, m.onabort = function() {
      m && (r(new O("Request aborted", O.ECONNABORTED, t7, m)), m = null);
    }, m.onerror = function() {
      r(new O("Network Error", O.ERR_NETWORK, t7, m)), m = null;
    }, m.ontimeout = function() {
      let D = o.timeout ? "timeout of " + o.timeout + "ms exceeded" : "timeout exceeded";
      const S = o.transitional || Do;
      o.timeoutErrorMessage && (D = o.timeoutErrorMessage), r(new O(
        D,
        S.clarifyTimeoutError ? O.ETIMEDOUT : O.ECONNABORTED,
        t7,
        m
      )), m = null;
    }, s === void 0 && c.setContentType(null), "setRequestHeader" in m && b.forEach(c.toJSON(), function(D, S) {
      m.setRequestHeader(S, D);
    }), b.isUndefined(o.withCredentials) || (m.withCredentials = !!o.withCredentials), i && i !== "json" && (m.responseType = o.responseType), l && ([d, p] = Ct(l, true), m.addEventListener("progress", d)), a && m.upload && ([f, h7] = Ct(a), m.upload.addEventListener("progress", f), m.upload.addEventListener("loadend", h7)), (o.cancelToken || o.signal) && (u = (E) => {
      m && (r(!E || E.type ? new We(null, t7, m) : E), m.abort(), m = null);
    }, o.cancelToken && o.cancelToken.subscribe(u), o.signal && (o.signal.aborted ? u() : o.signal.addEventListener("abort", u)));
    const y = Uc(o.url);
    if (y && oe.protocols.indexOf(y) === -1) {
      r(new O("Unsupported protocol " + y + ":", O.ERR_BAD_REQUEST, t7));
      return;
    }
    m.send(s || null);
  });
};
var Qc = (t7, e) => {
  const { length: n } = t7 = t7 ? t7.filter(Boolean) : [];
  if (e || n) {
    let r = new AbortController(), o;
    const s = function(l) {
      if (!o) {
        o = true, i();
        const u = l instanceof Error ? l : this.reason;
        r.abort(u instanceof O ? u : new We(u instanceof Error ? u.message : u));
      }
    };
    let c = e && setTimeout(() => {
      c = null, s(new O(`timeout ${e} of ms exceeded`, O.ETIMEDOUT));
    }, e);
    const i = () => {
      t7 && (c && clearTimeout(c), c = null, t7.forEach((l) => {
        l.unsubscribe ? l.unsubscribe(s) : l.removeEventListener("abort", s);
      }), t7 = null);
    };
    t7.forEach((l) => l.addEventListener("abort", s));
    const { signal: a } = r;
    return a.unsubscribe = () => b.asap(i), a;
  }
};
var Yc = function* (t7, e) {
  let n = t7.byteLength;
  if (n < e) {
    yield t7;
    return;
  }
  let r = 0, o;
  for (; r < n; )
    o = r + e, yield t7.slice(r, o), r = o;
};
var Xc = async function* (t7, e) {
  for await (const n of Kc(t7))
    yield* Yc(n, e);
};
var Kc = async function* (t7) {
  if (t7[Symbol.asyncIterator]) {
    yield* t7;
    return;
  }
  const e = t7.getReader();
  try {
    for (; ; ) {
      const { done: n, value: r } = await e.read();
      if (n)
        break;
      yield r;
    }
  } finally {
    await e.cancel();
  }
};
var ar = (t7, e, n, r) => {
  const o = Xc(t7, e);
  let s = 0, c, i = (a) => {
    c || (c = true, r && r(a));
  };
  return new ReadableStream({
    async pull(a) {
      try {
        const { done: l, value: u } = await o.next();
        if (l) {
          i(), a.close();
          return;
        }
        let f = u.byteLength;
        if (n) {
          let d = s += f;
          n(d);
        }
        a.enqueue(new Uint8Array(u));
      } catch (l) {
        throw i(l), l;
      }
    },
    cancel(a) {
      return i(a), o.return();
    }
  }, {
    highWaterMark: 2
  });
};
var Mt = typeof fetch == "function" && typeof Request == "function" && typeof Response == "function";
var Lo = Mt && typeof ReadableStream == "function";
var ei = Mt && (typeof TextEncoder == "function" ? /* @__PURE__ */ ((t7) => (e) => t7.encode(e))(new TextEncoder()) : async (t7) => new Uint8Array(await new Response(t7).arrayBuffer()));
var No = (t7, ...e) => {
  try {
    return !!t7(...e);
  } catch {
    return false;
  }
};
var ti = Lo && No(() => {
  let t7 = false;
  const e = new Request(oe.origin, {
    body: new ReadableStream(),
    method: "POST",
    get duplex() {
      return t7 = true, "half";
    }
  }).headers.has("Content-Type");
  return t7 && !e;
});
var lr = 64 * 1024;
var qn = Lo && No(() => b.isReadableStream(new Response("").body));
var Dt = {
  stream: qn && ((t7) => t7.body)
};
Mt && ((t7) => {
  ["text", "arrayBuffer", "blob", "formData", "stream"].forEach((e) => {
    !Dt[e] && (Dt[e] = b.isFunction(t7[e]) ? (n) => n[e]() : (n, r) => {
      throw new O(`Response type '${e}' is not supported`, O.ERR_NOT_SUPPORT, r);
    });
  });
})(new Response());
var ni = async (t7) => {
  if (t7 == null)
    return 0;
  if (b.isBlob(t7))
    return t7.size;
  if (b.isSpecCompliantForm(t7))
    return (await new Request(oe.origin, {
      method: "POST",
      body: t7
    }).arrayBuffer()).byteLength;
  if (b.isArrayBufferView(t7) || b.isArrayBuffer(t7))
    return t7.byteLength;
  if (b.isURLSearchParams(t7) && (t7 = t7 + ""), b.isString(t7))
    return (await ei(t7)).byteLength;
};
var ri = async (t7, e) => {
  const n = b.toFiniteNumber(t7.getContentLength());
  return n ?? ni(e);
};
var oi = Mt && (async (t7) => {
  let {
    url: e,
    method: n,
    data: r,
    signal: o,
    cancelToken: s,
    timeout: c,
    onDownloadProgress: i,
    onUploadProgress: a,
    responseType: l,
    headers: u,
    withCredentials: f = "same-origin",
    fetchOptions: d
  } = Fo(t7);
  l = l ? (l + "").toLowerCase() : "text";
  let h7 = Qc([o, s && s.toAbortSignal()], c), p;
  const g = h7 && h7.unsubscribe && (() => {
    h7.unsubscribe();
  });
  let m;
  try {
    if (a && ti && n !== "get" && n !== "head" && (m = await ri(u, r)) !== 0) {
      let S = new Request(e, {
        method: "POST",
        body: r,
        duplex: "half"
      }), _;
      if (b.isFormData(r) && (_ = S.headers.get("content-type")) && u.setContentType(_), S.body) {
        const [T, U] = sr(
          m,
          Ct(cr(a))
        );
        r = ar(S.body, lr, T, U);
      }
    }
    b.isString(f) || (f = f ? "include" : "omit");
    const v = "credentials" in Request.prototype;
    p = new Request(e, {
      ...d,
      signal: h7,
      method: n.toUpperCase(),
      headers: u.normalize().toJSON(),
      body: r,
      duplex: "half",
      credentials: v ? f : void 0
    });
    let y = await fetch(p);
    const E = qn && (l === "stream" || l === "response");
    if (qn && (i || E && g)) {
      const S = {};
      ["status", "statusText", "headers"].forEach((K) => {
        S[K] = y[K];
      });
      const _ = b.toFiniteNumber(y.headers.get("content-length")), [T, U] = i && sr(
        _,
        Ct(cr(i), true)
      ) || [];
      y = new Response(
        ar(y.body, lr, T, () => {
          U && U(), g && g();
        }),
        S
      );
    }
    l = l || "text";
    let D = await Dt[b.findKey(Dt, l) || "text"](y, t7);
    return !E && g && g(), await new Promise((S, _) => {
      Ro(S, _, {
        data: D,
        headers: le.from(y.headers),
        status: y.status,
        statusText: y.statusText,
        config: t7,
        request: p
      });
    });
  } catch (v) {
    throw g && g(), v && v.name === "TypeError" && /Load failed|fetch/i.test(v.message) ? Object.assign(
      new O("Network Error", O.ERR_NETWORK, t7, p),
      {
        cause: v.cause || v
      }
    ) : O.from(v, v && v.code, t7, p);
  }
});
var Rn = {
  http: yc,
  xhr: Jc,
  fetch: oi
};
b.forEach(Rn, (t7, e) => {
  if (t7) {
    try {
      Object.defineProperty(t7, "name", { value: e });
    } catch {
    }
    Object.defineProperty(t7, "adapterName", { value: e });
  }
});
var ur = (t7) => `- ${t7}`;
var si = (t7) => b.isFunction(t7) || t7 === null || t7 === false;
var Io = {
  getAdapter: (t7) => {
    t7 = b.isArray(t7) ? t7 : [t7];
    const { length: e } = t7;
    let n, r;
    const o = {};
    for (let s = 0; s < e; s++) {
      n = t7[s];
      let c;
      if (r = n, !si(n) && (r = Rn[(c = String(n)).toLowerCase()], r === void 0))
        throw new O(`Unknown adapter '${c}'`);
      if (r)
        break;
      o[c || "#" + s] = r;
    }
    if (!r) {
      const s = Object.entries(o).map(
        ([i, a]) => `adapter ${i} ` + (a === false ? "is not supported by the environment" : "is not available in the build")
      );
      let c = e ? s.length > 1 ? `since :
` + s.map(ur).join(`
`) : " " + ur(s[0]) : "as no adapter specified";
      throw new O(
        "There is no suitable adapter to dispatch the request " + c,
        "ERR_NOT_SUPPORT"
      );
    }
    return r;
  },
  adapters: Rn
};
function tn(t7) {
  if (t7.cancelToken && t7.cancelToken.throwIfRequested(), t7.signal && t7.signal.aborted)
    throw new We(null, t7);
}
function fr(t7) {
  return tn(t7), t7.headers = le.from(t7.headers), t7.data = en.call(
    t7,
    t7.transformRequest
  ), ["post", "put", "patch"].indexOf(t7.method) !== -1 && t7.headers.setContentType("application/x-www-form-urlencoded", false), Io.getAdapter(t7.adapter || ft.adapter)(t7).then(function(r) {
    return tn(t7), r.data = en.call(
      t7,
      t7.transformResponse,
      r
    ), r.headers = le.from(r.headers), r;
  }, function(r) {
    return qo(r) || (tn(t7), r && r.response && (r.response.data = en.call(
      t7,
      t7.transformResponse,
      r.response
    ), r.response.headers = le.from(r.response.headers))), Promise.reject(r);
  });
}
var Bo = "1.9.0";
var Ut = {};
["object", "boolean", "number", "function", "string", "symbol"].forEach((t7, e) => {
  Ut[t7] = function(r) {
    return typeof r === t7 || "a" + (e < 1 ? "n " : " ") + t7;
  };
});
var dr = {};
Ut.transitional = function(e, n, r) {
  function o(s, c) {
    return "[Axios v" + Bo + "] Transitional option '" + s + "'" + c + (r ? ". " + r : "");
  }
  return (s, c, i) => {
    if (e === false)
      throw new O(
        o(c, " has been removed" + (n ? " in " + n : "")),
        O.ERR_DEPRECATED
      );
    return n && !dr[c] && (dr[c] = true, console.warn(
      o(
        c,
        " has been deprecated since v" + n + " and will be removed in the near future"
      )
    )), e ? e(s, c, i) : true;
  };
};
Ut.spelling = function(e) {
  return (n, r) => (console.warn(`${r} is likely a misspelling of ${e}`), true);
};
function ci(t7, e, n) {
  if (typeof t7 != "object")
    throw new O("options must be an object", O.ERR_BAD_OPTION_VALUE);
  const r = Object.keys(t7);
  let o = r.length;
  for (; o-- > 0; ) {
    const s = r[o], c = e[s];
    if (c) {
      const i = t7[s], a = i === void 0 || c(i, s, t7);
      if (a !== true)
        throw new O("option " + s + " must be " + a, O.ERR_BAD_OPTION_VALUE);
      continue;
    }
    if (n !== true)
      throw new O("Unknown option " + s, O.ERR_BAD_OPTION);
  }
}
var _t = {
  assertOptions: ci,
  validators: Ut
};
var ve = _t.validators;
var Oe = class {
  constructor(e) {
    this.defaults = e || {}, this.interceptors = {
      request: new rr(),
      response: new rr()
    };
  }
  /**
   * Dispatch a request
   *
   * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)
   * @param {?Object} config
   *
   * @returns {Promise} The Promise to be fulfilled
   */
  async request(e, n) {
    try {
      return await this._request(e, n);
    } catch (r) {
      if (r instanceof Error) {
        let o = {};
        Error.captureStackTrace ? Error.captureStackTrace(o) : o = new Error();
        const s = o.stack ? o.stack.replace(/^.+\n/, "") : "";
        try {
          r.stack ? s && !String(r.stack).endsWith(s.replace(/^.+\n.+\n/, "")) && (r.stack += `
` + s) : r.stack = s;
        } catch {
        }
      }
      throw r;
    }
  }
  _request(e, n) {
    typeof e == "string" ? (n = n || {}, n.url = e) : n = e || {}, n = ze(this.defaults, n);
    const { transitional: r, paramsSerializer: o, headers: s } = n;
    r !== void 0 && _t.assertOptions(r, {
      silentJSONParsing: ve.transitional(ve.boolean),
      forcedJSONParsing: ve.transitional(ve.boolean),
      clarifyTimeoutError: ve.transitional(ve.boolean)
    }, false), o != null && (b.isFunction(o) ? n.paramsSerializer = {
      serialize: o
    } : _t.assertOptions(o, {
      encode: ve.function,
      serialize: ve.function
    }, true)), n.allowAbsoluteUrls !== void 0 || (this.defaults.allowAbsoluteUrls !== void 0 ? n.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls : n.allowAbsoluteUrls = true), _t.assertOptions(n, {
      baseUrl: ve.spelling("baseURL"),
      withXsrfToken: ve.spelling("withXSRFToken")
    }, true), n.method = (n.method || this.defaults.method || "get").toLowerCase();
    let c = s && b.merge(
      s.common,
      s[n.method]
    );
    s && b.forEach(
      ["delete", "get", "head", "post", "put", "patch", "common"],
      (p) => {
        delete s[p];
      }
    ), n.headers = le.concat(c, s);
    const i = [];
    let a = true;
    this.interceptors.request.forEach(function(g) {
      typeof g.runWhen == "function" && g.runWhen(n) === false || (a = a && g.synchronous, i.unshift(g.fulfilled, g.rejected));
    });
    const l = [];
    this.interceptors.response.forEach(function(g) {
      l.push(g.fulfilled, g.rejected);
    });
    let u, f = 0, d;
    if (!a) {
      const p = [fr.bind(this), void 0];
      for (p.unshift.apply(p, i), p.push.apply(p, l), d = p.length, u = Promise.resolve(n); f < d; )
        u = u.then(p[f++], p[f++]);
      return u;
    }
    d = i.length;
    let h7 = n;
    for (f = 0; f < d; ) {
      const p = i[f++], g = i[f++];
      try {
        h7 = p(h7);
      } catch (m) {
        g.call(this, m);
        break;
      }
    }
    try {
      u = fr.call(this, h7);
    } catch (p) {
      return Promise.reject(p);
    }
    for (f = 0, d = l.length; f < d; )
      u = u.then(l[f++], l[f++]);
    return u;
  }
  getUri(e) {
    e = ze(this.defaults, e);
    const n = To(e.baseURL, e.url, e.allowAbsoluteUrls);
    return Co(n, e.params, e.paramsSerializer);
  }
};
b.forEach(["delete", "get", "head", "options"], function(e) {
  Oe.prototype[e] = function(n, r) {
    return this.request(ze(r || {}, {
      method: e,
      url: n,
      data: (r || {}).data
    }));
  };
});
b.forEach(["post", "put", "patch"], function(e) {
  function n(r) {
    return function(s, c, i) {
      return this.request(ze(i || {}, {
        method: e,
        headers: r ? {
          "Content-Type": "multipart/form-data"
        } : {},
        url: s,
        data: c
      }));
    };
  }
  Oe.prototype[e] = n(), Oe.prototype[e + "Form"] = n(true);
});
var ii = class Oo {
  constructor(e) {
    if (typeof e != "function")
      throw new TypeError("executor must be a function.");
    let n;
    this.promise = new Promise(function(s) {
      n = s;
    });
    const r = this;
    this.promise.then((o) => {
      if (!r._listeners) return;
      let s = r._listeners.length;
      for (; s-- > 0; )
        r._listeners[s](o);
      r._listeners = null;
    }), this.promise.then = (o) => {
      let s;
      const c = new Promise((i) => {
        r.subscribe(i), s = i;
      }).then(o);
      return c.cancel = function() {
        r.unsubscribe(s);
      }, c;
    }, e(function(s, c, i) {
      r.reason || (r.reason = new We(s, c, i), n(r.reason));
    });
  }
  /**
   * Throws a `CanceledError` if cancellation has been requested.
   */
  throwIfRequested() {
    if (this.reason)
      throw this.reason;
  }
  /**
   * Subscribe to the cancel signal
   */
  subscribe(e) {
    if (this.reason) {
      e(this.reason);
      return;
    }
    this._listeners ? this._listeners.push(e) : this._listeners = [e];
  }
  /**
   * Unsubscribe from the cancel signal
   */
  unsubscribe(e) {
    if (!this._listeners)
      return;
    const n = this._listeners.indexOf(e);
    n !== -1 && this._listeners.splice(n, 1);
  }
  toAbortSignal() {
    const e = new AbortController(), n = (r) => {
      e.abort(r);
    };
    return this.subscribe(n), e.signal.unsubscribe = () => this.unsubscribe(n), e.signal;
  }
  /**
   * Returns an object that contains a new `CancelToken` and a function that, when called,
   * cancels the `CancelToken`.
   */
  static source() {
    let e;
    return {
      token: new Oo(function(o) {
        e = o;
      }),
      cancel: e
    };
  }
};
function ai(t7) {
  return function(n) {
    return t7.apply(null, n);
  };
}
function li(t7) {
  return b.isObject(t7) && t7.isAxiosError === true;
}
var Tn = {
  Continue: 100,
  SwitchingProtocols: 101,
  Processing: 102,
  EarlyHints: 103,
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NonAuthoritativeInformation: 203,
  NoContent: 204,
  ResetContent: 205,
  PartialContent: 206,
  MultiStatus: 207,
  AlreadyReported: 208,
  ImUsed: 226,
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  SeeOther: 303,
  NotModified: 304,
  UseProxy: 305,
  Unused: 306,
  TemporaryRedirect: 307,
  PermanentRedirect: 308,
  BadRequest: 400,
  Unauthorized: 401,
  PaymentRequired: 402,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  NotAcceptable: 406,
  ProxyAuthenticationRequired: 407,
  RequestTimeout: 408,
  Conflict: 409,
  Gone: 410,
  LengthRequired: 411,
  PreconditionFailed: 412,
  PayloadTooLarge: 413,
  UriTooLong: 414,
  UnsupportedMediaType: 415,
  RangeNotSatisfiable: 416,
  ExpectationFailed: 417,
  ImATeapot: 418,
  MisdirectedRequest: 421,
  UnprocessableEntity: 422,
  Locked: 423,
  FailedDependency: 424,
  TooEarly: 425,
  UpgradeRequired: 426,
  PreconditionRequired: 428,
  TooManyRequests: 429,
  RequestHeaderFieldsTooLarge: 431,
  UnavailableForLegalReasons: 451,
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
  HttpVersionNotSupported: 505,
  VariantAlsoNegotiates: 506,
  InsufficientStorage: 507,
  LoopDetected: 508,
  NotExtended: 510,
  NetworkAuthenticationRequired: 511
};
Object.entries(Tn).forEach(([t7, e]) => {
  Tn[e] = t7;
});
function Po(t7) {
  const e = new Oe(t7), n = ho(Oe.prototype.request, e);
  return b.extend(n, Oe.prototype, e, { allOwnKeys: true }), b.extend(n, e, null, { allOwnKeys: true }), n.create = function(o) {
    return Po(ze(t7, o));
  }, n;
}
var Y = Po(ft);
Y.Axios = Oe;
Y.CanceledError = We;
Y.CancelToken = ii;
Y.isCancel = qo;
Y.VERSION = Bo;
Y.toFormData = zt;
Y.AxiosError = O;
Y.Cancel = Y.CanceledError;
Y.all = function(e) {
  return Promise.all(e);
};
Y.spread = ai;
Y.isAxiosError = li;
Y.mergeConfig = ze;
Y.AxiosHeaders = le;
Y.formToJSON = (t7) => So(b.isHTMLForm(t7) ? new FormData(t7) : t7);
Y.getAdapter = Io.getAdapter;
Y.HttpStatusCode = Tn;
Y.default = Y;
var {
  Axios: L6,
  AxiosError: N6,
  CanceledError: I6,
  isCancel: B6,
  CancelToken: O6,
  VERSION: P6,
  all: z6,
  Cancel: M6,
  isAxiosError: U6,
  spread: j6,
  toFormData: V6,
  AxiosHeaders: G6,
  HttpStatusCode: $6,
  formToJSON: H6,
  getAdapter: Z6,
  mergeConfig: W6
} = Y;
function ui(t7) {
  return t7 == null ? true : typeof t7 == "string" || Array.isArray(t7) ? t7.length === 0 : typeof t7 == "object" ? Object.keys(t7).length === 0 : false;
}
function pr(t7) {
  return t7 !== null && typeof t7 == "object" && !Array.isArray(t7);
}
function zo(t7 = {}, e = {}) {
  const n = {}, r = /* @__PURE__ */ new Set([...Object.keys(t7), ...Object.keys(e)]);
  for (const o of r) {
    if (o === "__proto__" || o === "constructor" || o === "prototype")
      continue;
    const s = t7[o], c = e[o];
    pr(s) && pr(c) ? n[o] = zo(
      s,
      c
    ) : n[o] = c !== void 0 ? c : s;
  }
  return n;
}
var fi = "q-defaults";
function di() {
  var t7, e;
  const n = getCurrentInstance();
  if (!n)
    throw new Error("[Quidgest UI] useDefaults must be called from inside a setup function");
  const r = n.type.name ?? n.type.__name;
  if (!r) throw new Error("[Quidgest UI] Could not determine component name");
  const o = pi(), s = (t7 = o.value) == null ? void 0 : t7.Global, c = (e = o.value) == null ? void 0 : e[r];
  return computed(() => zo(s, c));
}
function pi() {
  const t7 = inject(fi, void 0);
  if (!t7) throw new Error("[Quidgest UI] Could not find defaults instance");
  return t7;
}
var hi = ["id"];
var mi = defineComponent({
  __name: "QSpinnerLoader",
  props: {
    id: {},
    class: {},
    size: { default: 48 }
  },
  setup(t7) {
    const e = t7, n = computed(() => ({
      "font-size": e.size !== 48 ? `${e.size}px` : void 0
    }));
    return (r, o) => (openBlock(), createElementBlock("div", {
      id: e.id,
      class: normalizeClass(["q-spinner-loader", e.class]),
      style: normalizeStyle(n.value)
    }, null, 14, hi));
  }
});
function gi(t7) {
  return t7.replace(/([a-z])([A-Z])/g, "$1 $2").replace(/([0-9])([a-zA-Z])/g, "$1 $2").replace(/([a-zA-Z])([0-9])/g, "$1 $2").replace(/([A-Z]+)([A-Z][a-z])/g, "$1 $2").trim().split(/\s+/);
}
function bi(t7) {
  return gi(t7).join("-").toLowerCase();
}
function vi(t7, e) {
  var n;
  const r = bi(e);
  return r ? typeof ((n = t7.props) == null ? void 0 : n[r]) < "u" : false;
}
function ce(t7) {
  const e = t7, n = e.setup;
  return n && (e.setup = (r, o) => {
    const s = di();
    if (ui(s.value)) return n(r, o);
    const c = getCurrentInstance();
    if (c === null) return n(r, o);
    const i = new Proxy(r, {
      get(a, l) {
        var u;
        const f = Reflect.get(a, l), d = (u = s.value) == null ? void 0 : u[l];
        return typeof l == "string" && !vi(c.vnode, l) ? d ?? f : f;
      }
    });
    return n(i, o);
  }), t7;
}
var yi = ce(mi);
function wi(t7, e = false) {
  return e ? /^#[a-fA-F0-9]{6}$/.test(t7) : /^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/.test(t7);
}
function ki(t7) {
  const e = t7.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
  if (e) {
    const n = parseInt(e[1], 10), r = parseInt(e[2], 10), o = parseInt(e[3], 10);
    return { r: n, g: r, b: o };
  }
}
function xi(t7) {
  if (wi(t7)) {
    t7.length === 4 && (t7 = "#" + t7[1] + t7[1] + t7[2] + t7[2] + t7[3] + t7[3]);
    const e = parseInt(t7.slice(1, 3), 16), n = parseInt(t7.slice(3, 5), 16), r = parseInt(t7.slice(5, 7), 16);
    return { r: e, g: n, b: r };
  } else {
    const e = ki(t7);
    if (e) return e;
  }
  throw new Error("Invalid color format");
}
function _i(t7, e) {
  const n = Mo(t7), r = e / 100;
  return n.l = n.l - r * n.l, Ei(n);
}
function Ai(t7) {
  const e = t7.r.toString(16).padStart(2, "0"), n = t7.g.toString(16).padStart(2, "0"), r = t7.b.toString(16).padStart(2, "0");
  return `#${e}${n}${r}`;
}
function hr(t7) {
  return `${t7.r} ${t7.g} ${t7.b}`;
}
function Mo(t7) {
  const e = t7.r / 255, n = t7.g / 255, r = t7.b / 255, o = Math.max(e, n, r), s = Math.min(e, n, r);
  let c = 0, i;
  const a = (o + s) / 2;
  if (o === s)
    c = i = 0;
  else {
    const l = o - s;
    switch (i = a > 0.5 ? l / (2 - o - s) : l / (o + s), o) {
      case e:
        c = (n - r) / l + (n < r ? 6 : 0);
        break;
      case n:
        c = (r - e) / l + 2;
        break;
      case r:
        c = (e - n) / l + 4;
        break;
    }
    c /= 6;
  }
  return {
    h: Math.round(c * 360),
    s: Math.round(i * 100),
    l: Math.round(a * 100)
  };
}
function Ei(t7) {
  const e = t7.h / 360, n = t7.s / 100, r = t7.l / 100;
  let o, s, c;
  if (n === 0)
    o = s = c = r;
  else {
    const i = r < 0.5 ? r * (1 + n) : r + n - r * n, a = 2 * r - i;
    o = nn(a, i, e + 1 / 3), s = nn(a, i, e), c = nn(a, i, e - 1 / 3);
  }
  return {
    r: Math.round(o * 255),
    g: Math.round(s * 255),
    b: Math.round(c * 255)
  };
}
function nn(t7, e, n) {
  return n < 0 && (n += 1), n > 1 && (n -= 1), n < 1 / 6 ? t7 + (e - t7) * 6 * n : n < 1 / 2 ? e : n < 2 / 3 ? t7 + (e - t7) * (2 / 3 - n) * 6 : t7;
}
function Ci(t7) {
  return t7 > 50 ? "#000" : "#fff";
}
var Di = Symbol.for("q-theme");
function Uo() {
  const t7 = inject(Di);
  if (!t7)
    throw new Error("[Quidgest UI] Could not find theme instance");
  return t7;
}
function jo(t7) {
  const e = computed(() => unref(t7)), n = computed(
    () => e.value ? !Si(e.value) : false
  ), r = computed(() => {
    const o = e.value;
    if (!o) return;
    let s = o;
    const c = Uo();
    if (n.value) {
      const d = o;
      s = c.current.value.scheme[d] ?? o;
    }
    let i;
    try {
      if (i = xi(s), !i) return;
    } catch (d) {
      console.error("Failed to parse color:", d);
      return;
    }
    const a = Mo(i), l = Ci(a.l), u = _i(i, 25), f = Ai(u);
    return {
      mainColor: s,
      mainColorRgb: hr(i),
      onMainColor: l,
      hoverColor: f,
      hoverColorRgb: hr(u),
      onHoverColor: l
    };
  });
  return { isUtilityColor: n, style: r };
}
function Si(t7) {
  return t7.startsWith("#") || t7.startsWith("rgb") || t7.startsWith("hsl");
}
var qi = ["id", "disabled"];
var Ri = {
  key: 0,
  class: "q-button__spinner"
};
var Ti = { class: "q-button__content" };
var Fi = defineComponent({
  __name: "QButton",
  props: {
    id: {},
    class: {},
    label: {},
    variant: { default: "outlined" },
    color: { default: "primary" },
    disabled: { type: Boolean },
    borderless: { type: Boolean },
    elevated: { type: Boolean },
    block: { type: Boolean },
    loading: { type: Boolean },
    size: { default: "regular" },
    iconPos: { default: "start" },
    pill: { type: Boolean }
  },
  emits: ["click"],
  setup(t7, { emit: e }) {
    const n = t7, r = e, o = computed(() => n.disabled || n.loading);
    function s(u) {
      o.value || r("click", u);
    }
    const { isUtilityColor: c, style: i } = jo(toRef(n, "color")), a = computed(() => {
      const u = c.value ? `q-button--${n.color}` : void 0, f = n.size !== "regular" ? `q-button--${n.size}` : void 0, d = n.iconPos !== "start" ? `q-button--icon-${n.iconPos}` : void 0;
      return [
        "q-button",
        `q-button--${n.variant}`,
        u,
        f,
        d,
        {
          "q-button--borderless": n.borderless,
          "q-button--elevated": n.elevated,
          "q-button--block": n.block,
          "q-button--loading": n.loading,
          "q-button--pill": n.pill
        },
        n.class
      ];
    }), l = computed(() => {
      var u, f, d, h7, p, g;
      if (!c.value)
        return {
          "--q-button-main-color": (u = i.value) == null ? void 0 : u.mainColor,
          "--q-button-main-color-rgb": (f = i.value) == null ? void 0 : f.mainColorRgb,
          "--q-button-on-main-color": (d = i.value) == null ? void 0 : d.onMainColor,
          "--q-button-hover-color": (h7 = i.value) == null ? void 0 : h7.hoverColor,
          "--q-button-hover-color-rgb": (p = i.value) == null ? void 0 : p.hoverColorRgb,
          "--q-button-on-hover-color": (g = i.value) == null ? void 0 : g.onHoverColor
        };
    });
    return (u, f) => (openBlock(), createElementBlock("button", {
      id: n.id,
      type: "button",
      class: normalizeClass(a.value),
      disabled: o.value,
      style: normalizeStyle(l.value),
      onClick: withModifiers(s, ["stop", "prevent"])
    }, [
      n.loading ? (openBlock(), createElementBlock("div", Ri, [
        createVNode(unref(yi))
      ])) : createCommentVNode("v-if", true),
      createBaseVNode("span", Ti, [
        renderSlot(u.$slots, "default"),
        createTextVNode(
          " " + toDisplayString(n.label),
          1
          /* TEXT */
        )
      ])
    ], 14, qi));
  }
});
var ye = ce(Fi);
var Li = defineComponent({
  __name: "QIcon",
  props: {
    id: {},
    class: {},
    icon: {},
    type: { default: "svg" },
    size: {},
    color: {}
  },
  setup(t7) {
    const e = t7, n = computed(() => {
      switch (e.type) {
        case "svg":
          return Gi;
        case "font":
          return ji;
        case "img":
          return Vi;
        default:
          return;
      }
    }), { style: r } = jo(toRef(e, "color")), o = computed(() => {
      var s;
      return {
        "font-size": e.size !== void 0 ? `${e.size}px` : void 0,
        color: e.color !== void 0 && e.color !== "primary" ? (s = r.value) == null ? void 0 : s.mainColor : void 0
      };
    });
    return (s, c) => (openBlock(), createBlock(resolveDynamicComponent(n.value), {
      id: e.id,
      class: normalizeClass(e.class),
      icon: e.icon,
      style: normalizeStyle(o.value)
    }, null, 8, ["id", "class", "icon", "style"]));
  }
});
var Ni = ["id"];
var Ii = defineComponent({
  __name: "QIconFont",
  props: {
    id: {},
    class: {},
    icon: {},
    library: { default: "" },
    variant: {}
  },
  setup(t7) {
    const e = t7, n = computed(() => e.variant ? `${e.library}-${e.variant}` : e.library), r = computed(() => e.library && e.icon ? `${e.library}-${e.icon}` : e.icon);
    return (o, s) => (openBlock(), createElementBlock("i", {
      id: e.id,
      class: normalizeClass(["q-icon", "q-icon__font", n.value, r.value, e.class])
    }, null, 10, Ni));
  }
});
var Bi = ["id", "src"];
var Oi = defineComponent({
  __name: "QIconImg",
  props: {
    id: {},
    class: {},
    icon: {}
  },
  setup(t7) {
    const e = t7;
    return (n, r) => (openBlock(), createElementBlock("img", {
      id: e.id,
      src: e.icon,
      class: normalizeClass(["q-icon", "q-icon__img", e.class])
    }, null, 10, Bi));
  }
});
var et = {};
var Pi = defineComponent({
  name: "InlineSvg",
  inheritAttrs: false,
  props: {
    /**
     * The source URL of the SVG bundle.
     */
    src: {
      type: String,
      required: true
    },
    /**
     * The ID of the SVG symbol to be rendered.
     */
    symbol: {
      type: String,
      default: ""
    },
    /**
     * The title to be associated with the SVG.
     */
    title: {
      type: String,
      default: ""
    },
    /**
     * A function to transform the source SVG element before rendering.
     * If not provided, no transformation will be applied.
     */
    transformSource: {
      type: Function,
      default: void 0
    },
    /**
     * Determines whether to keep the existing SVG content visible during the loading of a new SVG.
     * Set to `false` to hide content during loading.
     */
    keepDuringLoading: {
      type: Boolean,
      default: true
    }
  },
  emits: {
    loaded: (t7) => typeof t7 == "object",
    unloaded: () => true,
    error: (t7) => typeof t7 == "object"
  },
  data() {
    return {
      /** @type SVGElement */
      svgElSource: null
    };
  },
  watch: {
    src(t7) {
      this.getSource(t7);
    }
  },
  async mounted() {
    await this.getSource(this.src);
  },
  methods: {
    copySvgAttrs(t7, e) {
      const n = e.attributes;
      if (n) for (const r of n) t7[r.name] = r.value;
    },
    copyComponentAttrs(t7, e) {
      for (const [n, r] of Object.entries(e))
        r !== false && r !== null && r !== void 0 && (t7[n] = r);
    },
    getSvgContent(t7) {
      return this.symbol && (t7 = t7.getElementById(this.symbol), !t7) ? null : (this.transformSource && (t7 = t7.cloneNode(true), t7 = this.transformSource(t7)), this.title && (this.transformSource || (t7 = t7.cloneNode(true)), zi(t7, this.title)), t7);
    },
    /**
     * Get svgElSource
     * @param {string} src
     */
    async getSource(t7) {
      try {
        et[t7] || (et[t7] = Mi(this.download(t7))), this.svgElSource && et[t7].getIsPending() && !this.keepDuringLoading && (this.svgElSource = null, this.$emit("unloaded"));
        const e = await et[t7];
        this.svgElSource = e, await this.$nextTick(), this.$emit("loaded", this.$el);
      } catch (e) {
        this.svgElSource && (this.svgElSource = null, this.$emit("unloaded")), delete et[t7], this.$emit("error", e);
      }
    },
    /**
     * Get the contents of the SVG
     * @param {string} url
     * @returns {PromiseWithState<Element>}
     */
    async download(t7) {
      const e = await fetch(t7);
      if (!e.ok) throw new Error("Error loading SVG");
      const n = await e.text(), r = new DOMParser().parseFromString(n, "text/xml").getElementsByTagName("svg")[0];
      if (!r) throw new Error("Loaded file is not a valid SVG");
      return r;
    }
  },
  render() {
    if (!this.svgElSource) return null;
    const t7 = this.getSvgContent(this.svgElSource);
    if (!t7) return h("div", this.$attrs);
    const e = {};
    return this.copySvgAttrs(e, this.svgElSource), this.copySvgAttrs(e, t7), this.copyComponentAttrs(e, this.$attrs), e.innerHTML = t7.innerHTML, h("svg", e);
  },
  expose: []
});
function zi(t7, e) {
  const n = t7.getElementsByTagName("title");
  if (n.length)
    n[0].textContent = e;
  else {
    const r = document.createElementNS("http://www.w3.org/2000/svg", "title");
    r.textContent = e, t7.insertBefore(r, t7.firstChild);
  }
}
function Mi(t7) {
  if (t7.getIsPending) return t7;
  let e = true;
  const n = t7.then(
    (r) => (e = false, r),
    (r) => {
      throw e = false, r;
    }
  );
  return n.getIsPending = () => e, n;
}
var Ui = defineComponent({
  __name: "QIconSvg",
  props: {
    id: {},
    class: {},
    icon: {},
    bundle: { default: "" }
  },
  emits: ["loaded", "unloaded"],
  setup(t7, { emit: e }) {
    const n = t7, r = e;
    function o(c) {
      r("loaded", c);
    }
    function s() {
      r("unloaded");
    }
    return (c, i) => (openBlock(), createBlock(unref(Pi), {
      id: n.id,
      class: normalizeClass(["q-icon", "q-icon__svg", n.class]),
      src: n.bundle,
      symbol: n.icon,
      onLoaded: o,
      onUnloaded: s
    }, null, 8, ["id", "class", "src", "symbol"]));
  }
});
var de = ce(Li);
var ji = ce(Ii);
var Vi = ce(Oi);
var Gi = ce(Ui);
var $i = 0;
function Vo(t7) {
  return unref(t7) || `uid-${++$i}`;
}
var Hi = ["id"];
var Zi = ["for"];
var Wi = defineComponent({
  inheritAttrs: false,
  __name: "QLabel",
  props: {
    id: {},
    class: {},
    label: {},
    for: {},
    required: { type: Boolean }
  },
  setup(t7) {
    const e = t7, n = computed(() => [
      "q-label",
      {
        "q-label--required": e.required
      },
      e.class
    ]);
    return (r, o) => (openBlock(), createElementBlock("div", {
      id: e.id,
      class: normalizeClass(n.value)
    }, [
      renderSlot(r.$slots, "prepend"),
      createBaseVNode("label", mergeProps({
        for: e.for
      }, r.$attrs), [
        renderSlot(r.$slots, "default", {}, () => [
          createTextVNode(
            toDisplayString(e.label),
            1
            /* TEXT */
          )
        ])
      ], 16, Zi),
      renderSlot(r.$slots, "append")
    ], 10, Hi));
  }
});
var Go = ce(Wi);
var Ji = ["id"];
var Qi = {
  key: 0,
  class: "q-field__prepend"
};
var Yi = {
  key: 1,
  class: "q-field__append"
};
var Xi = {
  key: 1,
  class: "q-field__extras"
};
var Ki = defineComponent({
  inheritAttrs: false,
  __name: "QField",
  props: {
    id: {},
    class: {},
    label: {},
    for: {},
    required: { type: Boolean },
    size: { default: "medium" },
    readonly: { type: Boolean },
    disabled: { type: Boolean },
    invalid: { type: Boolean }
  },
  setup(t7, { expose: e }) {
    const n = t7, r = ref(null), o = computed(() => n.required && !n.readonly && !n.disabled), s = computed(() => [
      "q-field",
      `q-field--${n.size}`,
      {
        "q-field--readonly": n.readonly,
        "q-field--disabled": n.disabled,
        "q-field--required": o.value,
        "q-field--invalid": n.invalid
      },
      n.class
    ]);
    return e({
      fieldRef: r
    }), (c, i) => (openBlock(), createElementBlock("div", {
      id: n.id,
      class: normalizeClass(s.value)
    }, [
      n.label ? (openBlock(), createBlock(unref(Go), {
        key: 0,
        class: "q-field__label",
        label: n.label,
        for: n.for,
        required: n.required
      }, {
        prepend: withCtx(() => [
          renderSlot(c.$slots, "label.prepend")
        ]),
        append: withCtx(() => [
          renderSlot(c.$slots, "label.append")
        ]),
        _: 3
        /* FORWARDED */
      }, 8, ["label", "for", "required"])) : createCommentVNode("v-if", true),
      renderSlot(c.$slots, "control", {}, () => [
        createBaseVNode(
          "div",
          mergeProps({
            ref_key: "fieldRef",
            ref: r,
            class: "q-field__control"
          }, c.$attrs),
          [
            c.$slots.prepend ? (openBlock(), createElementBlock("div", Qi, [
              renderSlot(c.$slots, "prepend")
            ])) : createCommentVNode("v-if", true),
            renderSlot(c.$slots, "default"),
            c.$slots.append ? (openBlock(), createElementBlock("div", Yi, [
              renderSlot(c.$slots, "append")
            ])) : createCommentVNode("v-if", true)
          ],
          16
          /* FULL_PROPS */
        )
      ]),
      c.$slots.extras ? (openBlock(), createElementBlock("div", Xi, [
        renderSlot(c.$slots, "extras")
      ])) : createCommentVNode("v-if", true)
    ], 10, Ji));
  }
});
var $o = ce(Ki);
var ea = {
  clearValue: "Clear value"
};
var ta = {
  clear: {
    icon: "close"
  }
};
var na = defineComponent({
  __name: "QClearButton",
  props: {
    id: {},
    class: {},
    icons: { default: () => ta },
    texts: { default: () => ea }
  },
  emits: ["click"],
  setup(t7, { emit: e }) {
    const n = t7, r = e;
    function o(s) {
      r("click", s);
    }
    return (s, c) => (openBlock(), createBlock(unref(ye), {
      id: n.id,
      class: normalizeClass(["q-clear-btn", n.class]),
      "aria-label": n.texts.clearValue,
      variant: "ghost",
      color: "neutral",
      borderless: "",
      tabindex: "-1",
      onClick: o
    }, {
      default: withCtx(() => [
        createVNode(
          unref(de),
          normalizeProps(guardReactiveProps(n.icons.clear)),
          null,
          16
          /* FULL_PROPS */
        )
      ]),
      _: 1
      /* STABLE */
    }, 8, ["id", "class", "aria-label"]));
  }
});
var ra = ce(na);
var oa = ["id", "type", "maxlength", "required", "placeholder", "readonly", "disabled"];
var sa = defineComponent({
  inheritAttrs: false,
  __name: "QTextField",
  props: mergeModels({
    id: {},
    class: {},
    label: {},
    for: {},
    required: { type: Boolean },
    size: {},
    readonly: { type: Boolean },
    disabled: { type: Boolean },
    invalid: { type: Boolean },
    placeholder: {},
    maxLength: {},
    clearable: { type: Boolean },
    type: { default: "text" },
    texts: {}
  }, {
    modelValue: {},
    modelModifiers: {}
  }),
  emits: mergeModels(["click:clear"], ["update:modelValue"]),
  setup(t7, { expose: e, emit: n }) {
    const r = t7, o = n, s = useModel(t7, "modelValue"), c = computed({
      get: () => s.value,
      set: (p) => {
        const g = r.maxLength;
        s.value = g ? p == null ? void 0 : p.slice(0, g) : p;
      }
    });
    watch(s, (p) => c.value = p, { immediate: true });
    const i = Vo(r.id), a = ref(null), l = ref(null), u = computed(
      () => r.readonly || r.disabled ? void 0 : r.placeholder
    ), f = computed(() => r.clearable && !r.readonly && !r.disabled), d = computed(() => f.value && s.value);
    function h7(p) {
      f.value && (s.value = "", o("click:clear", p));
    }
    return e({
      fieldRef: computed(() => {
        var p;
        return (p = a.value) == null ? void 0 : p.fieldRef;
      }),
      inputRef: l
    }), (p, g) => (openBlock(), createBlock(unref($o), {
      ref_key: "fieldRef",
      ref: a,
      class: normalizeClass(["q-text-field", r.class]),
      for: unref(i),
      label: r.label,
      size: r.size,
      readonly: r.readonly,
      disabled: r.disabled,
      required: r.required,
      invalid: r.invalid
    }, createSlots({
      "label.prepend": withCtx(() => [
        renderSlot(p.$slots, "label.prepend")
      ]),
      "label.append": withCtx(() => [
        renderSlot(p.$slots, "label.append")
      ]),
      default: withCtx(() => [
        withDirectives(createBaseVNode("input", mergeProps({
          id: unref(i),
          ref_key: "inputRef",
          ref: l,
          "onUpdate:modelValue": g[0] || (g[0] = (m) => c.value = m),
          class: "q-text-field__input",
          type: r.type,
          maxlength: r.maxLength,
          required: r.required,
          placeholder: u.value,
          readonly: r.readonly,
          disabled: r.disabled
        }, p.$attrs), null, 16, oa), [
          [vModelDynamic, c.value]
        ])
      ]),
      _: 2
      /* DYNAMIC */
    }, [
      p.$slots.prepend ? {
        name: "prepend",
        fn: withCtx(() => [
          renderSlot(p.$slots, "prepend")
        ]),
        key: "0"
      } : void 0,
      p.$slots.append || d.value ? {
        name: "append",
        fn: withCtx(() => [
          renderSlot(p.$slots, "append"),
          d.value ? (openBlock(), createBlock(unref(ra), {
            key: 0,
            "data-testid": "q-text-field__clear-button",
            texts: r.texts,
            onClick: h7
          }, null, 8, ["texts"])) : createCommentVNode("v-if", true)
        ]),
        key: "1"
      } : void 0,
      p.$slots.extras ? {
        name: "extras",
        fn: withCtx(() => [
          renderSlot(p.$slots, "extras")
        ]),
        key: "2"
      } : void 0
    ]), 1032, ["class", "for", "label", "size", "readonly", "disabled", "required", "invalid"]));
  }
});
var ca = ce(sa);
function Ho(t7) {
  return getCurrentScope() ? (onScopeDispose(t7), true) : false;
}
var Zo = typeof window < "u" && typeof document < "u";
typeof WorkerGlobalScope < "u" && globalThis instanceof WorkerGlobalScope;
var ia = Object.prototype.toString;
var aa = (t7) => ia.call(t7) === "[object Object]";
var Fe = () => {
};
var la = ua();
function ua() {
  var t7, e;
  return Zo && ((t7 = window == null ? void 0 : window.navigator) == null ? void 0 : t7.userAgent) && (/iP(?:ad|hone|od)/.test(window.navigator.userAgent) || ((e = window == null ? void 0 : window.navigator) == null ? void 0 : e.maxTouchPoints) > 2 && /iPad|Macintosh/.test(window == null ? void 0 : window.navigator.userAgent));
}
function mr(...t7) {
  if (t7.length !== 1)
    return toRef(...t7);
  const e = t7[0];
  return typeof e == "function" ? readonly(customRef(() => ({ get: e, set: Fe }))) : ref(e);
}
function rn(t7) {
  return Array.isArray(t7) ? t7 : [t7];
}
function fa(t7, e, n) {
  return watch(
    t7,
    e,
    {
      ...n,
      immediate: true
    }
  );
}
var dt = Zo ? window : void 0;
function qe(t7) {
  var e;
  const n = toValue(t7);
  return (e = n == null ? void 0 : n.$el) != null ? e : n;
}
function tt(...t7) {
  const e = [], n = () => {
    e.forEach((i) => i()), e.length = 0;
  }, r = (i, a, l, u) => (i.addEventListener(a, l, u), () => i.removeEventListener(a, l, u)), o = computed(() => {
    const i = rn(toValue(t7[0])).filter((a) => a != null);
    return i.every((a) => typeof a != "string") ? i : void 0;
  }), s = fa(
    () => {
      var i, a;
      return [
        (a = (i = o.value) == null ? void 0 : i.map((l) => qe(l))) != null ? a : [dt].filter((l) => l != null),
        rn(toValue(o.value ? t7[1] : t7[0])),
        rn(unref(o.value ? t7[2] : t7[1])),
        // @ts-expect-error - TypeScript gets the correct types, but somehow still complains
        toValue(o.value ? t7[3] : t7[2])
      ];
    },
    ([i, a, l, u]) => {
      if (n(), !(i != null && i.length) || !(a != null && a.length) || !(l != null && l.length))
        return;
      const f = aa(u) ? { ...u } : u;
      e.push(
        ...i.flatMap(
          (d) => a.flatMap(
            (h7) => l.map((p) => r(d, h7, p, f))
          )
        )
      );
    },
    { flush: "post" }
  ), c = () => {
    s(), n();
  };
  return Ho(n), c;
}
var gr = false;
function da(t7, e, n = {}) {
  const { window: r = dt, ignore: o = [], capture: s = true, detectIframe: c = false, controls: i = false } = n;
  if (!r)
    return i ? { stop: Fe, cancel: Fe, trigger: Fe } : Fe;
  if (la && !gr) {
    gr = true;
    const m = { passive: true };
    Array.from(r.document.body.children).forEach((v) => tt(v, "click", Fe, m)), tt(r.document.documentElement, "click", Fe, m);
  }
  let a = true;
  const l = (m) => toValue(o).some((v) => {
    if (typeof v == "string")
      return Array.from(r.document.querySelectorAll(v)).some((y) => y === m.target || m.composedPath().includes(y));
    {
      const y = qe(v);
      return y && (m.target === y || m.composedPath().includes(y));
    }
  });
  function u(m) {
    const v = toValue(m);
    return v && v.$.subTree.shapeFlag === 16;
  }
  function f(m, v) {
    const y = toValue(m), E = y.$.subTree && y.$.subTree.children;
    return E == null || !Array.isArray(E) ? false : E.some((D) => D.el === v.target || v.composedPath().includes(D.el));
  }
  const d = (m) => {
    const v = qe(t7);
    if (m.target != null && !(!(v instanceof Element) && u(t7) && f(t7, m)) && !(!v || v === m.target || m.composedPath().includes(v))) {
      if ("detail" in m && m.detail === 0 && (a = !l(m)), !a) {
        a = true;
        return;
      }
      e(m);
    }
  };
  let h7 = false;
  const p = [
    tt(r, "click", (m) => {
      h7 || (h7 = true, setTimeout(() => {
        h7 = false;
      }, 0), d(m));
    }, { passive: true, capture: s }),
    tt(r, "pointerdown", (m) => {
      const v = qe(t7);
      a = !l(m) && !!(v && !m.composedPath().includes(v));
    }, { passive: true }),
    c && tt(r, "blur", (m) => {
      setTimeout(() => {
        var v;
        const y = qe(t7);
        ((v = r.document.activeElement) == null ? void 0 : v.tagName) === "IFRAME" && !(y != null && y.contains(r.document.activeElement)) && e(m);
      }, 0);
    }, { passive: true })
  ].filter(Boolean), g = () => p.forEach((m) => m());
  return i ? {
    stop: g,
    cancel: () => {
      a = false;
    },
    trigger: (m) => {
      a = true, d(m), a = false;
    }
  } : g;
}
function pa() {
  const t7 = shallowRef(false), e = getCurrentInstance();
  return e && onMounted(() => {
    t7.value = true;
  }, e), t7;
}
function ha(t7) {
  const e = pa();
  return computed(() => (e.value, !!t7()));
}
function ma(t7, e, n = {}) {
  const { window: r = dt, ...o } = n;
  let s;
  const c = ha(() => r && "ResizeObserver" in r), i = () => {
    s && (s.disconnect(), s = void 0);
  }, a = computed(() => {
    const f = toValue(t7);
    return Array.isArray(f) ? f.map((d) => qe(d)) : [qe(f)];
  }), l = watch(
    a,
    (f) => {
      if (i(), c.value && r) {
        s = new ResizeObserver(e);
        for (const d of f)
          d && s.observe(d, o);
      }
    },
    { immediate: true, flush: "post" }
  ), u = () => {
    i(), l();
  };
  return Ho(u), {
    isSupported: c,
    stop: u
  };
}
function ga(t7 = dt, e) {
  t7 && typeof t7.requestAnimationFrame == "function" ? t7.requestAnimationFrame(e) : e();
}
function ba(t7 = {}) {
  var e, n;
  const { window: r = dt } = t7, o = mr(t7 == null ? void 0 : t7.element), s = mr((e = t7 == null ? void 0 : t7.input) != null ? e : ""), c = (n = t7 == null ? void 0 : t7.styleProp) != null ? n : "height", i = shallowRef(1), a = shallowRef(0);
  function l() {
    var u;
    if (!o.value)
      return;
    let f = "";
    o.value.style[c] = "1px", i.value = (u = o.value) == null ? void 0 : u.scrollHeight;
    const d = toValue(t7 == null ? void 0 : t7.styleTarget);
    d ? d.style[c] = `${i.value}px` : f = `${i.value}px`, o.value.style[c] = f;
  }
  return watch([s, o], () => nextTick(l), { immediate: true }), watch(i, () => {
    var u;
    return (u = t7 == null ? void 0 : t7.onResize) == null ? void 0 : u.call(t7);
  }), ma(o, ([{ contentRect: u }]) => {
    a.value !== u.width && ga(r, () => {
      a.value = u.width, l();
    });
  }), t7 != null && t7.watch && watch(t7.watch, l, { immediate: true, deep: true }), {
    textarea: o,
    input: s,
    triggerResize: l
  };
}
var va = defineComponent({
  __name: "QDismissibleLayer",
  emits: ["escape-key-down", "pointer-down-outside", "focus-outside", "interact-outside", "dismiss"],
  setup(t7, { emit: e }) {
    const n = e, r = ref(null);
    function o(c) {
      c.key === "Escape" && (n("escape-key-down", c), c.defaultPrevented || n("dismiss", c));
    }
    function s(c) {
      if (!r.value) return;
      const i = c.target;
      if (!r.value.contains(i)) {
        const a = new CustomEvent("focus-outside", {
          bubbles: false,
          cancelable: true,
          detail: { originalEvent: c }
        });
        n("focus-outside", a), n("interact-outside", a), a.defaultPrevented || n("dismiss", a);
      }
    }
    return onMounted(() => {
      document.addEventListener("keydown", o), document.addEventListener("focusin", s), da(r, (c) => {
        const i = new CustomEvent("pointer-down-outside", {
          bubbles: false,
          cancelable: true,
          detail: { originalEvent: c }
        });
        n("pointer-down-outside", i), n("interact-outside", i), i.defaultPrevented || n("dismiss", i);
      });
    }), onUnmounted(() => {
      document.removeEventListener("keydown", o), document.removeEventListener("focusin", s);
    }), (c, i) => (openBlock(), createElementBlock(
      "div",
      {
        ref_key: "layerRef",
        ref: r
      },
      [
        renderSlot(c.$slots, "default")
      ],
      512
      /* NEED_PATCH */
    ));
  }
});
var ya = ce(va);
var Wo = ["input:not([inert])", "select:not([inert])", "textarea:not([inert])", "a[href]:not([inert])", "button:not([inert])", "[tabindex]:not(slot):not([inert])", "audio[controls]:not([inert])", "video[controls]:not([inert])", '[contenteditable]:not([contenteditable="false"]):not([inert])', "details>summary:first-of-type:not([inert])", "details:not([inert])"];
var St = Wo.join(",");
var Jo = typeof Element > "u";
var Me = Jo ? function() {
} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
var qt = !Jo && Element.prototype.getRootNode ? function(t7) {
  var e;
  return t7 == null || (e = t7.getRootNode) === null || e === void 0 ? void 0 : e.call(t7);
} : function(t7) {
  return t7 == null ? void 0 : t7.ownerDocument;
};
var Rt = function t(e, n) {
  var r;
  n === void 0 && (n = true);
  var o = e == null || (r = e.getAttribute) === null || r === void 0 ? void 0 : r.call(e, "inert"), s = o === "" || o === "true", c = s || n && e && t(e.parentNode);
  return c;
};
var wa = function(t7) {
  var e, n = t7 == null || (e = t7.getAttribute) === null || e === void 0 ? void 0 : e.call(t7, "contenteditable");
  return n === "" || n === "true";
};
var Qo = function(t7, e, n) {
  if (Rt(t7))
    return [];
  var r = Array.prototype.slice.apply(t7.querySelectorAll(St));
  return e && Me.call(t7, St) && r.unshift(t7), r = r.filter(n), r;
};
var Yo = function t2(e, n, r) {
  for (var o = [], s = Array.from(e); s.length; ) {
    var c = s.shift();
    if (!Rt(c, false))
      if (c.tagName === "SLOT") {
        var i = c.assignedElements(), a = i.length ? i : c.children, l = t2(a, true, r);
        r.flatten ? o.push.apply(o, l) : o.push({
          scopeParent: c,
          candidates: l
        });
      } else {
        var u = Me.call(c, St);
        u && r.filter(c) && (n || !e.includes(c)) && o.push(c);
        var f = c.shadowRoot || // check for an undisclosed shadow
        typeof r.getShadowRoot == "function" && r.getShadowRoot(c), d = !Rt(f, false) && (!r.shadowRootFilter || r.shadowRootFilter(c));
        if (f && d) {
          var h7 = t2(f === true ? c.children : f.children, true, r);
          r.flatten ? o.push.apply(o, h7) : o.push({
            scopeParent: c,
            candidates: h7
          });
        } else
          s.unshift.apply(s, c.children);
      }
  }
  return o;
};
var Xo = function(t7) {
  return !isNaN(parseInt(t7.getAttribute("tabindex"), 10));
};
var Le = function(t7) {
  if (!t7)
    throw new Error("No node provided");
  return t7.tabIndex < 0 && (/^(AUDIO|VIDEO|DETAILS)$/.test(t7.tagName) || wa(t7)) && !Xo(t7) ? 0 : t7.tabIndex;
};
var ka = function(t7, e) {
  var n = Le(t7);
  return n < 0 && e && !Xo(t7) ? 0 : n;
};
var xa = function(t7, e) {
  return t7.tabIndex === e.tabIndex ? t7.documentOrder - e.documentOrder : t7.tabIndex - e.tabIndex;
};
var Ko = function(t7) {
  return t7.tagName === "INPUT";
};
var _a = function(t7) {
  return Ko(t7) && t7.type === "hidden";
};
var Aa = function(t7) {
  var e = t7.tagName === "DETAILS" && Array.prototype.slice.apply(t7.children).some(function(n) {
    return n.tagName === "SUMMARY";
  });
  return e;
};
var Ea = function(t7, e) {
  for (var n = 0; n < t7.length; n++)
    if (t7[n].checked && t7[n].form === e)
      return t7[n];
};
var Ca = function(t7) {
  if (!t7.name)
    return true;
  var e = t7.form || qt(t7), n = function(s) {
    return e.querySelectorAll('input[type="radio"][name="' + s + '"]');
  }, r;
  if (typeof window < "u" && typeof window.CSS < "u" && typeof window.CSS.escape == "function")
    r = n(window.CSS.escape(t7.name));
  else
    try {
      r = n(t7.name);
    } catch (s) {
      return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s", s.message), false;
    }
  var o = Ea(r, t7.form);
  return !o || o === t7;
};
var Da = function(t7) {
  return Ko(t7) && t7.type === "radio";
};
var Sa = function(t7) {
  return Da(t7) && !Ca(t7);
};
var qa = function(t7) {
  var e, n = t7 && qt(t7), r = (e = n) === null || e === void 0 ? void 0 : e.host, o = false;
  if (n && n !== t7) {
    var s, c, i;
    for (o = !!((s = r) !== null && s !== void 0 && (c = s.ownerDocument) !== null && c !== void 0 && c.contains(r) || t7 != null && (i = t7.ownerDocument) !== null && i !== void 0 && i.contains(t7)); !o && r; ) {
      var a, l, u;
      n = qt(r), r = (a = n) === null || a === void 0 ? void 0 : a.host, o = !!((l = r) !== null && l !== void 0 && (u = l.ownerDocument) !== null && u !== void 0 && u.contains(r));
    }
  }
  return o;
};
var br = function(t7) {
  var e = t7.getBoundingClientRect(), n = e.width, r = e.height;
  return n === 0 && r === 0;
};
var Ra = function(t7, e) {
  var n = e.displayCheck, r = e.getShadowRoot;
  if (getComputedStyle(t7).visibility === "hidden")
    return true;
  var o = Me.call(t7, "details>summary:first-of-type"), s = o ? t7.parentElement : t7;
  if (Me.call(s, "details:not([open]) *"))
    return true;
  if (!n || n === "full" || n === "legacy-full") {
    if (typeof r == "function") {
      for (var c = t7; t7; ) {
        var i = t7.parentElement, a = qt(t7);
        if (i && !i.shadowRoot && r(i) === true)
          return br(t7);
        t7.assignedSlot ? t7 = t7.assignedSlot : !i && a !== t7.ownerDocument ? t7 = a.host : t7 = i;
      }
      t7 = c;
    }
    if (qa(t7))
      return !t7.getClientRects().length;
    if (n !== "legacy-full")
      return true;
  } else if (n === "non-zero-area")
    return br(t7);
  return false;
};
var Ta = function(t7) {
  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t7.tagName))
    for (var e = t7.parentElement; e; ) {
      if (e.tagName === "FIELDSET" && e.disabled) {
        for (var n = 0; n < e.children.length; n++) {
          var r = e.children.item(n);
          if (r.tagName === "LEGEND")
            return Me.call(e, "fieldset[disabled] *") ? true : !r.contains(t7);
        }
        return true;
      }
      e = e.parentElement;
    }
  return false;
};
var Tt = function(t7, e) {
  return !(e.disabled || // we must do an inert look up to filter out any elements inside an inert ancestor
  //  because we're limited in the type of selectors we can use in JSDom (see related
  //  note related to `candidateSelectors`)
  Rt(e) || _a(e) || Ra(e, t7) || // For a details element with a summary, the summary element gets the focus
  Aa(e) || Ta(e));
};
var Fn = function(t7, e) {
  return !(Sa(e) || Le(e) < 0 || !Tt(t7, e));
};
var Fa = function(t7) {
  var e = parseInt(t7.getAttribute("tabindex"), 10);
  return !!(isNaN(e) || e >= 0);
};
var La = function t3(e) {
  var n = [], r = [];
  return e.forEach(function(o, s) {
    var c = !!o.scopeParent, i = c ? o.scopeParent : o, a = ka(i, c), l = c ? t3(o.candidates) : i;
    a === 0 ? c ? n.push.apply(n, l) : n.push(i) : r.push({
      documentOrder: s,
      tabIndex: a,
      item: o,
      isScope: c,
      content: l
    });
  }), r.sort(xa).reduce(function(o, s) {
    return s.isScope ? o.push.apply(o, s.content) : o.push(s.content), o;
  }, []).concat(n);
};
var Na = function(t7, e) {
  e = e || {};
  var n;
  return e.getShadowRoot ? n = Yo([t7], e.includeContainer, {
    filter: Fn.bind(null, e),
    flatten: false,
    getShadowRoot: e.getShadowRoot,
    shadowRootFilter: Fa
  }) : n = Qo(t7, e.includeContainer, Fn.bind(null, e)), La(n);
};
var Ia = function(t7, e) {
  e = e || {};
  var n;
  return e.getShadowRoot ? n = Yo([t7], e.includeContainer, {
    filter: Tt.bind(null, e),
    flatten: true,
    getShadowRoot: e.getShadowRoot
  }) : n = Qo(t7, e.includeContainer, Tt.bind(null, e)), n;
};
var je = function(t7, e) {
  if (e = e || {}, !t7)
    throw new Error("No node provided");
  return Me.call(t7, St) === false ? false : Fn(e, t7);
};
var Ba = Wo.concat("iframe").join(",");
var on = function(t7, e) {
  if (e = e || {}, !t7)
    throw new Error("No node provided");
  return Me.call(t7, Ba) === false ? false : Tt(e, t7);
};
function Ln(t7, e) {
  (e == null || e > t7.length) && (e = t7.length);
  for (var n = 0, r = Array(e); n < e; n++) r[n] = t7[n];
  return r;
}
function Oa(t7) {
  if (Array.isArray(t7)) return Ln(t7);
}
function Pa(t7, e, n) {
  return (e = Va(e)) in t7 ? Object.defineProperty(t7, e, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : t7[e] = n, t7;
}
function za(t7) {
  if (typeof Symbol < "u" && t7[Symbol.iterator] != null || t7["@@iterator"] != null) return Array.from(t7);
}
function Ma() {
  throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
}
function vr(t7, e) {
  var n = Object.keys(t7);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(t7);
    e && (r = r.filter(function(o) {
      return Object.getOwnPropertyDescriptor(t7, o).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function yr(t7) {
  for (var e = 1; e < arguments.length; e++) {
    var n = arguments[e] != null ? arguments[e] : {};
    e % 2 ? vr(Object(n), true).forEach(function(r) {
      Pa(t7, r, n[r]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t7, Object.getOwnPropertyDescriptors(n)) : vr(Object(n)).forEach(function(r) {
      Object.defineProperty(t7, r, Object.getOwnPropertyDescriptor(n, r));
    });
  }
  return t7;
}
function Ua(t7) {
  return Oa(t7) || za(t7) || Ga(t7) || Ma();
}
function ja(t7, e) {
  if (typeof t7 != "object" || !t7) return t7;
  var n = t7[Symbol.toPrimitive];
  if (n !== void 0) {
    var r = n.call(t7, e);
    if (typeof r != "object") return r;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (e === "string" ? String : Number)(t7);
}
function Va(t7) {
  var e = ja(t7, "string");
  return typeof e == "symbol" ? e : e + "";
}
function Ga(t7, e) {
  if (t7) {
    if (typeof t7 == "string") return Ln(t7, e);
    var n = {}.toString.call(t7).slice(8, -1);
    return n === "Object" && t7.constructor && (n = t7.constructor.name), n === "Map" || n === "Set" ? Array.from(t7) : n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? Ln(t7, e) : void 0;
  }
}
var wr = {
  activateTrap: function(t7, e) {
    if (t7.length > 0) {
      var n = t7[t7.length - 1];
      n !== e && n._setPausedState(true);
    }
    var r = t7.indexOf(e);
    r === -1 || t7.splice(r, 1), t7.push(e);
  },
  deactivateTrap: function(t7, e) {
    var n = t7.indexOf(e);
    n !== -1 && t7.splice(n, 1), t7.length > 0 && !t7[t7.length - 1]._isManuallyPaused() && t7[t7.length - 1]._setPausedState(false);
  }
};
var $a = function(t7) {
  return t7.tagName && t7.tagName.toLowerCase() === "input" && typeof t7.select == "function";
};
var Ha = function(t7) {
  return (t7 == null ? void 0 : t7.key) === "Escape" || (t7 == null ? void 0 : t7.key) === "Esc" || (t7 == null ? void 0 : t7.keyCode) === 27;
};
var rt = function(t7) {
  return (t7 == null ? void 0 : t7.key) === "Tab" || (t7 == null ? void 0 : t7.keyCode) === 9;
};
var Za = function(t7) {
  return rt(t7) && !t7.shiftKey;
};
var Wa = function(t7) {
  return rt(t7) && t7.shiftKey;
};
var kr = function(t7) {
  return setTimeout(t7, 0);
};
var nt = function(t7) {
  for (var e = arguments.length, n = new Array(e > 1 ? e - 1 : 0), r = 1; r < e; r++)
    n[r - 1] = arguments[r];
  return typeof t7 == "function" ? t7.apply(void 0, n) : t7;
};
var gt = function(t7) {
  return t7.target.shadowRoot && typeof t7.composedPath == "function" ? t7.composedPath()[0] : t7.target;
};
var Ja = [];
var Qa = function(t7, e) {
  var n = (e == null ? void 0 : e.document) || document, r = (e == null ? void 0 : e.trapStack) || Ja, o = yr({
    returnFocusOnDeactivate: true,
    escapeDeactivates: true,
    delayInitialFocus: true,
    isKeyForward: Za,
    isKeyBackward: Wa
  }, e), s = {
    // containers given to createFocusTrap()
    // @type {Array<HTMLElement>}
    containers: [],
    // list of objects identifying tabbable nodes in `containers` in the trap
    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap
    //  is active, but the trap should never get to a state where there isn't at least one group
    //  with at least one tabbable node in it (that would lead to an error condition that would
    //  result in an error being thrown)
    // @type {Array<{
    //   container: HTMLElement,
    //   tabbableNodes: Array<HTMLElement>, // empty if none
    //   focusableNodes: Array<HTMLElement>, // empty if none
    //   posTabIndexesFound: boolean,
    //   firstTabbableNode: HTMLElement|undefined,
    //   lastTabbableNode: HTMLElement|undefined,
    //   firstDomTabbableNode: HTMLElement|undefined,
    //   lastDomTabbableNode: HTMLElement|undefined,
    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined
    // }>}
    containerGroups: [],
    // same order/length as `containers` list
    // references to objects in `containerGroups`, but only those that actually have
    //  tabbable nodes in them
    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__
    //  the same length
    tabbableGroups: [],
    nodeFocusedBeforeActivation: null,
    mostRecentlyFocusedNode: null,
    active: false,
    paused: false,
    manuallyPaused: false,
    // timer ID for when delayInitialFocus is true and initial focus in this trap
    //  has been delayed during activation
    delayInitialFocusTimer: void 0,
    // the most recent KeyboardEvent for the configured nav key (typically [SHIFT+]TAB), if any
    recentNavEvent: void 0
  }, c, i = function(w, x, R) {
    return w && w[x] !== void 0 ? w[x] : o[R || x];
  }, a = function(w, x) {
    var R = typeof (x == null ? void 0 : x.composedPath) == "function" ? x.composedPath() : void 0;
    return s.containerGroups.findIndex(function(k) {
      var C = k.container, A = k.tabbableNodes;
      return C.contains(w) || // fall back to explicit tabbable search which will take into consideration any
      //  web components if the `tabbableOptions.getShadowRoot` option was used for
      //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't
      //  look inside web components even if open)
      (R == null ? void 0 : R.includes(C)) || A.find(function(N) {
        return N === w;
      });
    });
  }, l = function(w) {
    var x = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, R = x.hasFallback, k = R === void 0 ? false : R, C = x.params, A = C === void 0 ? [] : C, N = o[w];
    if (typeof N == "function" && (N = N.apply(void 0, Ua(A))), N === true && (N = void 0), !N) {
      if (N === void 0 || N === false)
        return N;
      throw new Error("`".concat(w, "` was specified but was not a node, or did not return a node"));
    }
    var J = N;
    if (typeof N == "string") {
      try {
        J = n.querySelector(N);
      } catch (H) {
        throw new Error("`".concat(w, '` appears to be an invalid selector; error="').concat(H.message, '"'));
      }
      if (!J && !k)
        throw new Error("`".concat(w, "` as selector refers to no known node"));
    }
    return J;
  }, u = function() {
    var w = l("initialFocus", {
      hasFallback: true
    });
    if (w === false)
      return false;
    if (w === void 0 || w && !on(w, o.tabbableOptions))
      if (a(n.activeElement) >= 0)
        w = n.activeElement;
      else {
        var x = s.tabbableGroups[0], R = x && x.firstTabbableNode;
        w = R || l("fallbackFocus");
      }
    else w === null && (w = l("fallbackFocus"));
    if (!w)
      throw new Error("Your focus-trap needs to have at least one focusable element");
    return w;
  }, f = function() {
    if (s.containerGroups = s.containers.map(function(w) {
      var x = Na(w, o.tabbableOptions), R = Ia(w, o.tabbableOptions), k = x.length > 0 ? x[0] : void 0, C = x.length > 0 ? x[x.length - 1] : void 0, A = R.find(function(H) {
        return je(H);
      }), N = R.slice().reverse().find(function(H) {
        return je(H);
      }), J = !!x.find(function(H) {
        return Le(H) > 0;
      });
      return {
        container: w,
        tabbableNodes: x,
        focusableNodes: R,
        /** True if at least one node with positive `tabindex` was found in this container. */
        posTabIndexesFound: J,
        /** First tabbable node in container, __tabindex__ order; `undefined` if none. */
        firstTabbableNode: k,
        /** Last tabbable node in container, __tabindex__ order; `undefined` if none. */
        lastTabbableNode: C,
        // NOTE: DOM order is NOT NECESSARILY "document position" order, but figuring that out
        //  would require more than just https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition
        //  because that API doesn't work with Shadow DOM as well as it should (@see
        //  https://github.com/whatwg/dom/issues/320) and since this first/last is only needed, so far,
        //  to address an edge case related to positive tabindex support, this seems like a much easier,
        //  "close enough most of the time" alternative for positive tabindexes which should generally
        //  be avoided anyway...
        /** First tabbable node in container, __DOM__ order; `undefined` if none. */
        firstDomTabbableNode: A,
        /** Last tabbable node in container, __DOM__ order; `undefined` if none. */
        lastDomTabbableNode: N,
        /**
         * Finds the __tabbable__ node that follows the given node in the specified direction,
         *  in this container, if any.
         * @param {HTMLElement} node
         * @param {boolean} [forward] True if going in forward tab order; false if going
         *  in reverse.
         * @returns {HTMLElement|undefined} The next tabbable node, if any.
         */
        nextTabbableNode: function(H) {
          var q = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true, z = x.indexOf(H);
          return z < 0 ? q ? R.slice(R.indexOf(H) + 1).find(function(M) {
            return je(M);
          }) : R.slice(0, R.indexOf(H)).reverse().find(function(M) {
            return je(M);
          }) : x[z + (q ? 1 : -1)];
        }
      };
    }), s.tabbableGroups = s.containerGroups.filter(function(w) {
      return w.tabbableNodes.length > 0;
    }), s.tabbableGroups.length <= 0 && !l("fallbackFocus"))
      throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");
    if (s.containerGroups.find(function(w) {
      return w.posTabIndexesFound;
    }) && s.containerGroups.length > 1)
      throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.");
  }, d = function(w) {
    var x = w.activeElement;
    if (x)
      return x.shadowRoot && x.shadowRoot.activeElement !== null ? d(x.shadowRoot) : x;
  }, h7 = function(w) {
    if (w !== false && w !== d(document)) {
      if (!w || !w.focus) {
        h7(u());
        return;
      }
      w.focus({
        preventScroll: !!o.preventScroll
      }), s.mostRecentlyFocusedNode = w, $a(w) && w.select();
    }
  }, p = function(w) {
    var x = l("setReturnFocus", {
      params: [w]
    });
    return x || (x === false ? false : w);
  }, g = function(w) {
    var x = w.target, R = w.event, k = w.isBackward, C = k === void 0 ? false : k;
    x = x || gt(R), f();
    var A = null;
    if (s.tabbableGroups.length > 0) {
      var N = a(x, R), J = N >= 0 ? s.containerGroups[N] : void 0;
      if (N < 0)
        C ? A = s.tabbableGroups[s.tabbableGroups.length - 1].lastTabbableNode : A = s.tabbableGroups[0].firstTabbableNode;
      else if (C) {
        var H = s.tabbableGroups.findIndex(function(he) {
          var Re = he.firstTabbableNode;
          return x === Re;
        });
        if (H < 0 && (J.container === x || on(x, o.tabbableOptions) && !je(x, o.tabbableOptions) && !J.nextTabbableNode(x, false)) && (H = N), H >= 0) {
          var q = H === 0 ? s.tabbableGroups.length - 1 : H - 1, z = s.tabbableGroups[q];
          A = Le(x) >= 0 ? z.lastTabbableNode : z.lastDomTabbableNode;
        } else rt(R) || (A = J.nextTabbableNode(x, false));
      } else {
        var M = s.tabbableGroups.findIndex(function(he) {
          var Re = he.lastTabbableNode;
          return x === Re;
        });
        if (M < 0 && (J.container === x || on(x, o.tabbableOptions) && !je(x, o.tabbableOptions) && !J.nextTabbableNode(x)) && (M = N), M >= 0) {
          var ue = M === s.tabbableGroups.length - 1 ? 0 : M + 1, ee = s.tabbableGroups[ue];
          A = Le(x) >= 0 ? ee.firstTabbableNode : ee.firstDomTabbableNode;
        } else rt(R) || (A = J.nextTabbableNode(x));
      }
    } else
      A = l("fallbackFocus");
    return A;
  }, m = function(w) {
    var x = gt(w);
    if (!(a(x, w) >= 0)) {
      if (nt(o.clickOutsideDeactivates, w)) {
        c.deactivate({
          // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,
          //  which will result in the outside click setting focus to the node
          //  that was clicked (and if not focusable, to "nothing"); by setting
          //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused
          //  on activation (or the configured `setReturnFocus` node), whether the
          //  outside click was on a focusable node or not
          returnFocus: o.returnFocusOnDeactivate
        });
        return;
      }
      nt(o.allowOutsideClick, w) || w.preventDefault();
    }
  }, v = function(w) {
    var x = gt(w), R = a(x, w) >= 0;
    if (R || x instanceof Document)
      R && (s.mostRecentlyFocusedNode = x);
    else {
      w.stopImmediatePropagation();
      var k, C = true;
      if (s.mostRecentlyFocusedNode)
        if (Le(s.mostRecentlyFocusedNode) > 0) {
          var A = a(s.mostRecentlyFocusedNode), N = s.containerGroups[A].tabbableNodes;
          if (N.length > 0) {
            var J = N.findIndex(function(H) {
              return H === s.mostRecentlyFocusedNode;
            });
            J >= 0 && (o.isKeyForward(s.recentNavEvent) ? J + 1 < N.length && (k = N[J + 1], C = false) : J - 1 >= 0 && (k = N[J - 1], C = false));
          }
        } else
          s.containerGroups.some(function(H) {
            return H.tabbableNodes.some(function(q) {
              return Le(q) > 0;
            });
          }) || (C = false);
      else
        C = false;
      C && (k = g({
        // move FROM the MRU node, not event-related node (which will be the node that is
        //  outside the trap causing the focus escape we're trying to fix)
        target: s.mostRecentlyFocusedNode,
        isBackward: o.isKeyBackward(s.recentNavEvent)
      })), h7(k || s.mostRecentlyFocusedNode || u());
    }
    s.recentNavEvent = void 0;
  }, y = function(w) {
    var x = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
    s.recentNavEvent = w;
    var R = g({
      event: w,
      isBackward: x
    });
    R && (rt(w) && w.preventDefault(), h7(R));
  }, E = function(w) {
    (o.isKeyForward(w) || o.isKeyBackward(w)) && y(w, o.isKeyBackward(w));
  }, D = function(w) {
    Ha(w) && nt(o.escapeDeactivates, w) !== false && (w.preventDefault(), c.deactivate());
  }, S = function(w) {
    var x = gt(w);
    a(x, w) >= 0 || nt(o.clickOutsideDeactivates, w) || nt(o.allowOutsideClick, w) || (w.preventDefault(), w.stopImmediatePropagation());
  }, _ = function() {
    if (s.active)
      return wr.activateTrap(r, c), s.delayInitialFocusTimer = o.delayInitialFocus ? kr(function() {
        h7(u());
      }) : h7(u()), n.addEventListener("focusin", v, true), n.addEventListener("mousedown", m, {
        capture: true,
        passive: false
      }), n.addEventListener("touchstart", m, {
        capture: true,
        passive: false
      }), n.addEventListener("click", S, {
        capture: true,
        passive: false
      }), n.addEventListener("keydown", E, {
        capture: true,
        passive: false
      }), n.addEventListener("keydown", D), c;
  }, T = function() {
    if (s.active)
      return n.removeEventListener("focusin", v, true), n.removeEventListener("mousedown", m, true), n.removeEventListener("touchstart", m, true), n.removeEventListener("click", S, true), n.removeEventListener("keydown", E, true), n.removeEventListener("keydown", D), c;
  }, U = function(w) {
    var x = w.some(function(R) {
      var k = Array.from(R.removedNodes);
      return k.some(function(C) {
        return C === s.mostRecentlyFocusedNode;
      });
    });
    x && h7(u());
  }, K = typeof window < "u" && "MutationObserver" in window ? new MutationObserver(U) : void 0, P = function() {
    K && (K.disconnect(), s.active && !s.paused && s.containers.map(function(w) {
      K.observe(w, {
        subtree: true,
        childList: true
      });
    }));
  };
  return c = {
    get active() {
      return s.active;
    },
    get paused() {
      return s.paused;
    },
    activate: function(w) {
      if (s.active)
        return this;
      var x = i(w, "onActivate"), R = i(w, "onPostActivate"), k = i(w, "checkCanFocusTrap");
      k || f(), s.active = true, s.paused = false, s.nodeFocusedBeforeActivation = n.activeElement, x == null || x();
      var C = function() {
        k && f(), _(), P(), R == null || R();
      };
      return k ? (k(s.containers.concat()).then(C, C), this) : (C(), this);
    },
    deactivate: function(w) {
      if (!s.active)
        return this;
      var x = yr({
        onDeactivate: o.onDeactivate,
        onPostDeactivate: o.onPostDeactivate,
        checkCanReturnFocus: o.checkCanReturnFocus
      }, w);
      clearTimeout(s.delayInitialFocusTimer), s.delayInitialFocusTimer = void 0, T(), s.active = false, s.paused = false, P(), wr.deactivateTrap(r, c);
      var R = i(x, "onDeactivate"), k = i(x, "onPostDeactivate"), C = i(x, "checkCanReturnFocus"), A = i(x, "returnFocus", "returnFocusOnDeactivate");
      R == null || R();
      var N = function() {
        kr(function() {
          A && h7(p(s.nodeFocusedBeforeActivation)), k == null || k();
        });
      };
      return A && C ? (C(p(s.nodeFocusedBeforeActivation)).then(N, N), this) : (N(), this);
    },
    pause: function(w) {
      return s.active ? (s.manuallyPaused = true, this._setPausedState(true, w)) : this;
    },
    unpause: function(w) {
      return s.active ? (s.manuallyPaused = false, r[r.length - 1] !== this ? this : this._setPausedState(false, w)) : this;
    },
    updateContainerElements: function(w) {
      var x = [].concat(w).filter(Boolean);
      return s.containers = x.map(function(R) {
        return typeof R == "string" ? n.querySelector(R) : R;
      }), s.active && f(), P(), this;
    }
  }, Object.defineProperties(c, {
    _isManuallyPaused: {
      value: function() {
        return s.manuallyPaused;
      }
    },
    _setPausedState: {
      value: function(w, x) {
        if (s.paused === w)
          return this;
        if (s.paused = w, w) {
          var R = i(x, "onPause"), k = i(x, "onPostPause");
          R == null || R(), T(), P(), k == null || k();
        } else {
          var C = i(x, "onUnpause"), A = i(x, "onPostUnpause");
          C == null || C(), f(), _(), P(), A == null || A();
        }
        return this;
      }
    }
  }), c.updateContainerElements(t7), c;
};
var Ya = defineComponent({
  name: "UseFocusTrap",
  props: ["as", "options"],
  setup(t7, { slots: e }) {
    let n;
    const r = ref(), o = () => n && n.activate(), s = () => n && n.deactivate();
    return watch(
      () => qe(r),
      (c) => {
        c && (n = Qa(c, t7.options || {}), o());
      },
      { flush: "post" }
    ), onScopeDispose(() => s()), () => {
      if (e.default)
        return h(t7.as || "div", { ref: r }, e.default());
    };
  }
});
var Xa = defineComponent({
  __name: "QFocusTrap",
  props: mergeModels({
    options: {},
    as: {}
  }, {
    disabled: { type: Boolean },
    disabledModifiers: {}
  }),
  emits: ["update:disabled"],
  setup(t7) {
    const e = t7, n = useModel(t7, "disabled");
    return (r, o) => n.value ? renderSlot(r.$slots, "default", { key: 0 }) : (openBlock(), createBlock(
      unref(Ya),
      normalizeProps(mergeProps({ key: 1 }, e)),
      {
        default: withCtx(() => [
          renderSlot(r.$slots, "default")
        ]),
        _: 3
        /* FORWARDED */
      },
      16
      /* FULL_PROPS */
    ));
  }
});
var Ka = ce(Xa);
var Ve = "data-scroll-locked";
var sn = "right-scroll-bar-position";
var cn = "width-before-scroll-bar";
var el = "with-scroll-bars-hidden";
var tl = "--removed-body-scroll-bar-size";
var nl = {
  left: 0,
  top: 0,
  right: 0,
  gap: 0
};
var an = (t7) => parseInt(t7 || "", 10) || 0;
var rl = (t7) => {
  const e = window.getComputedStyle(document.body), n = e[t7 === "padding" ? "paddingLeft" : "marginLeft"], r = e[t7 === "padding" ? "paddingTop" : "marginTop"], o = e[t7 === "padding" ? "paddingRight" : "marginRight"];
  return [an(n), an(r), an(o)];
};
var ol = (t7 = "margin") => {
  if (typeof window > "u")
    return nl;
  const e = rl(t7), n = document.documentElement.clientWidth, r = window.innerWidth;
  return {
    left: e[0],
    top: e[1],
    right: e[2],
    gap: Math.max(0, r - n + e[2] - e[0])
  };
};
function sl() {
  return document ? document.createElement("style") : null;
}
function cl(t7, e) {
  t7.appendChild(document.createTextNode(e));
}
function il(t7) {
  (document.head || document.getElementsByTagName("head")[0]).appendChild(t7);
}
var al = () => {
  let t7 = 0, e = null;
  return {
    add: (n) => {
      t7 === 0 && (e = sl()) && (cl(e, n), il(e)), t7++;
    },
    remove: () => {
      var n;
      t7--, !t7 && e && ((n = e.parentNode) == null || n.removeChild(e), e = null);
    }
  };
};
var ll = defineComponent({
  __name: "QRemoveScrollBar",
  props: {
    noRelative: { type: Boolean },
    noImportant: { type: Boolean },
    gapMode: { default: "margin" }
  },
  setup(t7) {
    const e = t7, n = al(), r = () => {
      const s = parseInt(document.body.getAttribute(Ve) || "0", 10);
      return isFinite(s) ? s : 0;
    }, o = ({ left: s, top: c, right: i, gap: a }, l, u = "margin", f) => `
  .${el} {
    overflow: hidden ${f};
    padding-right: ${a}px ${f};
  }
  body[${Ve}] {
    overflow: hidden ${f};
    overscroll-behavior: contain;
    ${[
      l && `position: relative ${f};`,
      u === "margin" && `
    padding-left: ${s}px;
    padding-top: ${c}px;
    padding-right: ${i}px;
    margin-left:0;
    margin-top:0;
    margin-right: ${a}px ${f};
    `,
      u === "padding" && `padding-right: ${a}px ${f};`
    ].filter(Boolean).join("")}
  }

  .${sn} {
    right: ${a}px ${f};
  }

  .${cn} {
    margin-right: ${a}px ${f};
  }

  .${sn} .${sn} {
    right: 0 ${f};
  }

  .${cn} .${cn} {
    margin-right: 0 ${f};
  }

  body[${Ve}] {
    ${tl}: ${a}px;
  }
`;
    return onMounted(() => {
      document.body.setAttribute(Ve, (r() + 1).toString());
      const s = ol(e.gapMode);
      n.add(
        o(s, !e.noRelative, e.gapMode, e.noImportant ? "" : "!important")
      );
    }), onUnmounted(() => {
      const s = r() - 1;
      s <= 0 ? document.body.removeAttribute(Ve) : document.body.setAttribute(Ve, s.toString()), n.remove();
    }), (s, c) => renderSlot(s.$slots, "default");
  }
});
var ul = ce(ll);
function fl(t7, e, n, r) {
  const o = (r == null ? void 0 : r.offset) ?? 0, s = (r == null ? void 0 : r.crossOffset) ?? 0, c = (r == null ? void 0 : r.placement) ?? "bottom", i = t7.getBoundingClientRect(), a = e.getBoundingClientRect(), l = n == null ? void 0 : n.getBoundingClientRect(), u = dl(i, a, c, o), f = bl(i, a, u), d = hl(f, u, o), h7 = ml(d, u, s), p = pl(h7, a, u);
  let g;
  if (r != null && r.arrow) {
    if (!l)
      throw new Error("[Quidgest UI] The arrow element must exist to compute its position");
    g = gl(p, i, a, l, u);
  }
  return { overlayCoords: p, arrowCoords: g, placement: u };
}
function dl(t7, e, n, r) {
  const o = xr(t7, e, n, r);
  if (_r(o)) {
    const s = Je(n), c = {
      top: "bottom",
      bottom: "top",
      left: "right",
      right: "left"
    }[s], i = xr(
      t7,
      e,
      c,
      r
    );
    if (_r(i))
      return n;
    const a = wl(n);
    return a ? `${c}-${a}` : c;
  }
  return n;
}
function pl(t7, e, n) {
  const r = Ft(n), o = (c, i, a) => Math.min(Math.max(c, i), a), s = { ...t7 };
  return r === "x" ? s.x = o(t7.x, 8, window.innerWidth - e.width - 8) : s.y = o(t7.y, 8, window.innerHeight - e.height - 8), s;
}
function hl(t7, e, n) {
  const r = Je(e);
  return {
    x: t7.x + (r === "left" ? -n : r === "right" ? n : 0),
    y: t7.y + (r === "top" ? -n : r === "bottom" ? n : 0)
  };
}
function ml(t7, e, n) {
  const r = Je(e);
  return {
    x: t7.x + (r === "top" || r === "bottom" ? n : 0),
    y: t7.y + (r === "right" || r === "left" ? n : 0)
  };
}
function gl(t7, e, n, r, o) {
  if (Ft(o) === "y") {
    const s = e.top + e.height / 2, c = t7.y + n.height / 2, i = c - s, a = n.height / 2 - i - r.height / 2 + 2;
    if (a > 0 && a < n.height - r.height)
      return {
        y: a
      };
  } else {
    const s = e.left + e.width / 2, c = t7.x + n.width / 2, i = c - s, a = n.width / 2 - i - r.width / 2 + 2;
    if (a > 0 && a < n.width - r.width)
      return {
        x: a
      };
  }
}
function bl(t7, e, n) {
  const [r, o] = n.split("-"), s = t7.x + t7.width / 2 - e.width / 2, c = t7.y + t7.height / 2 - e.height / 2;
  let i;
  switch (r) {
    case "top":
      i = { x: s, y: t7.y - e.height };
      break;
    case "bottom":
      i = { x: s, y: t7.y + t7.height };
      break;
    case "left":
      i = { x: t7.x - e.width, y: c };
      break;
    case "right":
      i = { x: t7.x + t7.width, y: c };
      break;
    default:
      throw new Error(`[Quidgest UI] Invalid placement value: ${n}`);
  }
  if (!o)
    return i;
  const a = Ft(n) === "y" ? "height" : "width", l = Ft(n), u = t7[a] / 2 - e[a] / 2;
  switch (o) {
    case "start":
      i[l] -= u;
      break;
    case "end":
      i[l] += u;
      break;
    default:
      throw new Error(`[Quidgest UI] Invalid alignment value: ${o}`);
  }
  return i;
}
function xr(t7, e, n, r) {
  const o = Je(n);
  let s = 0;
  switch (o) {
    case "top":
      s = t7.top - (e.height + r);
      break;
    case "bottom":
      s = window.innerHeight - t7.bottom - (e.height + r);
      break;
    case "left":
      s = t7.left - (e.width + r);
      break;
    case "right":
      s = window.innerWidth - t7.right - (e.width + r);
      break;
    default:
      throw new Error(`[Quidgest UI] Invalid side value: ${o}`);
  }
  return s;
}
function vl(t7) {
  return t7 === "x" ? "y" : "x";
}
function yl(t7) {
  const e = Je(t7);
  return ["left", "right"].includes(e) ? "x" : "y";
}
function Ft(t7) {
  return vl(yl(t7));
}
function Je(t7) {
  const [e] = t7.split("-");
  return e;
}
function wl(t7) {
  const [, e] = t7.split("-");
  return e;
}
function _r(t7) {
  return t7 < 0;
}
function kl(t7) {
  return typeof t7 == "string" ? document.querySelector(t7) : t7;
}
var xl = ["id"];
var _l = ["role"];
var Al = defineComponent({
  inheritAttrs: false,
  __name: "QOverlay",
  props: mergeModels({
    id: {},
    class: {},
    anchor: {},
    appearance: { default: "regular" },
    arrow: { type: Boolean },
    attach: { default: "body" },
    inline: { type: Boolean },
    backdropBlur: { type: Boolean },
    backdropClass: {},
    delay: { default: 500 },
    nonModal: { type: Boolean },
    scrollLock: { type: Boolean },
    offset: { default: 8 },
    crossOffset: { default: 0 },
    persistent: { type: Boolean },
    placement: { default: "right" },
    spy: { type: Boolean },
    transition: { default: "fade" },
    trigger: { default: "click" },
    closeOnContentClick: { type: Boolean },
    width: { default: "auto" },
    focusTrap: { type: Boolean }
  }, {
    modelValue: { type: Boolean },
    modelModifiers: {}
  }),
  emits: mergeModels(["enter", "leave"], ["update:modelValue"]),
  setup(t7, { emit: e }) {
    const n = t7, r = e, o = useModel(t7, "modelValue"), { class: s } = Uo(), c = computed(() => [
      "q-overlay",
      `q-overlay--${Je(f.placement)}`,
      s.value,
      {
        "q-overlay--independent": i.value,
        "q-overlay--inverted": n.appearance === "inverted"
      },
      n.class
    ]), i = computed(() => n.anchor === void 0), a = computed(() => o.value && !n.nonModal), l = computed(() => (o.value || f.animating) && n.scrollLock), u = computed(() => !n.inline && (o.value || f.animating)), f = reactive({
      animating: false,
      top: 0,
      left: 0,
      width: 0,
      placement: n.placement
    }), d = reactive({
      top: void 0,
      left: void 0,
      hidden: true
    });
    watch(o, R);
    const h7 = computed(() => {
      if (i.value) return;
      const I = C(), Ae = n.width === "anchor", ie = Ae ? I == null ? void 0 : I.getBoundingClientRect().width : void 0;
      return {
        top: `${f.top}px`,
        left: `${f.left}px`,
        width: ie !== void 0 ? `${ie}px` : void 0
      };
    }), p = computed(() => {
      if (n.arrow)
        return {
          top: d.top !== void 0 ? `${d.top}px` : void 0,
          left: d.left !== void 0 ? `${d.left}px` : void 0,
          opacity: d.hidden ? 0 : 1
        };
    }), g = ref(null), m = ref(null), v = ref(null);
    function y() {
      const I = C();
      if (!I || !m.value)
        return;
      const Ae = {
        placement: n.placement,
        width: n.width,
        offset: n.offset,
        crossOffset: n.crossOffset,
        arrow: n.arrow
      }, ie = fl(
        I,
        m.value,
        (g == null ? void 0 : g.value) ?? void 0,
        Ae
      );
      f.left = ie.overlayCoords.x, f.top = ie.overlayCoords.y, f.placement = ie.placement, d.hidden = ie.arrowCoords === void 0, ie.arrowCoords && (d.top = ie.arrowCoords.y, d.left = ie.arrowCoords.x);
    }
    watch(
      () => n.placement,
      () => nextTick(y)
    );
    let E;
    function D() {
      _(0);
    }
    function S() {
      _(n.delay);
    }
    function _(I) {
      E && U(), E = window.setTimeout(() => {
        o.value = true;
      }, I);
    }
    function T() {
      if (E && U(), o.value = false, n.anchor && n.trigger === "click") {
        const I = C();
        nextTick(() => I == null ? void 0 : I.focus());
      }
    }
    function U() {
      clearTimeout(E), E = void 0;
    }
    function K() {
      nextTick(Re), r("enter");
    }
    function P() {
      k();
    }
    function w() {
      r("leave");
    }
    function x() {
      k(), Yt();
    }
    function R() {
      f.animating = true;
    }
    function k() {
      f.animating = false;
    }
    function C() {
      return n.anchor ? kl(n.anchor) : null;
    }
    function A(I) {
      n.persistent && I.preventDefault();
    }
    function N(I) {
      var Ae;
      if (n.persistent) {
        I.preventDefault();
        return;
      }
      let ie = false;
      I.defaultPrevented || I.detail.originalEvent.type === "pointerdown" && (ie = true);
      const mt = I.detail.originalEvent.target;
      if ((Ae = C()) != null && Ae.contains(mt)) {
        I.preventDefault();
        return;
      }
      I.detail.originalEvent.type === "focusin" && ie && I.preventDefault();
    }
    function J() {
      T();
    }
    let H;
    function q() {
      const I = C();
      if (I)
        switch (H = new MutationObserver(y), H.observe(I, {
          attributes: false,
          childList: true,
          characterData: true,
          subtree: true
        }), n.trigger) {
          case "click":
            I.addEventListener("click", D);
            break;
          case "hover":
            I.addEventListener("mouseenter", S), I.addEventListener("mouseleave", T), I.addEventListener("focusin", D), I.addEventListener("focusout", T);
            break;
        }
    }
    function z() {
      const I = C();
      if (I)
        switch (H == null || H.disconnect(), n.trigger) {
          case "click":
            I.removeEventListener("click", D);
            break;
          case "hover":
            I.removeEventListener("mouseenter", S), I.removeEventListener("mouseleave", T), I.removeEventListener("focusin", D), I.removeEventListener("focusout", T);
            break;
        }
    }
    function M() {
      window.addEventListener("resize", y), n.scrollLock || window.addEventListener("scroll", y);
    }
    function ue() {
      window.removeEventListener("resize", y), n.scrollLock || window.removeEventListener("scroll", y);
    }
    let ee;
    function he() {
      C() ? (y(), ee = window.setTimeout(he, 100)) : T();
    }
    function Re() {
      var I;
      i.value || (M(), n.spy ? he() : y()), (i.value || n.trigger === "click") && ((I = m.value) == null || I.focus());
    }
    function Yt() {
      i.value || ue(), n.spy && (clearTimeout(ee), ee = void 0);
    }
    return onMounted(() => {
      nextTick(q);
    }), onBeforeUnmount(z), (I, Ae) => (openBlock(), createElementBlock(
      Fragment,
      null,
      [
        (openBlock(), createBlock(Teleport, {
          disabled: !u.value,
          to: n.attach
        }, [
          createVNode(Transition, {
            name: "fade",
            appear: ""
          }, {
            default: withCtx(() => [
              a.value ? (openBlock(), createElementBlock(
                "div",
                {
                  key: 0,
                  class: normalizeClass([
                    n.backdropClass,
                    "q-overlay__underlay",
                    { "q-overlay__underlay--blur": n.backdropBlur }
                  ])
                },
                null,
                2
                /* CLASS */
              )) : createCommentVNode("v-if", true)
            ]),
            _: 1
            /* STABLE */
          }),
          createVNode(Transition, {
            name: n.transition,
            appear: "",
            onEnter: K,
            onAfterEnter: P,
            onLeave: w,
            onAfterLeave: x
          }, {
            default: withCtx(() => [
              o.value ? (openBlock(), createElementBlock("div", {
                key: 0,
                id: n.id,
                ref_key: "overlayContainerRef",
                ref: v,
                tabindex: "-1",
                class: normalizeClass(c.value),
                style: normalizeStyle(h7.value)
              }, [
                createVNode(unref(ya), {
                  onDismiss: J,
                  onInteractOutside: N,
                  onPointerDownOutside: A
                }, {
                  default: withCtx(() => [
                    v.value ? (openBlock(), createBlock(unref(Ka), {
                      key: 0,
                      disabled: n.nonModal || !o.value,
                      options: { fallbackFocus: v.value }
                    }, {
                      default: withCtx(() => [
                        createBaseVNode("div", mergeProps({
                          ref_key: "overlayRef",
                          ref: m,
                          class: "q-overlay__content",
                          tabindex: "-1",
                          role: a.value ? "dialog" : void 0
                        }, I.$attrs), [
                          n.arrow ? (openBlock(), createElementBlock(
                            "div",
                            {
                              key: 0,
                              ref_key: "arrowRef",
                              ref: g,
                              role: "presentation",
                              class: "q-overlay__arrow",
                              style: normalizeStyle(p.value)
                            },
                            null,
                            4
                            /* STYLE */
                          )) : createCommentVNode("v-if", true),
                          renderSlot(I.$slots, "default")
                        ], 16, _l)
                      ]),
                      _: 3
                      /* FORWARDED */
                    }, 8, ["disabled", "options"])) : createCommentVNode("v-if", true)
                  ]),
                  _: 3
                  /* FORWARDED */
                })
              ], 14, xl)) : createCommentVNode("v-if", true)
            ]),
            _: 3
            /* FORWARDED */
          }, 8, ["name"])
        ], 8, ["disabled", "to"])),
        l.value ? (openBlock(), createBlock(unref(ul), { key: 0 })) : createCommentVNode("v-if", true)
      ],
      64
      /* STABLE_FRAGMENT */
    ));
  }
});
var El = ce(Al);
var Cl = [
  {
    id: "button",
    icon: {
      icon: "check"
    },
    props: {
      variant: "bold",
      label: "Ok"
    }
  }
];
var Dl = {
  icon: "check-circle-outline"
};
var Sl = {
  close: {
    icon: "close"
  }
};
var ql = { class: "q-dialog__header" };
var Rl = { class: "q-dialog__header-title" };
var Tl = { class: "q-dialog__body" };
var Fl = {
  key: 0,
  class: "q-dialog__body-icon"
};
var Ll = { class: "q-dialog__body-text" };
var Nl = ["innerHTML"];
var Il = { key: 1 };
var Bl = { class: "q-dialog__body-actions" };
var Ol = defineComponent({
  __name: "QDialog",
  props: mergeModels({
    id: {},
    class: {},
    title: {},
    text: {},
    icon: { default: () => Dl },
    buttons: { default: () => Cl },
    attach: { default: "body" },
    inline: { type: Boolean },
    backdropBlur: { type: Boolean, default: true },
    dismissible: { type: Boolean },
    html: { type: Boolean, default: true },
    icons: { default: () => Sl }
  }, {
    modelValue: { type: Boolean },
    modelModifiers: {}
  }),
  emits: ["update:modelValue"],
  setup(t7) {
    const e = t7, n = useModel(t7, "modelValue");
    function r() {
      n.value = false;
    }
    function o(s) {
      var c;
      (c = s.action) == null || c.call(s), r();
    }
    return (s, c) => (openBlock(), createBlock(unref(El), {
      id: e.id,
      modelValue: n.value,
      "onUpdate:modelValue": c[0] || (c[0] = (i) => n.value = i),
      "backdrop-class": "q-dialog__underlay",
      "scroll-lock": "",
      persistent: "",
      inline: e.inline,
      attach: e.attach,
      "backdrop-blur": e.backdropBlur
    }, {
      default: withCtx(() => [
        createBaseVNode(
          "div",
          {
            class: normalizeClass(["q-dialog", e.class])
          },
          [
            createBaseVNode("div", ql, [
              createBaseVNode(
                "span",
                Rl,
                toDisplayString(e.title),
                1
                /* TEXT */
              ),
              e.dismissible ? (openBlock(), createBlock(unref(ye), {
                key: 0,
                variant: "text",
                onClick: r
              }, {
                default: withCtx(() => [
                  createVNode(
                    unref(de),
                    normalizeProps(guardReactiveProps(e.icons.close)),
                    null,
                    16
                    /* FULL_PROPS */
                  )
                ]),
                _: 1
                /* STABLE */
              })) : createCommentVNode("v-if", true)
            ]),
            renderSlot(s.$slots, "body.content", {}, () => [
              createBaseVNode("div", Tl, [
                renderSlot(s.$slots, "body.icon", {}, () => [
                  e.icon ? (openBlock(), createElementBlock("span", Fl, [
                    createVNode(
                      unref(de),
                      normalizeProps(guardReactiveProps(e.icon)),
                      null,
                      16
                      /* FULL_PROPS */
                    )
                  ])) : createCommentVNode("v-if", true)
                ]),
                createBaseVNode("div", Ll, [
                  e.html ? (openBlock(), createElementBlock("span", {
                    key: 0,
                    innerHTML: e.text
                  }, null, 8, Nl)) : (openBlock(), createElementBlock(
                    "span",
                    Il,
                    toDisplayString(e.text),
                    1
                    /* TEXT */
                  ))
                ]),
                renderSlot(s.$slots, "body.append")
              ])
            ]),
            createBaseVNode("div", Bl, [
              (openBlock(true), createElementBlock(
                Fragment,
                null,
                renderList(e.buttons, (i) => (openBlock(), createBlock(unref(ye), mergeProps({ ref_for: true }, i.props, {
                  key: i.id,
                  onClick: () => o(i)
                }), {
                  default: withCtx(() => [
                    i.icon ? (openBlock(), createBlock(
                      unref(de),
                      mergeProps({
                        key: 0,
                        ref_for: true
                      }, i.icon),
                      null,
                      16
                      /* FULL_PROPS */
                    )) : createCommentVNode("v-if", true)
                  ]),
                  _: 2
                  /* DYNAMIC */
                }, 1040, ["onClick"]))),
                128
                /* KEYED_FRAGMENT */
              ))
            ])
          ],
          2
          /* CLASS */
        )
      ]),
      _: 3
      /* FORWARDED */
    }, 8, ["id", "modelValue", "inline", "attach", "backdrop-blur"]));
  }
});
var Pl = ce(Ol);
var zl = ["id", "required", "placeholder", "readonly", "disabled", "maxlength", "rows", "cols", "resize", "wrap"];
var Ml = defineComponent({
  inheritAttrs: false,
  __name: "QTextArea",
  props: mergeModels({
    id: {},
    class: {},
    label: {},
    for: {},
    required: { type: Boolean },
    size: {},
    readonly: { type: Boolean },
    disabled: { type: Boolean },
    invalid: { type: Boolean },
    placeholder: {},
    maxLength: {},
    rows: { default: 3 },
    cols: {},
    resize: { default: "vertical" },
    wrap: { default: "soft" },
    autosize: { type: Boolean }
  }, {
    modelValue: {},
    modelModifiers: {}
  }),
  emits: ["update:modelValue"],
  setup(t7) {
    const e = t7, n = useModel(t7, "modelValue"), r = computed({
      get: () => n.value,
      set: (l) => {
        const u = e.maxLength;
        n.value = u ? l == null ? void 0 : l.slice(0, u) : l;
      }
    });
    watch(n, (l) => r.value = l, { immediate: true });
    const o = Vo(e.id), s = computed(
      () => e.readonly || e.disabled ? void 0 : e.placeholder
    ), c = computed(() => ({ input: e.autosize ? a : null })), i = ref(), { triggerResize: a } = ba({
      element: computed(() => e.autosize ? i.value : void 0),
      styleProp: "minHeight"
    });
    return (l, u) => (openBlock(), createBlock(unref($o), {
      class: normalizeClass(["q-text-area", e.class]),
      for: unref(o),
      label: e.label,
      size: e.size,
      readonly: e.readonly,
      disabled: e.disabled,
      required: e.required
    }, createSlots({
      "label.prepend": withCtx(() => [
        renderSlot(l.$slots, "label.prepend")
      ]),
      "label.append": withCtx(() => [
        renderSlot(l.$slots, "label.append")
      ]),
      default: withCtx(() => [
        withDirectives(createBaseVNode("textarea", mergeProps({
          id: unref(o),
          ref_key: "textareaRef",
          ref: i,
          "onUpdate:modelValue": u[0] || (u[0] = (f) => r.value = f),
          class: "q-text-area__input",
          required: e.required,
          placeholder: s.value,
          readonly: e.readonly,
          disabled: e.disabled,
          maxlength: e.maxLength,
          rows: e.rows,
          cols: e.cols,
          resize: e.resize,
          wrap: e.wrap
        }, l.$attrs, toHandlers(c.value, true)), null, 16, zl), [
          [vModelText, r.value]
        ])
      ]),
      _: 2
      /* DYNAMIC */
    }, [
      l.$slots.prepend ? {
        name: "prepend",
        fn: withCtx(() => [
          renderSlot(l.$slots, "prepend")
        ]),
        key: "0"
      } : void 0,
      l.$slots.append ? {
        name: "append",
        fn: withCtx(() => [
          renderSlot(l.$slots, "append")
        ]),
        key: "1"
      } : void 0,
      l.$slots.extras ? {
        name: "extras",
        fn: withCtx(() => [
          renderSlot(l.$slots, "extras")
        ]),
        key: "2"
      } : void 0
    ]), 1032, ["class", "for", "label", "size", "readonly", "disabled", "required"]));
  }
});
var Ul = ce(Ml);
var jl = { class: "pulsing-dots" };
var Vl = defineComponent({
  __name: "PulseDots",
  setup(t7) {
    const e = [1, 2, 3];
    return (n, r) => (openBlock(), createElementBlock("div", jl, [
      (openBlock(), createElementBlock(Fragment, null, renderList(e, (o, s) => createBaseVNode("span", {
        key: s,
        class: "dot",
        style: normalizeStyle({ animationDelay: s * 0.2 + "s" })
      }, " • ", 4)), 64))
    ]));
  }
});
function Gl(t7) {
  return t7 && t7.__esModule && Object.prototype.hasOwnProperty.call(t7, "default") ? t7.default : t7;
}
function $l(t7) {
  if (t7.__esModule) return t7;
  var e = t7.default;
  if (typeof e == "function") {
    var n = function r() {
      return this instanceof r ? Reflect.construct(e, arguments, this.constructor) : e.apply(this, arguments);
    };
    n.prototype = e.prototype;
  } else n = {};
  return Object.defineProperty(n, "__esModule", { value: true }), Object.keys(t7).forEach(function(r) {
    var o = Object.getOwnPropertyDescriptor(t7, r);
    Object.defineProperty(n, r, o.get ? o : {
      enumerable: true,
      get: function() {
        return t7[r];
      }
    });
  }), n;
}
var V = {};
var Hl = "Á";
var Zl = "á";
var Wl = "Ă";
var Jl = "ă";
var Ql = "∾";
var Yl = "∿";
var Xl = "∾̳";
var Kl = "Â";
var eu = "â";
var tu = "´";
var nu = "А";
var ru = "а";
var ou = "Æ";
var su = "æ";
var cu = "⁡";
var iu = "𝔄";
var au = "𝔞";
var lu = "À";
var uu = "à";
var fu = "ℵ";
var du = "ℵ";
var pu = "Α";
var hu = "α";
var mu = "Ā";
var gu = "ā";
var bu = "⨿";
var vu = "&";
var yu = "&";
var wu = "⩕";
var ku = "⩓";
var xu = "∧";
var _u = "⩜";
var Au = "⩘";
var Eu = "⩚";
var Cu = "∠";
var Du = "⦤";
var Su = "∠";
var qu = "⦨";
var Ru = "⦩";
var Tu = "⦪";
var Fu = "⦫";
var Lu = "⦬";
var Nu = "⦭";
var Iu = "⦮";
var Bu = "⦯";
var Ou = "∡";
var Pu = "∟";
var zu = "⊾";
var Mu = "⦝";
var Uu = "∢";
var ju = "Å";
var Vu = "⍼";
var Gu = "Ą";
var $u = "ą";
var Hu = "𝔸";
var Zu = "𝕒";
var Wu = "⩯";
var Ju = "≈";
var Qu = "⩰";
var Yu = "≊";
var Xu = "≋";
var Ku = "'";
var ef = "⁡";
var tf = "≈";
var nf = "≊";
var rf = "Å";
var of = "å";
var sf = "𝒜";
var cf = "𝒶";
var af = "≔";
var lf = "*";
var uf = "≈";
var ff = "≍";
var df = "Ã";
var pf = "ã";
var hf = "Ä";
var mf = "ä";
var gf = "∳";
var bf = "⨑";
var vf = "≌";
var yf = "϶";
var wf = "‵";
var kf = "∽";
var xf = "⋍";
var _f = "∖";
var Af = "⫧";
var Ef = "⊽";
var Cf = "⌅";
var Df = "⌆";
var Sf = "⌅";
var qf = "⎵";
var Rf = "⎶";
var Tf = "≌";
var Ff = "Б";
var Lf = "б";
var Nf = "„";
var If = "∵";
var Bf = "∵";
var Of = "∵";
var Pf = "⦰";
var zf = "϶";
var Mf = "ℬ";
var Uf = "ℬ";
var jf = "Β";
var Vf = "β";
var Gf = "ℶ";
var $f = "≬";
var Hf = "𝔅";
var Zf = "𝔟";
var Wf = "⋂";
var Jf = "◯";
var Qf = "⋃";
var Yf = "⨀";
var Xf = "⨁";
var Kf = "⨂";
var ed = "⨆";
var td = "★";
var nd = "▽";
var rd = "△";
var od = "⨄";
var sd = "⋁";
var cd = "⋀";
var id = "⤍";
var ad = "⧫";
var ld = "▪";
var ud = "▴";
var fd = "▾";
var dd = "◂";
var pd = "▸";
var hd = "␣";
var md = "▒";
var gd = "░";
var bd = "▓";
var vd = "█";
var yd = "=⃥";
var wd = "≡⃥";
var kd = "⫭";
var xd = "⌐";
var _d = "𝔹";
var Ad = "𝕓";
var Ed = "⊥";
var Cd = "⊥";
var Dd = "⋈";
var Sd = "⧉";
var qd = "┐";
var Rd = "╕";
var Td = "╖";
var Fd = "╗";
var Ld = "┌";
var Nd = "╒";
var Id = "╓";
var Bd = "╔";
var Od = "─";
var Pd = "═";
var zd = "┬";
var Md = "╤";
var Ud = "╥";
var jd = "╦";
var Vd = "┴";
var Gd = "╧";
var $d = "╨";
var Hd = "╩";
var Zd = "⊟";
var Wd = "⊞";
var Jd = "⊠";
var Qd = "┘";
var Yd = "╛";
var Xd = "╜";
var Kd = "╝";
var ep = "└";
var tp = "╘";
var np = "╙";
var rp = "╚";
var op = "│";
var sp = "║";
var cp = "┼";
var ip = "╪";
var ap = "╫";
var lp = "╬";
var up = "┤";
var fp = "╡";
var dp = "╢";
var pp = "╣";
var hp = "├";
var mp = "╞";
var gp = "╟";
var bp = "╠";
var vp = "‵";
var yp = "˘";
var wp = "˘";
var kp = "¦";
var xp = "𝒷";
var _p = "ℬ";
var Ap = "⁏";
var Ep = "∽";
var Cp = "⋍";
var Dp = "⧅";
var Sp = "\\";
var qp = "⟈";
var Rp = "•";
var Tp = "•";
var Fp = "≎";
var Lp = "⪮";
var Np = "≏";
var Ip = "≎";
var Bp = "≏";
var Op = "Ć";
var Pp = "ć";
var zp = "⩄";
var Mp = "⩉";
var Up = "⩋";
var jp = "∩";
var Vp = "⋒";
var Gp = "⩇";
var $p = "⩀";
var Hp = "ⅅ";
var Zp = "∩︀";
var Wp = "⁁";
var Jp = "ˇ";
var Qp = "ℭ";
var Yp = "⩍";
var Xp = "Č";
var Kp = "č";
var eh = "Ç";
var th = "ç";
var nh = "Ĉ";
var rh = "ĉ";
var oh = "∰";
var sh = "⩌";
var ch = "⩐";
var ih = "Ċ";
var ah = "ċ";
var lh = "¸";
var uh = "¸";
var fh = "⦲";
var dh = "¢";
var ph = "·";
var hh = "·";
var mh = "𝔠";
var gh = "ℭ";
var bh = "Ч";
var vh = "ч";
var yh = "✓";
var wh = "✓";
var kh = "Χ";
var xh = "χ";
var _h = "ˆ";
var Ah = "≗";
var Eh = "↺";
var Ch = "↻";
var Dh = "⊛";
var Sh = "⊚";
var qh = "⊝";
var Rh = "⊙";
var Th = "®";
var Fh = "Ⓢ";
var Lh = "⊖";
var Nh = "⊕";
var Ih = "⊗";
var Bh = "○";
var Oh = "⧃";
var Ph = "≗";
var zh = "⨐";
var Mh = "⫯";
var Uh = "⧂";
var jh = "∲";
var Vh = "”";
var Gh = "’";
var $h = "♣";
var Hh = "♣";
var Zh = ":";
var Wh = "∷";
var Jh = "⩴";
var Qh = "≔";
var Yh = "≔";
var Xh = ",";
var Kh = "@";
var em = "∁";
var tm = "∘";
var nm = "∁";
var rm = "ℂ";
var om = "≅";
var sm = "⩭";
var cm = "≡";
var im = "∮";
var am = "∯";
var lm = "∮";
var um = "𝕔";
var fm = "ℂ";
var dm = "∐";
var pm = "∐";
var hm = "©";
var mm = "©";
var gm = "℗";
var bm = "∳";
var vm = "↵";
var ym = "✗";
var wm = "⨯";
var km = "𝒞";
var xm = "𝒸";
var _m = "⫏";
var Am = "⫑";
var Em = "⫐";
var Cm = "⫒";
var Dm = "⋯";
var Sm = "⤸";
var qm = "⤵";
var Rm = "⋞";
var Tm = "⋟";
var Fm = "↶";
var Lm = "⤽";
var Nm = "⩈";
var Im = "⩆";
var Bm = "≍";
var Om = "∪";
var Pm = "⋓";
var zm = "⩊";
var Mm = "⊍";
var Um = "⩅";
var jm = "∪︀";
var Vm = "↷";
var Gm = "⤼";
var $m = "⋞";
var Hm = "⋟";
var Zm = "⋎";
var Wm = "⋏";
var Jm = "¤";
var Qm = "↶";
var Ym = "↷";
var Xm = "⋎";
var Km = "⋏";
var eg = "∲";
var tg = "∱";
var ng = "⌭";
var rg = "†";
var og = "‡";
var sg = "ℸ";
var cg = "↓";
var ig = "↡";
var ag = "⇓";
var lg = "‐";
var ug = "⫤";
var fg = "⊣";
var dg = "⤏";
var pg = "˝";
var hg = "Ď";
var mg = "ď";
var gg = "Д";
var bg = "д";
var vg = "‡";
var yg = "⇊";
var wg = "ⅅ";
var kg = "ⅆ";
var xg = "⤑";
var _g = "⩷";
var Ag = "°";
var Eg = "∇";
var Cg = "Δ";
var Dg = "δ";
var Sg = "⦱";
var qg = "⥿";
var Rg = "𝔇";
var Tg = "𝔡";
var Fg = "⥥";
var Lg = "⇃";
var Ng = "⇂";
var Ig = "´";
var Bg = "˙";
var Og = "˝";
var Pg = "`";
var zg = "˜";
var Mg = "⋄";
var Ug = "⋄";
var jg = "⋄";
var Vg = "♦";
var Gg = "♦";
var $g = "¨";
var Hg = "ⅆ";
var Zg = "ϝ";
var Wg = "⋲";
var Jg = "÷";
var Qg = "÷";
var Yg = "⋇";
var Xg = "⋇";
var Kg = "Ђ";
var eb = "ђ";
var tb = "⌞";
var nb = "⌍";
var rb = "$";
var ob = "𝔻";
var sb = "𝕕";
var cb = "¨";
var ib = "˙";
var ab = "⃜";
var lb = "≐";
var ub = "≑";
var fb = "≐";
var db = "∸";
var pb = "∔";
var hb = "⊡";
var mb = "⌆";
var gb = "∯";
var bb = "¨";
var vb = "⇓";
var yb = "⇐";
var wb = "⇔";
var kb = "⫤";
var xb = "⟸";
var _b = "⟺";
var Ab = "⟹";
var Eb = "⇒";
var Cb = "⊨";
var Db = "⇑";
var Sb = "⇕";
var qb = "∥";
var Rb = "⤓";
var Tb = "↓";
var Fb = "↓";
var Lb = "⇓";
var Nb = "⇵";
var Ib = "̑";
var Bb = "⇊";
var Ob = "⇃";
var Pb = "⇂";
var zb = "⥐";
var Mb = "⥞";
var Ub = "⥖";
var jb = "↽";
var Vb = "⥟";
var Gb = "⥗";
var $b = "⇁";
var Hb = "↧";
var Zb = "⊤";
var Wb = "⤐";
var Jb = "⌟";
var Qb = "⌌";
var Yb = "𝒟";
var Xb = "𝒹";
var Kb = "Ѕ";
var ev = "ѕ";
var tv = "⧶";
var nv = "Đ";
var rv = "đ";
var ov = "⋱";
var sv = "▿";
var cv = "▾";
var iv = "⇵";
var av = "⥯";
var lv = "⦦";
var uv = "Џ";
var fv = "џ";
var dv = "⟿";
var pv = "É";
var hv = "é";
var mv = "⩮";
var gv = "Ě";
var bv = "ě";
var vv = "Ê";
var yv = "ê";
var wv = "≖";
var kv = "≕";
var xv = "Э";
var _v = "э";
var Av = "⩷";
var Ev = "Ė";
var Cv = "ė";
var Dv = "≑";
var Sv = "ⅇ";
var qv = "≒";
var Rv = "𝔈";
var Tv = "𝔢";
var Fv = "⪚";
var Lv = "È";
var Nv = "è";
var Iv = "⪖";
var Bv = "⪘";
var Ov = "⪙";
var Pv = "∈";
var zv = "⏧";
var Mv = "ℓ";
var Uv = "⪕";
var jv = "⪗";
var Vv = "Ē";
var Gv = "ē";
var $v = "∅";
var Hv = "∅";
var Zv = "◻";
var Wv = "∅";
var Jv = "▫";
var Qv = " ";
var Yv = " ";
var Xv = " ";
var Kv = "Ŋ";
var e0 = "ŋ";
var t0 = " ";
var n0 = "Ę";
var r0 = "ę";
var o0 = "𝔼";
var s0 = "𝕖";
var c0 = "⋕";
var i0 = "⧣";
var a0 = "⩱";
var l0 = "ε";
var u0 = "Ε";
var f0 = "ε";
var d0 = "ϵ";
var p0 = "≖";
var h0 = "≕";
var m0 = "≂";
var g0 = "⪖";
var b0 = "⪕";
var v0 = "⩵";
var y0 = "=";
var w0 = "≂";
var k0 = "≟";
var x0 = "⇌";
var _0 = "≡";
var A0 = "⩸";
var E0 = "⧥";
var C0 = "⥱";
var D0 = "≓";
var S0 = "ℯ";
var q0 = "ℰ";
var R0 = "≐";
var T0 = "⩳";
var F0 = "≂";
var L0 = "Η";
var N0 = "η";
var I0 = "Ð";
var B0 = "ð";
var O0 = "Ë";
var P0 = "ë";
var z0 = "€";
var M0 = "!";
var U0 = "∃";
var j0 = "∃";
var V0 = "ℰ";
var G0 = "ⅇ";
var $0 = "ⅇ";
var H0 = "≒";
var Z0 = "Ф";
var W0 = "ф";
var J0 = "♀";
var Q0 = "ﬃ";
var Y0 = "ﬀ";
var X0 = "ﬄ";
var K0 = "𝔉";
var ey = "𝔣";
var ty = "ﬁ";
var ny = "◼";
var ry = "▪";
var oy = "fj";
var sy = "♭";
var cy = "ﬂ";
var iy = "▱";
var ay = "ƒ";
var ly = "𝔽";
var uy = "𝕗";
var fy = "∀";
var dy = "∀";
var py = "⋔";
var hy = "⫙";
var my = "ℱ";
var gy = "⨍";
var by = "½";
var vy = "⅓";
var yy = "¼";
var wy = "⅕";
var ky = "⅙";
var xy = "⅛";
var _y = "⅔";
var Ay = "⅖";
var Ey = "¾";
var Cy = "⅗";
var Dy = "⅜";
var Sy = "⅘";
var qy = "⅚";
var Ry = "⅝";
var Ty = "⅞";
var Fy = "⁄";
var Ly = "⌢";
var Ny = "𝒻";
var Iy = "ℱ";
var By = "ǵ";
var Oy = "Γ";
var Py = "γ";
var zy = "Ϝ";
var My = "ϝ";
var Uy = "⪆";
var jy = "Ğ";
var Vy = "ğ";
var Gy = "Ģ";
var $y = "Ĝ";
var Hy = "ĝ";
var Zy = "Г";
var Wy = "г";
var Jy = "Ġ";
var Qy = "ġ";
var Yy = "≥";
var Xy = "≧";
var Ky = "⪌";
var ew = "⋛";
var tw = "≥";
var nw = "≧";
var rw = "⩾";
var ow = "⪩";
var sw = "⩾";
var cw = "⪀";
var iw = "⪂";
var aw = "⪄";
var lw = "⋛︀";
var uw = "⪔";
var fw = "𝔊";
var dw = "𝔤";
var pw = "≫";
var hw = "⋙";
var mw = "⋙";
var gw = "ℷ";
var bw = "Ѓ";
var vw = "ѓ";
var yw = "⪥";
var ww = "≷";
var kw = "⪒";
var xw = "⪤";
var _w = "⪊";
var Aw = "⪊";
var Ew = "⪈";
var Cw = "≩";
var Dw = "⪈";
var Sw = "≩";
var qw = "⋧";
var Rw = "𝔾";
var Tw = "𝕘";
var Fw = "`";
var Lw = "≥";
var Nw = "⋛";
var Iw = "≧";
var Bw = "⪢";
var Ow = "≷";
var Pw = "⩾";
var zw = "≳";
var Mw = "𝒢";
var Uw = "ℊ";
var jw = "≳";
var Vw = "⪎";
var Gw = "⪐";
var $w = "⪧";
var Hw = "⩺";
var Zw = ">";
var Ww = ">";
var Jw = "≫";
var Qw = "⋗";
var Yw = "⦕";
var Xw = "⩼";
var Kw = "⪆";
var ek = "⥸";
var tk = "⋗";
var nk = "⋛";
var rk = "⪌";
var ok = "≷";
var sk = "≳";
var ck = "≩︀";
var ik = "≩︀";
var ak = "ˇ";
var lk = " ";
var uk = "½";
var fk = "ℋ";
var dk = "Ъ";
var pk = "ъ";
var hk = "⥈";
var mk = "↔";
var gk = "⇔";
var bk = "↭";
var vk = "^";
var yk = "ℏ";
var wk = "Ĥ";
var kk = "ĥ";
var xk = "♥";
var _k = "♥";
var Ak = "…";
var Ek = "⊹";
var Ck = "𝔥";
var Dk = "ℌ";
var Sk = "ℋ";
var qk = "⤥";
var Rk = "⤦";
var Tk = "⇿";
var Fk = "∻";
var Lk = "↩";
var Nk = "↪";
var Ik = "𝕙";
var Bk = "ℍ";
var Ok = "―";
var Pk = "─";
var zk = "𝒽";
var Mk = "ℋ";
var Uk = "ℏ";
var jk = "Ħ";
var Vk = "ħ";
var Gk = "≎";
var $k = "≏";
var Hk = "⁃";
var Zk = "‐";
var Wk = "Í";
var Jk = "í";
var Qk = "⁣";
var Yk = "Î";
var Xk = "î";
var Kk = "И";
var ex = "и";
var tx = "İ";
var nx = "Е";
var rx = "е";
var ox = "¡";
var sx = "⇔";
var cx = "𝔦";
var ix = "ℑ";
var ax = "Ì";
var lx = "ì";
var ux = "ⅈ";
var fx = "⨌";
var dx = "∭";
var px = "⧜";
var hx = "℩";
var mx = "Ĳ";
var gx = "ĳ";
var bx = "Ī";
var vx = "ī";
var yx = "ℑ";
var wx = "ⅈ";
var kx = "ℐ";
var xx = "ℑ";
var _x = "ı";
var Ax = "ℑ";
var Ex = "⊷";
var Cx = "Ƶ";
var Dx = "⇒";
var Sx = "℅";
var qx = "∞";
var Rx = "⧝";
var Tx = "ı";
var Fx = "⊺";
var Lx = "∫";
var Nx = "∬";
var Ix = "ℤ";
var Bx = "∫";
var Ox = "⊺";
var Px = "⋂";
var zx = "⨗";
var Mx = "⨼";
var Ux = "⁣";
var jx = "⁢";
var Vx = "Ё";
var Gx = "ё";
var $x = "Į";
var Hx = "į";
var Zx = "𝕀";
var Wx = "𝕚";
var Jx = "Ι";
var Qx = "ι";
var Yx = "⨼";
var Xx = "¿";
var Kx = "𝒾";
var e_ = "ℐ";
var t_ = "∈";
var n_ = "⋵";
var r_ = "⋹";
var o_ = "⋴";
var s_ = "⋳";
var c_ = "∈";
var i_ = "⁢";
var a_ = "Ĩ";
var l_ = "ĩ";
var u_ = "І";
var f_ = "і";
var d_ = "Ï";
var p_ = "ï";
var h_ = "Ĵ";
var m_ = "ĵ";
var g_ = "Й";
var b_ = "й";
var v_ = "𝔍";
var y_ = "𝔧";
var w_ = "ȷ";
var k_ = "𝕁";
var x_ = "𝕛";
var __ = "𝒥";
var A_ = "𝒿";
var E_ = "Ј";
var C_ = "ј";
var D_ = "Є";
var S_ = "є";
var q_ = "Κ";
var R_ = "κ";
var T_ = "ϰ";
var F_ = "Ķ";
var L_ = "ķ";
var N_ = "К";
var I_ = "к";
var B_ = "𝔎";
var O_ = "𝔨";
var P_ = "ĸ";
var z_ = "Х";
var M_ = "х";
var U_ = "Ќ";
var j_ = "ќ";
var V_ = "𝕂";
var G_ = "𝕜";
var $_ = "𝒦";
var H_ = "𝓀";
var Z_ = "⇚";
var W_ = "Ĺ";
var J_ = "ĺ";
var Q_ = "⦴";
var Y_ = "ℒ";
var X_ = "Λ";
var K_ = "λ";
var eA = "⟨";
var tA = "⟪";
var nA = "⦑";
var rA = "⟨";
var oA = "⪅";
var sA = "ℒ";
var cA = "«";
var iA = "⇤";
var aA = "⤟";
var lA = "←";
var uA = "↞";
var fA = "⇐";
var dA = "⤝";
var pA = "↩";
var hA = "↫";
var mA = "⤹";
var gA = "⥳";
var bA = "↢";
var vA = "⤙";
var yA = "⤛";
var wA = "⪫";
var kA = "⪭";
var xA = "⪭︀";
var _A = "⤌";
var AA = "⤎";
var EA = "❲";
var CA = "{";
var DA = "[";
var SA = "⦋";
var qA = "⦏";
var RA = "⦍";
var TA = "Ľ";
var FA = "ľ";
var LA = "Ļ";
var NA = "ļ";
var IA = "⌈";
var BA = "{";
var OA = "Л";
var PA = "л";
var zA = "⤶";
var MA = "“";
var UA = "„";
var jA = "⥧";
var VA = "⥋";
var GA = "↲";
var $A = "≤";
var HA = "≦";
var ZA = "⟨";
var WA = "⇤";
var JA = "←";
var QA = "←";
var YA = "⇐";
var XA = "⇆";
var KA = "↢";
var eE = "⌈";
var tE = "⟦";
var nE = "⥡";
var rE = "⥙";
var oE = "⇃";
var sE = "⌊";
var cE = "↽";
var iE = "↼";
var aE = "⇇";
var lE = "↔";
var uE = "↔";
var fE = "⇔";
var dE = "⇆";
var pE = "⇋";
var hE = "↭";
var mE = "⥎";
var gE = "↤";
var bE = "⊣";
var vE = "⥚";
var yE = "⋋";
var wE = "⧏";
var kE = "⊲";
var xE = "⊴";
var _E = "⥑";
var AE = "⥠";
var EE = "⥘";
var CE = "↿";
var DE = "⥒";
var SE = "↼";
var qE = "⪋";
var RE = "⋚";
var TE = "≤";
var FE = "≦";
var LE = "⩽";
var NE = "⪨";
var IE = "⩽";
var BE = "⩿";
var OE = "⪁";
var PE = "⪃";
var zE = "⋚︀";
var ME = "⪓";
var UE = "⪅";
var jE = "⋖";
var VE = "⋚";
var GE = "⪋";
var $E = "⋚";
var HE = "≦";
var ZE = "≶";
var WE = "≶";
var JE = "⪡";
var QE = "≲";
var YE = "⩽";
var XE = "≲";
var KE = "⥼";
var e1 = "⌊";
var t1 = "𝔏";
var n1 = "𝔩";
var r1 = "≶";
var o1 = "⪑";
var s1 = "⥢";
var c1 = "↽";
var i1 = "↼";
var a1 = "⥪";
var l1 = "▄";
var u1 = "Љ";
var f1 = "љ";
var d1 = "⇇";
var p1 = "≪";
var h1 = "⋘";
var m1 = "⌞";
var g1 = "⇚";
var b1 = "⥫";
var v1 = "◺";
var y1 = "Ŀ";
var w1 = "ŀ";
var k1 = "⎰";
var x1 = "⎰";
var _1 = "⪉";
var A1 = "⪉";
var E1 = "⪇";
var C1 = "≨";
var D1 = "⪇";
var S1 = "≨";
var q1 = "⋦";
var R1 = "⟬";
var T1 = "⇽";
var F1 = "⟦";
var L1 = "⟵";
var N1 = "⟵";
var I1 = "⟸";
var B1 = "⟷";
var O1 = "⟷";
var P1 = "⟺";
var z1 = "⟼";
var M1 = "⟶";
var U1 = "⟶";
var j1 = "⟹";
var V1 = "↫";
var G1 = "↬";
var $1 = "⦅";
var H1 = "𝕃";
var Z1 = "𝕝";
var W1 = "⨭";
var J1 = "⨴";
var Q1 = "∗";
var Y1 = "_";
var X1 = "↙";
var K1 = "↘";
var eC = "◊";
var tC = "◊";
var nC = "⧫";
var rC = "(";
var oC = "⦓";
var sC = "⇆";
var cC = "⌟";
var iC = "⇋";
var aC = "⥭";
var lC = "‎";
var uC = "⊿";
var fC = "‹";
var dC = "𝓁";
var pC = "ℒ";
var hC = "↰";
var mC = "↰";
var gC = "≲";
var bC = "⪍";
var vC = "⪏";
var yC = "[";
var wC = "‘";
var kC = "‚";
var xC = "Ł";
var _C = "ł";
var AC = "⪦";
var EC = "⩹";
var CC = "<";
var DC = "<";
var SC = "≪";
var qC = "⋖";
var RC = "⋋";
var TC = "⋉";
var FC = "⥶";
var LC = "⩻";
var NC = "◃";
var IC = "⊴";
var BC = "◂";
var OC = "⦖";
var PC = "⥊";
var zC = "⥦";
var MC = "≨︀";
var UC = "≨︀";
var jC = "¯";
var VC = "♂";
var GC = "✠";
var $C = "✠";
var HC = "↦";
var ZC = "↦";
var WC = "↧";
var JC = "↤";
var QC = "↥";
var YC = "▮";
var XC = "⨩";
var KC = "М";
var eD = "м";
var tD = "—";
var nD = "∺";
var rD = "∡";
var oD = " ";
var sD = "ℳ";
var cD = "𝔐";
var iD = "𝔪";
var aD = "℧";
var lD = "µ";
var uD = "*";
var fD = "⫰";
var dD = "∣";
var pD = "·";
var hD = "⊟";
var mD = "−";
var gD = "∸";
var bD = "⨪";
var vD = "∓";
var yD = "⫛";
var wD = "…";
var kD = "∓";
var xD = "⊧";
var _D = "𝕄";
var AD = "𝕞";
var ED = "∓";
var CD = "𝓂";
var DD = "ℳ";
var SD = "∾";
var qD = "Μ";
var RD = "μ";
var TD = "⊸";
var FD = "⊸";
var LD = "∇";
var ND = "Ń";
var ID = "ń";
var BD = "∠⃒";
var OD = "≉";
var PD = "⩰̸";
var zD = "≋̸";
var MD = "ŉ";
var UD = "≉";
var jD = "♮";
var VD = "ℕ";
var GD = "♮";
var $D = " ";
var HD = "≎̸";
var ZD = "≏̸";
var WD = "⩃";
var JD = "Ň";
var QD = "ň";
var YD = "Ņ";
var XD = "ņ";
var KD = "≇";
var eS = "⩭̸";
var tS = "⩂";
var nS = "Н";
var rS = "н";
var oS = "–";
var sS = "⤤";
var cS = "↗";
var iS = "⇗";
var aS = "↗";
var lS = "≠";
var uS = "≐̸";
var fS = "​";
var dS = "​";
var pS = "​";
var hS = "​";
var mS = "≢";
var gS = "⤨";
var bS = "≂̸";
var vS = "≫";
var yS = "≪";
var wS = `
`;
var kS = "∄";
var xS = "∄";
var _S = "𝔑";
var AS = "𝔫";
var ES = "≧̸";
var CS = "≱";
var DS = "≱";
var SS = "≧̸";
var qS = "⩾̸";
var RS = "⩾̸";
var TS = "⋙̸";
var FS = "≵";
var LS = "≫⃒";
var NS = "≯";
var IS = "≯";
var BS = "≫̸";
var OS = "↮";
var PS = "⇎";
var zS = "⫲";
var MS = "∋";
var US = "⋼";
var jS = "⋺";
var VS = "∋";
var GS = "Њ";
var $S = "њ";
var HS = "↚";
var ZS = "⇍";
var WS = "‥";
var JS = "≦̸";
var QS = "≰";
var YS = "↚";
var XS = "⇍";
var KS = "↮";
var eq = "⇎";
var tq = "≰";
var nq = "≦̸";
var rq = "⩽̸";
var oq = "⩽̸";
var sq = "≮";
var cq = "⋘̸";
var iq = "≴";
var aq = "≪⃒";
var lq = "≮";
var uq = "⋪";
var fq = "⋬";
var dq = "≪̸";
var pq = "∤";
var hq = "⁠";
var mq = " ";
var gq = "𝕟";
var bq = "ℕ";
var vq = "⫬";
var yq = "¬";
var wq = "≢";
var kq = "≭";
var xq = "∦";
var _q = "∉";
var Aq = "≠";
var Eq = "≂̸";
var Cq = "∄";
var Dq = "≯";
var Sq = "≱";
var qq = "≧̸";
var Rq = "≫̸";
var Tq = "≹";
var Fq = "⩾̸";
var Lq = "≵";
var Nq = "≎̸";
var Iq = "≏̸";
var Bq = "∉";
var Oq = "⋵̸";
var Pq = "⋹̸";
var zq = "∉";
var Mq = "⋷";
var Uq = "⋶";
var jq = "⧏̸";
var Vq = "⋪";
var Gq = "⋬";
var $q = "≮";
var Hq = "≰";
var Zq = "≸";
var Wq = "≪̸";
var Jq = "⩽̸";
var Qq = "≴";
var Yq = "⪢̸";
var Xq = "⪡̸";
var Kq = "∌";
var eR = "∌";
var tR = "⋾";
var nR = "⋽";
var rR = "⊀";
var oR = "⪯̸";
var sR = "⋠";
var cR = "∌";
var iR = "⧐̸";
var aR = "⋫";
var lR = "⋭";
var uR = "⊏̸";
var fR = "⋢";
var dR = "⊐̸";
var pR = "⋣";
var hR = "⊂⃒";
var mR = "⊈";
var gR = "⊁";
var bR = "⪰̸";
var vR = "⋡";
var yR = "≿̸";
var wR = "⊃⃒";
var kR = "⊉";
var xR = "≁";
var _R = "≄";
var AR = "≇";
var ER = "≉";
var CR = "∤";
var DR = "∦";
var SR = "∦";
var qR = "⫽⃥";
var RR = "∂̸";
var TR = "⨔";
var FR = "⊀";
var LR = "⋠";
var NR = "⊀";
var IR = "⪯̸";
var BR = "⪯̸";
var OR = "⤳̸";
var PR = "↛";
var zR = "⇏";
var MR = "↝̸";
var UR = "↛";
var jR = "⇏";
var VR = "⋫";
var GR = "⋭";
var $R = "⊁";
var HR = "⋡";
var ZR = "⪰̸";
var WR = "𝒩";
var JR = "𝓃";
var QR = "∤";
var YR = "∦";
var XR = "≁";
var KR = "≄";
var eT = "≄";
var tT = "∤";
var nT = "∦";
var rT = "⋢";
var oT = "⋣";
var sT = "⊄";
var cT = "⫅̸";
var iT = "⊈";
var aT = "⊂⃒";
var lT = "⊈";
var uT = "⫅̸";
var fT = "⊁";
var dT = "⪰̸";
var pT = "⊅";
var hT = "⫆̸";
var mT = "⊉";
var gT = "⊃⃒";
var bT = "⊉";
var vT = "⫆̸";
var yT = "≹";
var wT = "Ñ";
var kT = "ñ";
var xT = "≸";
var _T = "⋪";
var AT = "⋬";
var ET = "⋫";
var CT = "⋭";
var DT = "Ν";
var ST = "ν";
var qT = "#";
var RT = "№";
var TT = " ";
var FT = "≍⃒";
var LT = "⊬";
var NT = "⊭";
var IT = "⊮";
var BT = "⊯";
var OT = "≥⃒";
var PT = ">⃒";
var zT = "⤄";
var MT = "⧞";
var UT = "⤂";
var jT = "≤⃒";
var VT = "<⃒";
var GT = "⊴⃒";
var $T = "⤃";
var HT = "⊵⃒";
var ZT = "∼⃒";
var WT = "⤣";
var JT = "↖";
var QT = "⇖";
var YT = "↖";
var XT = "⤧";
var KT = "Ó";
var eF = "ó";
var tF = "⊛";
var nF = "Ô";
var rF = "ô";
var oF = "⊚";
var sF = "О";
var cF = "о";
var iF = "⊝";
var aF = "Ő";
var lF = "ő";
var uF = "⨸";
var fF = "⊙";
var dF = "⦼";
var pF = "Œ";
var hF = "œ";
var mF = "⦿";
var gF = "𝔒";
var bF = "𝔬";
var vF = "˛";
var yF = "Ò";
var wF = "ò";
var kF = "⧁";
var xF = "⦵";
var _F = "Ω";
var AF = "∮";
var EF = "↺";
var CF = "⦾";
var DF = "⦻";
var SF = "‾";
var qF = "⧀";
var RF = "Ō";
var TF = "ō";
var FF = "Ω";
var LF = "ω";
var NF = "Ο";
var IF = "ο";
var BF = "⦶";
var OF = "⊖";
var PF = "𝕆";
var zF = "𝕠";
var MF = "⦷";
var UF = "“";
var jF = "‘";
var VF = "⦹";
var GF = "⊕";
var $F = "↻";
var HF = "⩔";
var ZF = "∨";
var WF = "⩝";
var JF = "ℴ";
var QF = "ℴ";
var YF = "ª";
var XF = "º";
var KF = "⊶";
var eL = "⩖";
var tL = "⩗";
var nL = "⩛";
var rL = "Ⓢ";
var oL = "𝒪";
var sL = "ℴ";
var cL = "Ø";
var iL = "ø";
var aL = "⊘";
var lL = "Õ";
var uL = "õ";
var fL = "⨶";
var dL = "⨷";
var pL = "⊗";
var hL = "Ö";
var mL = "ö";
var gL = "⌽";
var bL = "‾";
var vL = "⏞";
var yL = "⎴";
var wL = "⏜";
var kL = "¶";
var xL = "∥";
var _L = "∥";
var AL = "⫳";
var EL = "⫽";
var CL = "∂";
var DL = "∂";
var SL = "П";
var qL = "п";
var RL = "%";
var TL = ".";
var FL = "‰";
var LL = "⊥";
var NL = "‱";
var IL = "𝔓";
var BL = "𝔭";
var OL = "Φ";
var PL = "φ";
var zL = "ϕ";
var ML = "ℳ";
var UL = "☎";
var jL = "Π";
var VL = "π";
var GL = "⋔";
var $L = "ϖ";
var HL = "ℏ";
var ZL = "ℎ";
var WL = "ℏ";
var JL = "⨣";
var QL = "⊞";
var YL = "⨢";
var XL = "+";
var KL = "∔";
var e2 = "⨥";
var t22 = "⩲";
var n2 = "±";
var r2 = "±";
var o2 = "⨦";
var s2 = "⨧";
var c2 = "±";
var i2 = "ℌ";
var a2 = "⨕";
var l2 = "𝕡";
var u2 = "ℙ";
var f2 = "£";
var d2 = "⪷";
var p2 = "⪻";
var h2 = "≺";
var m2 = "≼";
var g2 = "⪷";
var b2 = "≺";
var v2 = "≼";
var y2 = "≺";
var w2 = "⪯";
var k2 = "≼";
var x2 = "≾";
var _2 = "⪯";
var A2 = "⪹";
var E2 = "⪵";
var C2 = "⋨";
var D2 = "⪯";
var S2 = "⪳";
var q2 = "≾";
var R2 = "′";
var T2 = "″";
var F2 = "ℙ";
var L2 = "⪹";
var N2 = "⪵";
var I2 = "⋨";
var B2 = "∏";
var O2 = "∏";
var P2 = "⌮";
var z2 = "⌒";
var M2 = "⌓";
var U2 = "∝";
var j2 = "∝";
var V2 = "∷";
var G2 = "∝";
var $2 = "≾";
var H2 = "⊰";
var Z2 = "𝒫";
var W2 = "𝓅";
var J2 = "Ψ";
var Q2 = "ψ";
var Y2 = " ";
var X2 = "𝔔";
var K2 = "𝔮";
var eN = "⨌";
var tN = "𝕢";
var nN = "ℚ";
var rN = "⁗";
var oN = "𝒬";
var sN = "𝓆";
var cN = "ℍ";
var iN = "⨖";
var aN = "?";
var lN = "≟";
var uN = '"';
var fN = '"';
var dN = "⇛";
var pN = "∽̱";
var hN = "Ŕ";
var mN = "ŕ";
var gN = "√";
var bN = "⦳";
var vN = "⟩";
var yN = "⟫";
var wN = "⦒";
var kN = "⦥";
var xN = "⟩";
var _N = "»";
var AN = "⥵";
var EN = "⇥";
var CN = "⤠";
var DN = "⤳";
var SN = "→";
var qN = "↠";
var RN = "⇒";
var TN = "⤞";
var FN = "↪";
var LN = "↬";
var NN = "⥅";
var IN = "⥴";
var BN = "⤖";
var ON = "↣";
var PN = "↝";
var zN = "⤚";
var MN = "⤜";
var UN = "∶";
var jN = "ℚ";
var VN = "⤍";
var GN = "⤏";
var $N = "⤐";
var HN = "❳";
var ZN = "}";
var WN = "]";
var JN = "⦌";
var QN = "⦎";
var YN = "⦐";
var XN = "Ř";
var KN = "ř";
var eI = "Ŗ";
var tI = "ŗ";
var nI = "⌉";
var rI = "}";
var oI = "Р";
var sI = "р";
var cI = "⤷";
var iI = "⥩";
var aI = "”";
var lI = "”";
var uI = "↳";
var fI = "ℜ";
var dI = "ℛ";
var pI = "ℜ";
var hI = "ℝ";
var mI = "ℜ";
var gI = "▭";
var bI = "®";
var vI = "®";
var yI = "∋";
var wI = "⇋";
var kI = "⥯";
var xI = "⥽";
var _I = "⌋";
var AI = "𝔯";
var EI = "ℜ";
var CI = "⥤";
var DI = "⇁";
var SI = "⇀";
var qI = "⥬";
var RI = "Ρ";
var TI = "ρ";
var FI = "ϱ";
var LI = "⟩";
var NI = "⇥";
var II = "→";
var BI = "→";
var OI = "⇒";
var PI = "⇄";
var zI = "↣";
var MI = "⌉";
var UI = "⟧";
var jI = "⥝";
var VI = "⥕";
var GI = "⇂";
var $I = "⌋";
var HI = "⇁";
var ZI = "⇀";
var WI = "⇄";
var JI = "⇌";
var QI = "⇉";
var YI = "↝";
var XI = "↦";
var KI = "⊢";
var eB = "⥛";
var tB = "⋌";
var nB = "⧐";
var rB = "⊳";
var oB = "⊵";
var sB = "⥏";
var cB = "⥜";
var iB = "⥔";
var aB = "↾";
var lB = "⥓";
var uB = "⇀";
var fB = "˚";
var dB = "≓";
var pB = "⇄";
var hB = "⇌";
var mB = "‏";
var gB = "⎱";
var bB = "⎱";
var vB = "⫮";
var yB = "⟭";
var wB = "⇾";
var kB = "⟧";
var xB = "⦆";
var _B = "𝕣";
var AB = "ℝ";
var EB = "⨮";
var CB = "⨵";
var DB = "⥰";
var SB = ")";
var qB = "⦔";
var RB = "⨒";
var TB = "⇉";
var FB = "⇛";
var LB = "›";
var NB = "𝓇";
var IB = "ℛ";
var BB = "↱";
var OB = "↱";
var PB = "]";
var zB = "’";
var MB = "’";
var UB = "⋌";
var jB = "⋊";
var VB = "▹";
var GB = "⊵";
var $B = "▸";
var HB = "⧎";
var ZB = "⧴";
var WB = "⥨";
var JB = "℞";
var QB = "Ś";
var YB = "ś";
var XB = "‚";
var KB = "⪸";
var eO = "Š";
var tO = "š";
var nO = "⪼";
var rO = "≻";
var oO = "≽";
var sO = "⪰";
var cO = "⪴";
var iO = "Ş";
var aO = "ş";
var lO = "Ŝ";
var uO = "ŝ";
var fO = "⪺";
var dO = "⪶";
var pO = "⋩";
var hO = "⨓";
var mO = "≿";
var gO = "С";
var bO = "с";
var vO = "⊡";
var yO = "⋅";
var wO = "⩦";
var kO = "⤥";
var xO = "↘";
var _O = "⇘";
var AO = "↘";
var EO = "§";
var CO = ";";
var DO = "⤩";
var SO = "∖";
var qO = "∖";
var RO = "✶";
var TO = "𝔖";
var FO = "𝔰";
var LO = "⌢";
var NO = "♯";
var IO = "Щ";
var BO = "щ";
var OO = "Ш";
var PO = "ш";
var zO = "↓";
var MO = "←";
var UO = "∣";
var jO = "∥";
var VO = "→";
var GO = "↑";
var $O = "­";
var HO = "Σ";
var ZO = "σ";
var WO = "ς";
var JO = "ς";
var QO = "∼";
var YO = "⩪";
var XO = "≃";
var KO = "≃";
var eP = "⪞";
var tP = "⪠";
var nP = "⪝";
var rP = "⪟";
var oP = "≆";
var sP = "⨤";
var cP = "⥲";
var iP = "←";
var aP = "∘";
var lP = "∖";
var uP = "⨳";
var fP = "⧤";
var dP = "∣";
var pP = "⌣";
var hP = "⪪";
var mP = "⪬";
var gP = "⪬︀";
var bP = "Ь";
var vP = "ь";
var yP = "⌿";
var wP = "⧄";
var kP = "/";
var xP = "𝕊";
var _P = "𝕤";
var AP = "♠";
var EP = "♠";
var CP = "∥";
var DP = "⊓";
var SP = "⊓︀";
var qP = "⊔";
var RP = "⊔︀";
var TP = "√";
var FP = "⊏";
var LP = "⊑";
var NP = "⊏";
var IP = "⊑";
var BP = "⊐";
var OP = "⊒";
var PP = "⊐";
var zP = "⊒";
var MP = "□";
var UP = "□";
var jP = "⊓";
var VP = "⊏";
var GP = "⊑";
var $P = "⊐";
var HP = "⊒";
var ZP = "⊔";
var WP = "▪";
var JP = "□";
var QP = "▪";
var YP = "→";
var XP = "𝒮";
var KP = "𝓈";
var ez = "∖";
var tz = "⌣";
var nz = "⋆";
var rz = "⋆";
var oz = "☆";
var sz = "★";
var cz = "ϵ";
var iz = "ϕ";
var az = "¯";
var lz = "⊂";
var uz = "⋐";
var fz = "⪽";
var dz = "⫅";
var pz = "⊆";
var hz = "⫃";
var mz = "⫁";
var gz = "⫋";
var bz = "⊊";
var vz = "⪿";
var yz = "⥹";
var wz = "⊂";
var kz = "⋐";
var xz = "⊆";
var _z = "⫅";
var Az = "⊆";
var Ez = "⊊";
var Cz = "⫋";
var Dz = "⫇";
var Sz = "⫕";
var qz = "⫓";
var Rz = "⪸";
var Tz = "≻";
var Fz = "≽";
var Lz = "≻";
var Nz = "⪰";
var Iz = "≽";
var Bz = "≿";
var Oz = "⪰";
var Pz = "⪺";
var zz = "⪶";
var Mz = "⋩";
var Uz = "≿";
var jz = "∋";
var Vz = "∑";
var Gz = "∑";
var $z = "♪";
var Hz = "¹";
var Zz = "²";
var Wz = "³";
var Jz = "⊃";
var Qz = "⋑";
var Yz = "⪾";
var Xz = "⫘";
var Kz = "⫆";
var eM = "⊇";
var tM = "⫄";
var nM = "⊃";
var rM = "⊇";
var oM = "⟉";
var sM = "⫗";
var cM = "⥻";
var iM = "⫂";
var aM = "⫌";
var lM = "⊋";
var uM = "⫀";
var fM = "⊃";
var dM = "⋑";
var pM = "⊇";
var hM = "⫆";
var mM = "⊋";
var gM = "⫌";
var bM = "⫈";
var vM = "⫔";
var yM = "⫖";
var wM = "⤦";
var kM = "↙";
var xM = "⇙";
var _M = "↙";
var AM = "⤪";
var EM = "ß";
var CM = "	";
var DM = "⌖";
var SM = "Τ";
var qM = "τ";
var RM = "⎴";
var TM = "Ť";
var FM = "ť";
var LM = "Ţ";
var NM = "ţ";
var IM = "Т";
var BM = "т";
var OM = "⃛";
var PM = "⌕";
var zM = "𝔗";
var MM = "𝔱";
var UM = "∴";
var jM = "∴";
var VM = "∴";
var GM = "Θ";
var $M = "θ";
var HM = "ϑ";
var ZM = "ϑ";
var WM = "≈";
var JM = "∼";
var QM = "  ";
var YM = " ";
var XM = " ";
var KM = "≈";
var eU = "∼";
var tU = "Þ";
var nU = "þ";
var rU = "˜";
var oU = "∼";
var sU = "≃";
var cU = "≅";
var iU = "≈";
var aU = "⨱";
var lU = "⊠";
var uU = "×";
var fU = "⨰";
var dU = "∭";
var pU = "⤨";
var hU = "⌶";
var mU = "⫱";
var gU = "⊤";
var bU = "𝕋";
var vU = "𝕥";
var yU = "⫚";
var wU = "⤩";
var kU = "‴";
var xU = "™";
var _U = "™";
var AU = "▵";
var EU = "▿";
var CU = "◃";
var DU = "⊴";
var SU = "≜";
var qU = "▹";
var RU = "⊵";
var TU = "◬";
var FU = "≜";
var LU = "⨺";
var NU = "⃛";
var IU = "⨹";
var BU = "⧍";
var OU = "⨻";
var PU = "⏢";
var zU = "𝒯";
var MU = "𝓉";
var UU = "Ц";
var jU = "ц";
var VU = "Ћ";
var GU = "ћ";
var $U = "Ŧ";
var HU = "ŧ";
var ZU = "≬";
var WU = "↞";
var JU = "↠";
var QU = "Ú";
var YU = "ú";
var XU = "↑";
var KU = "↟";
var e3 = "⇑";
var t32 = "⥉";
var n3 = "Ў";
var r3 = "ў";
var o3 = "Ŭ";
var s3 = "ŭ";
var c3 = "Û";
var i3 = "û";
var a3 = "У";
var l3 = "у";
var u3 = "⇅";
var f3 = "Ű";
var d3 = "ű";
var p3 = "⥮";
var h3 = "⥾";
var m3 = "𝔘";
var g3 = "𝔲";
var b3 = "Ù";
var v3 = "ù";
var y3 = "⥣";
var w3 = "↿";
var k3 = "↾";
var x3 = "▀";
var _3 = "⌜";
var A3 = "⌜";
var E3 = "⌏";
var C3 = "◸";
var D3 = "Ū";
var S3 = "ū";
var q3 = "¨";
var R3 = "_";
var T3 = "⏟";
var F3 = "⎵";
var L3 = "⏝";
var N3 = "⋃";
var I3 = "⊎";
var B3 = "Ų";
var O3 = "ų";
var P3 = "𝕌";
var z3 = "𝕦";
var M3 = "⤒";
var U3 = "↑";
var j3 = "↑";
var V3 = "⇑";
var G3 = "⇅";
var $3 = "↕";
var H3 = "↕";
var Z3 = "⇕";
var W3 = "⥮";
var J3 = "↿";
var Q3 = "↾";
var Y3 = "⊎";
var X3 = "↖";
var K3 = "↗";
var ej = "υ";
var tj = "ϒ";
var nj = "ϒ";
var rj = "Υ";
var oj = "υ";
var sj = "↥";
var cj = "⊥";
var ij = "⇈";
var aj = "⌝";
var lj = "⌝";
var uj = "⌎";
var fj = "Ů";
var dj = "ů";
var pj = "◹";
var hj = "𝒰";
var mj = "𝓊";
var gj = "⋰";
var bj = "Ũ";
var vj = "ũ";
var yj = "▵";
var wj = "▴";
var kj = "⇈";
var xj = "Ü";
var _j = "ü";
var Aj = "⦧";
var Ej = "⦜";
var Cj = "ϵ";
var Dj = "ϰ";
var Sj = "∅";
var qj = "ϕ";
var Rj = "ϖ";
var Tj = "∝";
var Fj = "↕";
var Lj = "⇕";
var Nj = "ϱ";
var Ij = "ς";
var Bj = "⊊︀";
var Oj = "⫋︀";
var Pj = "⊋︀";
var zj = "⫌︀";
var Mj = "ϑ";
var Uj = "⊲";
var jj = "⊳";
var Vj = "⫨";
var Gj = "⫫";
var $j = "⫩";
var Hj = "В";
var Zj = "в";
var Wj = "⊢";
var Jj = "⊨";
var Qj = "⊩";
var Yj = "⊫";
var Xj = "⫦";
var Kj = "⊻";
var eV = "∨";
var tV = "⋁";
var nV = "≚";
var rV = "⋮";
var oV = "|";
var sV = "‖";
var cV = "|";
var iV = "‖";
var aV = "∣";
var lV = "|";
var uV = "❘";
var fV = "≀";
var dV = " ";
var pV = "𝔙";
var hV = "𝔳";
var mV = "⊲";
var gV = "⊂⃒";
var bV = "⊃⃒";
var vV = "𝕍";
var yV = "𝕧";
var wV = "∝";
var kV = "⊳";
var xV = "𝒱";
var _V = "𝓋";
var AV = "⫋︀";
var EV = "⊊︀";
var CV = "⫌︀";
var DV = "⊋︀";
var SV = "⊪";
var qV = "⦚";
var RV = "Ŵ";
var TV = "ŵ";
var FV = "⩟";
var LV = "∧";
var NV = "⋀";
var IV = "≙";
var BV = "℘";
var OV = "𝔚";
var PV = "𝔴";
var zV = "𝕎";
var MV = "𝕨";
var UV = "℘";
var jV = "≀";
var VV = "≀";
var GV = "𝒲";
var $V = "𝓌";
var HV = "⋂";
var ZV = "◯";
var WV = "⋃";
var JV = "▽";
var QV = "𝔛";
var YV = "𝔵";
var XV = "⟷";
var KV = "⟺";
var eG = "Ξ";
var tG = "ξ";
var nG = "⟵";
var rG = "⟸";
var oG = "⟼";
var sG = "⋻";
var cG = "⨀";
var iG = "𝕏";
var aG = "𝕩";
var lG = "⨁";
var uG = "⨂";
var fG = "⟶";
var dG = "⟹";
var pG = "𝒳";
var hG = "𝓍";
var mG = "⨆";
var gG = "⨄";
var bG = "△";
var vG = "⋁";
var yG = "⋀";
var wG = "Ý";
var kG = "ý";
var xG = "Я";
var _G = "я";
var AG = "Ŷ";
var EG = "ŷ";
var CG = "Ы";
var DG = "ы";
var SG = "¥";
var qG = "𝔜";
var RG = "𝔶";
var TG = "Ї";
var FG = "ї";
var LG = "𝕐";
var NG = "𝕪";
var IG = "𝒴";
var BG = "𝓎";
var OG = "Ю";
var PG = "ю";
var zG = "ÿ";
var MG = "Ÿ";
var UG = "Ź";
var jG = "ź";
var VG = "Ž";
var GG = "ž";
var $G = "З";
var HG = "з";
var ZG = "Ż";
var WG = "ż";
var JG = "ℨ";
var QG = "​";
var YG = "Ζ";
var XG = "ζ";
var KG = "𝔷";
var e$ = "ℨ";
var t$ = "Ж";
var n$ = "ж";
var r$ = "⇝";
var o$ = "𝕫";
var s$ = "ℤ";
var c$ = "𝒵";
var i$ = "𝓏";
var a$ = "‍";
var l$ = "‌";
var u$ = {
  Aacute: Hl,
  aacute: Zl,
  Abreve: Wl,
  abreve: Jl,
  ac: Ql,
  acd: Yl,
  acE: Xl,
  Acirc: Kl,
  acirc: eu,
  acute: tu,
  Acy: nu,
  acy: ru,
  AElig: ou,
  aelig: su,
  af: cu,
  Afr: iu,
  afr: au,
  Agrave: lu,
  agrave: uu,
  alefsym: fu,
  aleph: du,
  Alpha: pu,
  alpha: hu,
  Amacr: mu,
  amacr: gu,
  amalg: bu,
  amp: vu,
  AMP: yu,
  andand: wu,
  And: ku,
  and: xu,
  andd: _u,
  andslope: Au,
  andv: Eu,
  ang: Cu,
  ange: Du,
  angle: Su,
  angmsdaa: qu,
  angmsdab: Ru,
  angmsdac: Tu,
  angmsdad: Fu,
  angmsdae: Lu,
  angmsdaf: Nu,
  angmsdag: Iu,
  angmsdah: Bu,
  angmsd: Ou,
  angrt: Pu,
  angrtvb: zu,
  angrtvbd: Mu,
  angsph: Uu,
  angst: ju,
  angzarr: Vu,
  Aogon: Gu,
  aogon: $u,
  Aopf: Hu,
  aopf: Zu,
  apacir: Wu,
  ap: Ju,
  apE: Qu,
  ape: Yu,
  apid: Xu,
  apos: Ku,
  ApplyFunction: ef,
  approx: tf,
  approxeq: nf,
  Aring: rf,
  aring: of,
  Ascr: sf,
  ascr: cf,
  Assign: af,
  ast: lf,
  asymp: uf,
  asympeq: ff,
  Atilde: df,
  atilde: pf,
  Auml: hf,
  auml: mf,
  awconint: gf,
  awint: bf,
  backcong: vf,
  backepsilon: yf,
  backprime: wf,
  backsim: kf,
  backsimeq: xf,
  Backslash: _f,
  Barv: Af,
  barvee: Ef,
  barwed: Cf,
  Barwed: Df,
  barwedge: Sf,
  bbrk: qf,
  bbrktbrk: Rf,
  bcong: Tf,
  Bcy: Ff,
  bcy: Lf,
  bdquo: Nf,
  becaus: If,
  because: Bf,
  Because: Of,
  bemptyv: Pf,
  bepsi: zf,
  bernou: Mf,
  Bernoullis: Uf,
  Beta: jf,
  beta: Vf,
  beth: Gf,
  between: $f,
  Bfr: Hf,
  bfr: Zf,
  bigcap: Wf,
  bigcirc: Jf,
  bigcup: Qf,
  bigodot: Yf,
  bigoplus: Xf,
  bigotimes: Kf,
  bigsqcup: ed,
  bigstar: td,
  bigtriangledown: nd,
  bigtriangleup: rd,
  biguplus: od,
  bigvee: sd,
  bigwedge: cd,
  bkarow: id,
  blacklozenge: ad,
  blacksquare: ld,
  blacktriangle: ud,
  blacktriangledown: fd,
  blacktriangleleft: dd,
  blacktriangleright: pd,
  blank: hd,
  blk12: md,
  blk14: gd,
  blk34: bd,
  block: vd,
  bne: yd,
  bnequiv: wd,
  bNot: kd,
  bnot: xd,
  Bopf: _d,
  bopf: Ad,
  bot: Ed,
  bottom: Cd,
  bowtie: Dd,
  boxbox: Sd,
  boxdl: qd,
  boxdL: Rd,
  boxDl: Td,
  boxDL: Fd,
  boxdr: Ld,
  boxdR: Nd,
  boxDr: Id,
  boxDR: Bd,
  boxh: Od,
  boxH: Pd,
  boxhd: zd,
  boxHd: Md,
  boxhD: Ud,
  boxHD: jd,
  boxhu: Vd,
  boxHu: Gd,
  boxhU: $d,
  boxHU: Hd,
  boxminus: Zd,
  boxplus: Wd,
  boxtimes: Jd,
  boxul: Qd,
  boxuL: Yd,
  boxUl: Xd,
  boxUL: Kd,
  boxur: ep,
  boxuR: tp,
  boxUr: np,
  boxUR: rp,
  boxv: op,
  boxV: sp,
  boxvh: cp,
  boxvH: ip,
  boxVh: ap,
  boxVH: lp,
  boxvl: up,
  boxvL: fp,
  boxVl: dp,
  boxVL: pp,
  boxvr: hp,
  boxvR: mp,
  boxVr: gp,
  boxVR: bp,
  bprime: vp,
  breve: yp,
  Breve: wp,
  brvbar: kp,
  bscr: xp,
  Bscr: _p,
  bsemi: Ap,
  bsim: Ep,
  bsime: Cp,
  bsolb: Dp,
  bsol: Sp,
  bsolhsub: qp,
  bull: Rp,
  bullet: Tp,
  bump: Fp,
  bumpE: Lp,
  bumpe: Np,
  Bumpeq: Ip,
  bumpeq: Bp,
  Cacute: Op,
  cacute: Pp,
  capand: zp,
  capbrcup: Mp,
  capcap: Up,
  cap: jp,
  Cap: Vp,
  capcup: Gp,
  capdot: $p,
  CapitalDifferentialD: Hp,
  caps: Zp,
  caret: Wp,
  caron: Jp,
  Cayleys: Qp,
  ccaps: Yp,
  Ccaron: Xp,
  ccaron: Kp,
  Ccedil: eh,
  ccedil: th,
  Ccirc: nh,
  ccirc: rh,
  Cconint: oh,
  ccups: sh,
  ccupssm: ch,
  Cdot: ih,
  cdot: ah,
  cedil: lh,
  Cedilla: uh,
  cemptyv: fh,
  cent: dh,
  centerdot: ph,
  CenterDot: hh,
  cfr: mh,
  Cfr: gh,
  CHcy: bh,
  chcy: vh,
  check: yh,
  checkmark: wh,
  Chi: kh,
  chi: xh,
  circ: _h,
  circeq: Ah,
  circlearrowleft: Eh,
  circlearrowright: Ch,
  circledast: Dh,
  circledcirc: Sh,
  circleddash: qh,
  CircleDot: Rh,
  circledR: Th,
  circledS: Fh,
  CircleMinus: Lh,
  CirclePlus: Nh,
  CircleTimes: Ih,
  cir: Bh,
  cirE: Oh,
  cire: Ph,
  cirfnint: zh,
  cirmid: Mh,
  cirscir: Uh,
  ClockwiseContourIntegral: jh,
  CloseCurlyDoubleQuote: Vh,
  CloseCurlyQuote: Gh,
  clubs: $h,
  clubsuit: Hh,
  colon: Zh,
  Colon: Wh,
  Colone: Jh,
  colone: Qh,
  coloneq: Yh,
  comma: Xh,
  commat: Kh,
  comp: em,
  compfn: tm,
  complement: nm,
  complexes: rm,
  cong: om,
  congdot: sm,
  Congruent: cm,
  conint: im,
  Conint: am,
  ContourIntegral: lm,
  copf: um,
  Copf: fm,
  coprod: dm,
  Coproduct: pm,
  copy: hm,
  COPY: mm,
  copysr: gm,
  CounterClockwiseContourIntegral: bm,
  crarr: vm,
  cross: ym,
  Cross: wm,
  Cscr: km,
  cscr: xm,
  csub: _m,
  csube: Am,
  csup: Em,
  csupe: Cm,
  ctdot: Dm,
  cudarrl: Sm,
  cudarrr: qm,
  cuepr: Rm,
  cuesc: Tm,
  cularr: Fm,
  cularrp: Lm,
  cupbrcap: Nm,
  cupcap: Im,
  CupCap: Bm,
  cup: Om,
  Cup: Pm,
  cupcup: zm,
  cupdot: Mm,
  cupor: Um,
  cups: jm,
  curarr: Vm,
  curarrm: Gm,
  curlyeqprec: $m,
  curlyeqsucc: Hm,
  curlyvee: Zm,
  curlywedge: Wm,
  curren: Jm,
  curvearrowleft: Qm,
  curvearrowright: Ym,
  cuvee: Xm,
  cuwed: Km,
  cwconint: eg,
  cwint: tg,
  cylcty: ng,
  dagger: rg,
  Dagger: og,
  daleth: sg,
  darr: cg,
  Darr: ig,
  dArr: ag,
  dash: lg,
  Dashv: ug,
  dashv: fg,
  dbkarow: dg,
  dblac: pg,
  Dcaron: hg,
  dcaron: mg,
  Dcy: gg,
  dcy: bg,
  ddagger: vg,
  ddarr: yg,
  DD: wg,
  dd: kg,
  DDotrahd: xg,
  ddotseq: _g,
  deg: Ag,
  Del: Eg,
  Delta: Cg,
  delta: Dg,
  demptyv: Sg,
  dfisht: qg,
  Dfr: Rg,
  dfr: Tg,
  dHar: Fg,
  dharl: Lg,
  dharr: Ng,
  DiacriticalAcute: Ig,
  DiacriticalDot: Bg,
  DiacriticalDoubleAcute: Og,
  DiacriticalGrave: Pg,
  DiacriticalTilde: zg,
  diam: Mg,
  diamond: Ug,
  Diamond: jg,
  diamondsuit: Vg,
  diams: Gg,
  die: $g,
  DifferentialD: Hg,
  digamma: Zg,
  disin: Wg,
  div: Jg,
  divide: Qg,
  divideontimes: Yg,
  divonx: Xg,
  DJcy: Kg,
  djcy: eb,
  dlcorn: tb,
  dlcrop: nb,
  dollar: rb,
  Dopf: ob,
  dopf: sb,
  Dot: cb,
  dot: ib,
  DotDot: ab,
  doteq: lb,
  doteqdot: ub,
  DotEqual: fb,
  dotminus: db,
  dotplus: pb,
  dotsquare: hb,
  doublebarwedge: mb,
  DoubleContourIntegral: gb,
  DoubleDot: bb,
  DoubleDownArrow: vb,
  DoubleLeftArrow: yb,
  DoubleLeftRightArrow: wb,
  DoubleLeftTee: kb,
  DoubleLongLeftArrow: xb,
  DoubleLongLeftRightArrow: _b,
  DoubleLongRightArrow: Ab,
  DoubleRightArrow: Eb,
  DoubleRightTee: Cb,
  DoubleUpArrow: Db,
  DoubleUpDownArrow: Sb,
  DoubleVerticalBar: qb,
  DownArrowBar: Rb,
  downarrow: Tb,
  DownArrow: Fb,
  Downarrow: Lb,
  DownArrowUpArrow: Nb,
  DownBreve: Ib,
  downdownarrows: Bb,
  downharpoonleft: Ob,
  downharpoonright: Pb,
  DownLeftRightVector: zb,
  DownLeftTeeVector: Mb,
  DownLeftVectorBar: Ub,
  DownLeftVector: jb,
  DownRightTeeVector: Vb,
  DownRightVectorBar: Gb,
  DownRightVector: $b,
  DownTeeArrow: Hb,
  DownTee: Zb,
  drbkarow: Wb,
  drcorn: Jb,
  drcrop: Qb,
  Dscr: Yb,
  dscr: Xb,
  DScy: Kb,
  dscy: ev,
  dsol: tv,
  Dstrok: nv,
  dstrok: rv,
  dtdot: ov,
  dtri: sv,
  dtrif: cv,
  duarr: iv,
  duhar: av,
  dwangle: lv,
  DZcy: uv,
  dzcy: fv,
  dzigrarr: dv,
  Eacute: pv,
  eacute: hv,
  easter: mv,
  Ecaron: gv,
  ecaron: bv,
  Ecirc: vv,
  ecirc: yv,
  ecir: wv,
  ecolon: kv,
  Ecy: xv,
  ecy: _v,
  eDDot: Av,
  Edot: Ev,
  edot: Cv,
  eDot: Dv,
  ee: Sv,
  efDot: qv,
  Efr: Rv,
  efr: Tv,
  eg: Fv,
  Egrave: Lv,
  egrave: Nv,
  egs: Iv,
  egsdot: Bv,
  el: Ov,
  Element: Pv,
  elinters: zv,
  ell: Mv,
  els: Uv,
  elsdot: jv,
  Emacr: Vv,
  emacr: Gv,
  empty: $v,
  emptyset: Hv,
  EmptySmallSquare: Zv,
  emptyv: Wv,
  EmptyVerySmallSquare: Jv,
  emsp13: Qv,
  emsp14: Yv,
  emsp: Xv,
  ENG: Kv,
  eng: e0,
  ensp: t0,
  Eogon: n0,
  eogon: r0,
  Eopf: o0,
  eopf: s0,
  epar: c0,
  eparsl: i0,
  eplus: a0,
  epsi: l0,
  Epsilon: u0,
  epsilon: f0,
  epsiv: d0,
  eqcirc: p0,
  eqcolon: h0,
  eqsim: m0,
  eqslantgtr: g0,
  eqslantless: b0,
  Equal: v0,
  equals: y0,
  EqualTilde: w0,
  equest: k0,
  Equilibrium: x0,
  equiv: _0,
  equivDD: A0,
  eqvparsl: E0,
  erarr: C0,
  erDot: D0,
  escr: S0,
  Escr: q0,
  esdot: R0,
  Esim: T0,
  esim: F0,
  Eta: L0,
  eta: N0,
  ETH: I0,
  eth: B0,
  Euml: O0,
  euml: P0,
  euro: z0,
  excl: M0,
  exist: U0,
  Exists: j0,
  expectation: V0,
  exponentiale: G0,
  ExponentialE: $0,
  fallingdotseq: H0,
  Fcy: Z0,
  fcy: W0,
  female: J0,
  ffilig: Q0,
  fflig: Y0,
  ffllig: X0,
  Ffr: K0,
  ffr: ey,
  filig: ty,
  FilledSmallSquare: ny,
  FilledVerySmallSquare: ry,
  fjlig: oy,
  flat: sy,
  fllig: cy,
  fltns: iy,
  fnof: ay,
  Fopf: ly,
  fopf: uy,
  forall: fy,
  ForAll: dy,
  fork: py,
  forkv: hy,
  Fouriertrf: my,
  fpartint: gy,
  frac12: by,
  frac13: vy,
  frac14: yy,
  frac15: wy,
  frac16: ky,
  frac18: xy,
  frac23: _y,
  frac25: Ay,
  frac34: Ey,
  frac35: Cy,
  frac38: Dy,
  frac45: Sy,
  frac56: qy,
  frac58: Ry,
  frac78: Ty,
  frasl: Fy,
  frown: Ly,
  fscr: Ny,
  Fscr: Iy,
  gacute: By,
  Gamma: Oy,
  gamma: Py,
  Gammad: zy,
  gammad: My,
  gap: Uy,
  Gbreve: jy,
  gbreve: Vy,
  Gcedil: Gy,
  Gcirc: $y,
  gcirc: Hy,
  Gcy: Zy,
  gcy: Wy,
  Gdot: Jy,
  gdot: Qy,
  ge: Yy,
  gE: Xy,
  gEl: Ky,
  gel: ew,
  geq: tw,
  geqq: nw,
  geqslant: rw,
  gescc: ow,
  ges: sw,
  gesdot: cw,
  gesdoto: iw,
  gesdotol: aw,
  gesl: lw,
  gesles: uw,
  Gfr: fw,
  gfr: dw,
  gg: pw,
  Gg: hw,
  ggg: mw,
  gimel: gw,
  GJcy: bw,
  gjcy: vw,
  gla: yw,
  gl: ww,
  glE: kw,
  glj: xw,
  gnap: _w,
  gnapprox: Aw,
  gne: Ew,
  gnE: Cw,
  gneq: Dw,
  gneqq: Sw,
  gnsim: qw,
  Gopf: Rw,
  gopf: Tw,
  grave: Fw,
  GreaterEqual: Lw,
  GreaterEqualLess: Nw,
  GreaterFullEqual: Iw,
  GreaterGreater: Bw,
  GreaterLess: Ow,
  GreaterSlantEqual: Pw,
  GreaterTilde: zw,
  Gscr: Mw,
  gscr: Uw,
  gsim: jw,
  gsime: Vw,
  gsiml: Gw,
  gtcc: $w,
  gtcir: Hw,
  gt: Zw,
  GT: Ww,
  Gt: Jw,
  gtdot: Qw,
  gtlPar: Yw,
  gtquest: Xw,
  gtrapprox: Kw,
  gtrarr: ek,
  gtrdot: tk,
  gtreqless: nk,
  gtreqqless: rk,
  gtrless: ok,
  gtrsim: sk,
  gvertneqq: ck,
  gvnE: ik,
  Hacek: ak,
  hairsp: lk,
  half: uk,
  hamilt: fk,
  HARDcy: dk,
  hardcy: pk,
  harrcir: hk,
  harr: mk,
  hArr: gk,
  harrw: bk,
  Hat: vk,
  hbar: yk,
  Hcirc: wk,
  hcirc: kk,
  hearts: xk,
  heartsuit: _k,
  hellip: Ak,
  hercon: Ek,
  hfr: Ck,
  Hfr: Dk,
  HilbertSpace: Sk,
  hksearow: qk,
  hkswarow: Rk,
  hoarr: Tk,
  homtht: Fk,
  hookleftarrow: Lk,
  hookrightarrow: Nk,
  hopf: Ik,
  Hopf: Bk,
  horbar: Ok,
  HorizontalLine: Pk,
  hscr: zk,
  Hscr: Mk,
  hslash: Uk,
  Hstrok: jk,
  hstrok: Vk,
  HumpDownHump: Gk,
  HumpEqual: $k,
  hybull: Hk,
  hyphen: Zk,
  Iacute: Wk,
  iacute: Jk,
  ic: Qk,
  Icirc: Yk,
  icirc: Xk,
  Icy: Kk,
  icy: ex,
  Idot: tx,
  IEcy: nx,
  iecy: rx,
  iexcl: ox,
  iff: sx,
  ifr: cx,
  Ifr: ix,
  Igrave: ax,
  igrave: lx,
  ii: ux,
  iiiint: fx,
  iiint: dx,
  iinfin: px,
  iiota: hx,
  IJlig: mx,
  ijlig: gx,
  Imacr: bx,
  imacr: vx,
  image: yx,
  ImaginaryI: wx,
  imagline: kx,
  imagpart: xx,
  imath: _x,
  Im: Ax,
  imof: Ex,
  imped: Cx,
  Implies: Dx,
  incare: Sx,
  in: "∈",
  infin: qx,
  infintie: Rx,
  inodot: Tx,
  intcal: Fx,
  int: Lx,
  Int: Nx,
  integers: Ix,
  Integral: Bx,
  intercal: Ox,
  Intersection: Px,
  intlarhk: zx,
  intprod: Mx,
  InvisibleComma: Ux,
  InvisibleTimes: jx,
  IOcy: Vx,
  iocy: Gx,
  Iogon: $x,
  iogon: Hx,
  Iopf: Zx,
  iopf: Wx,
  Iota: Jx,
  iota: Qx,
  iprod: Yx,
  iquest: Xx,
  iscr: Kx,
  Iscr: e_,
  isin: t_,
  isindot: n_,
  isinE: r_,
  isins: o_,
  isinsv: s_,
  isinv: c_,
  it: i_,
  Itilde: a_,
  itilde: l_,
  Iukcy: u_,
  iukcy: f_,
  Iuml: d_,
  iuml: p_,
  Jcirc: h_,
  jcirc: m_,
  Jcy: g_,
  jcy: b_,
  Jfr: v_,
  jfr: y_,
  jmath: w_,
  Jopf: k_,
  jopf: x_,
  Jscr: __,
  jscr: A_,
  Jsercy: E_,
  jsercy: C_,
  Jukcy: D_,
  jukcy: S_,
  Kappa: q_,
  kappa: R_,
  kappav: T_,
  Kcedil: F_,
  kcedil: L_,
  Kcy: N_,
  kcy: I_,
  Kfr: B_,
  kfr: O_,
  kgreen: P_,
  KHcy: z_,
  khcy: M_,
  KJcy: U_,
  kjcy: j_,
  Kopf: V_,
  kopf: G_,
  Kscr: $_,
  kscr: H_,
  lAarr: Z_,
  Lacute: W_,
  lacute: J_,
  laemptyv: Q_,
  lagran: Y_,
  Lambda: X_,
  lambda: K_,
  lang: eA,
  Lang: tA,
  langd: nA,
  langle: rA,
  lap: oA,
  Laplacetrf: sA,
  laquo: cA,
  larrb: iA,
  larrbfs: aA,
  larr: lA,
  Larr: uA,
  lArr: fA,
  larrfs: dA,
  larrhk: pA,
  larrlp: hA,
  larrpl: mA,
  larrsim: gA,
  larrtl: bA,
  latail: vA,
  lAtail: yA,
  lat: wA,
  late: kA,
  lates: xA,
  lbarr: _A,
  lBarr: AA,
  lbbrk: EA,
  lbrace: CA,
  lbrack: DA,
  lbrke: SA,
  lbrksld: qA,
  lbrkslu: RA,
  Lcaron: TA,
  lcaron: FA,
  Lcedil: LA,
  lcedil: NA,
  lceil: IA,
  lcub: BA,
  Lcy: OA,
  lcy: PA,
  ldca: zA,
  ldquo: MA,
  ldquor: UA,
  ldrdhar: jA,
  ldrushar: VA,
  ldsh: GA,
  le: $A,
  lE: HA,
  LeftAngleBracket: ZA,
  LeftArrowBar: WA,
  leftarrow: JA,
  LeftArrow: QA,
  Leftarrow: YA,
  LeftArrowRightArrow: XA,
  leftarrowtail: KA,
  LeftCeiling: eE,
  LeftDoubleBracket: tE,
  LeftDownTeeVector: nE,
  LeftDownVectorBar: rE,
  LeftDownVector: oE,
  LeftFloor: sE,
  leftharpoondown: cE,
  leftharpoonup: iE,
  leftleftarrows: aE,
  leftrightarrow: lE,
  LeftRightArrow: uE,
  Leftrightarrow: fE,
  leftrightarrows: dE,
  leftrightharpoons: pE,
  leftrightsquigarrow: hE,
  LeftRightVector: mE,
  LeftTeeArrow: gE,
  LeftTee: bE,
  LeftTeeVector: vE,
  leftthreetimes: yE,
  LeftTriangleBar: wE,
  LeftTriangle: kE,
  LeftTriangleEqual: xE,
  LeftUpDownVector: _E,
  LeftUpTeeVector: AE,
  LeftUpVectorBar: EE,
  LeftUpVector: CE,
  LeftVectorBar: DE,
  LeftVector: SE,
  lEg: qE,
  leg: RE,
  leq: TE,
  leqq: FE,
  leqslant: LE,
  lescc: NE,
  les: IE,
  lesdot: BE,
  lesdoto: OE,
  lesdotor: PE,
  lesg: zE,
  lesges: ME,
  lessapprox: UE,
  lessdot: jE,
  lesseqgtr: VE,
  lesseqqgtr: GE,
  LessEqualGreater: $E,
  LessFullEqual: HE,
  LessGreater: ZE,
  lessgtr: WE,
  LessLess: JE,
  lesssim: QE,
  LessSlantEqual: YE,
  LessTilde: XE,
  lfisht: KE,
  lfloor: e1,
  Lfr: t1,
  lfr: n1,
  lg: r1,
  lgE: o1,
  lHar: s1,
  lhard: c1,
  lharu: i1,
  lharul: a1,
  lhblk: l1,
  LJcy: u1,
  ljcy: f1,
  llarr: d1,
  ll: p1,
  Ll: h1,
  llcorner: m1,
  Lleftarrow: g1,
  llhard: b1,
  lltri: v1,
  Lmidot: y1,
  lmidot: w1,
  lmoustache: k1,
  lmoust: x1,
  lnap: _1,
  lnapprox: A1,
  lne: E1,
  lnE: C1,
  lneq: D1,
  lneqq: S1,
  lnsim: q1,
  loang: R1,
  loarr: T1,
  lobrk: F1,
  longleftarrow: L1,
  LongLeftArrow: N1,
  Longleftarrow: I1,
  longleftrightarrow: B1,
  LongLeftRightArrow: O1,
  Longleftrightarrow: P1,
  longmapsto: z1,
  longrightarrow: M1,
  LongRightArrow: U1,
  Longrightarrow: j1,
  looparrowleft: V1,
  looparrowright: G1,
  lopar: $1,
  Lopf: H1,
  lopf: Z1,
  loplus: W1,
  lotimes: J1,
  lowast: Q1,
  lowbar: Y1,
  LowerLeftArrow: X1,
  LowerRightArrow: K1,
  loz: eC,
  lozenge: tC,
  lozf: nC,
  lpar: rC,
  lparlt: oC,
  lrarr: sC,
  lrcorner: cC,
  lrhar: iC,
  lrhard: aC,
  lrm: lC,
  lrtri: uC,
  lsaquo: fC,
  lscr: dC,
  Lscr: pC,
  lsh: hC,
  Lsh: mC,
  lsim: gC,
  lsime: bC,
  lsimg: vC,
  lsqb: yC,
  lsquo: wC,
  lsquor: kC,
  Lstrok: xC,
  lstrok: _C,
  ltcc: AC,
  ltcir: EC,
  lt: CC,
  LT: DC,
  Lt: SC,
  ltdot: qC,
  lthree: RC,
  ltimes: TC,
  ltlarr: FC,
  ltquest: LC,
  ltri: NC,
  ltrie: IC,
  ltrif: BC,
  ltrPar: OC,
  lurdshar: PC,
  luruhar: zC,
  lvertneqq: MC,
  lvnE: UC,
  macr: jC,
  male: VC,
  malt: GC,
  maltese: $C,
  Map: "⤅",
  map: HC,
  mapsto: ZC,
  mapstodown: WC,
  mapstoleft: JC,
  mapstoup: QC,
  marker: YC,
  mcomma: XC,
  Mcy: KC,
  mcy: eD,
  mdash: tD,
  mDDot: nD,
  measuredangle: rD,
  MediumSpace: oD,
  Mellintrf: sD,
  Mfr: cD,
  mfr: iD,
  mho: aD,
  micro: lD,
  midast: uD,
  midcir: fD,
  mid: dD,
  middot: pD,
  minusb: hD,
  minus: mD,
  minusd: gD,
  minusdu: bD,
  MinusPlus: vD,
  mlcp: yD,
  mldr: wD,
  mnplus: kD,
  models: xD,
  Mopf: _D,
  mopf: AD,
  mp: ED,
  mscr: CD,
  Mscr: DD,
  mstpos: SD,
  Mu: qD,
  mu: RD,
  multimap: TD,
  mumap: FD,
  nabla: LD,
  Nacute: ND,
  nacute: ID,
  nang: BD,
  nap: OD,
  napE: PD,
  napid: zD,
  napos: MD,
  napprox: UD,
  natural: jD,
  naturals: VD,
  natur: GD,
  nbsp: $D,
  nbump: HD,
  nbumpe: ZD,
  ncap: WD,
  Ncaron: JD,
  ncaron: QD,
  Ncedil: YD,
  ncedil: XD,
  ncong: KD,
  ncongdot: eS,
  ncup: tS,
  Ncy: nS,
  ncy: rS,
  ndash: oS,
  nearhk: sS,
  nearr: cS,
  neArr: iS,
  nearrow: aS,
  ne: lS,
  nedot: uS,
  NegativeMediumSpace: fS,
  NegativeThickSpace: dS,
  NegativeThinSpace: pS,
  NegativeVeryThinSpace: hS,
  nequiv: mS,
  nesear: gS,
  nesim: bS,
  NestedGreaterGreater: vS,
  NestedLessLess: yS,
  NewLine: wS,
  nexist: kS,
  nexists: xS,
  Nfr: _S,
  nfr: AS,
  ngE: ES,
  nge: CS,
  ngeq: DS,
  ngeqq: SS,
  ngeqslant: qS,
  nges: RS,
  nGg: TS,
  ngsim: FS,
  nGt: LS,
  ngt: NS,
  ngtr: IS,
  nGtv: BS,
  nharr: OS,
  nhArr: PS,
  nhpar: zS,
  ni: MS,
  nis: US,
  nisd: jS,
  niv: VS,
  NJcy: GS,
  njcy: $S,
  nlarr: HS,
  nlArr: ZS,
  nldr: WS,
  nlE: JS,
  nle: QS,
  nleftarrow: YS,
  nLeftarrow: XS,
  nleftrightarrow: KS,
  nLeftrightarrow: eq,
  nleq: tq,
  nleqq: nq,
  nleqslant: rq,
  nles: oq,
  nless: sq,
  nLl: cq,
  nlsim: iq,
  nLt: aq,
  nlt: lq,
  nltri: uq,
  nltrie: fq,
  nLtv: dq,
  nmid: pq,
  NoBreak: hq,
  NonBreakingSpace: mq,
  nopf: gq,
  Nopf: bq,
  Not: vq,
  not: yq,
  NotCongruent: wq,
  NotCupCap: kq,
  NotDoubleVerticalBar: xq,
  NotElement: _q,
  NotEqual: Aq,
  NotEqualTilde: Eq,
  NotExists: Cq,
  NotGreater: Dq,
  NotGreaterEqual: Sq,
  NotGreaterFullEqual: qq,
  NotGreaterGreater: Rq,
  NotGreaterLess: Tq,
  NotGreaterSlantEqual: Fq,
  NotGreaterTilde: Lq,
  NotHumpDownHump: Nq,
  NotHumpEqual: Iq,
  notin: Bq,
  notindot: Oq,
  notinE: Pq,
  notinva: zq,
  notinvb: Mq,
  notinvc: Uq,
  NotLeftTriangleBar: jq,
  NotLeftTriangle: Vq,
  NotLeftTriangleEqual: Gq,
  NotLess: $q,
  NotLessEqual: Hq,
  NotLessGreater: Zq,
  NotLessLess: Wq,
  NotLessSlantEqual: Jq,
  NotLessTilde: Qq,
  NotNestedGreaterGreater: Yq,
  NotNestedLessLess: Xq,
  notni: Kq,
  notniva: eR,
  notnivb: tR,
  notnivc: nR,
  NotPrecedes: rR,
  NotPrecedesEqual: oR,
  NotPrecedesSlantEqual: sR,
  NotReverseElement: cR,
  NotRightTriangleBar: iR,
  NotRightTriangle: aR,
  NotRightTriangleEqual: lR,
  NotSquareSubset: uR,
  NotSquareSubsetEqual: fR,
  NotSquareSuperset: dR,
  NotSquareSupersetEqual: pR,
  NotSubset: hR,
  NotSubsetEqual: mR,
  NotSucceeds: gR,
  NotSucceedsEqual: bR,
  NotSucceedsSlantEqual: vR,
  NotSucceedsTilde: yR,
  NotSuperset: wR,
  NotSupersetEqual: kR,
  NotTilde: xR,
  NotTildeEqual: _R,
  NotTildeFullEqual: AR,
  NotTildeTilde: ER,
  NotVerticalBar: CR,
  nparallel: DR,
  npar: SR,
  nparsl: qR,
  npart: RR,
  npolint: TR,
  npr: FR,
  nprcue: LR,
  nprec: NR,
  npreceq: IR,
  npre: BR,
  nrarrc: OR,
  nrarr: PR,
  nrArr: zR,
  nrarrw: MR,
  nrightarrow: UR,
  nRightarrow: jR,
  nrtri: VR,
  nrtrie: GR,
  nsc: $R,
  nsccue: HR,
  nsce: ZR,
  Nscr: WR,
  nscr: JR,
  nshortmid: QR,
  nshortparallel: YR,
  nsim: XR,
  nsime: KR,
  nsimeq: eT,
  nsmid: tT,
  nspar: nT,
  nsqsube: rT,
  nsqsupe: oT,
  nsub: sT,
  nsubE: cT,
  nsube: iT,
  nsubset: aT,
  nsubseteq: lT,
  nsubseteqq: uT,
  nsucc: fT,
  nsucceq: dT,
  nsup: pT,
  nsupE: hT,
  nsupe: mT,
  nsupset: gT,
  nsupseteq: bT,
  nsupseteqq: vT,
  ntgl: yT,
  Ntilde: wT,
  ntilde: kT,
  ntlg: xT,
  ntriangleleft: _T,
  ntrianglelefteq: AT,
  ntriangleright: ET,
  ntrianglerighteq: CT,
  Nu: DT,
  nu: ST,
  num: qT,
  numero: RT,
  numsp: TT,
  nvap: FT,
  nvdash: LT,
  nvDash: NT,
  nVdash: IT,
  nVDash: BT,
  nvge: OT,
  nvgt: PT,
  nvHarr: zT,
  nvinfin: MT,
  nvlArr: UT,
  nvle: jT,
  nvlt: VT,
  nvltrie: GT,
  nvrArr: $T,
  nvrtrie: HT,
  nvsim: ZT,
  nwarhk: WT,
  nwarr: JT,
  nwArr: QT,
  nwarrow: YT,
  nwnear: XT,
  Oacute: KT,
  oacute: eF,
  oast: tF,
  Ocirc: nF,
  ocirc: rF,
  ocir: oF,
  Ocy: sF,
  ocy: cF,
  odash: iF,
  Odblac: aF,
  odblac: lF,
  odiv: uF,
  odot: fF,
  odsold: dF,
  OElig: pF,
  oelig: hF,
  ofcir: mF,
  Ofr: gF,
  ofr: bF,
  ogon: vF,
  Ograve: yF,
  ograve: wF,
  ogt: kF,
  ohbar: xF,
  ohm: _F,
  oint: AF,
  olarr: EF,
  olcir: CF,
  olcross: DF,
  oline: SF,
  olt: qF,
  Omacr: RF,
  omacr: TF,
  Omega: FF,
  omega: LF,
  Omicron: NF,
  omicron: IF,
  omid: BF,
  ominus: OF,
  Oopf: PF,
  oopf: zF,
  opar: MF,
  OpenCurlyDoubleQuote: UF,
  OpenCurlyQuote: jF,
  operp: VF,
  oplus: GF,
  orarr: $F,
  Or: HF,
  or: ZF,
  ord: WF,
  order: JF,
  orderof: QF,
  ordf: YF,
  ordm: XF,
  origof: KF,
  oror: eL,
  orslope: tL,
  orv: nL,
  oS: rL,
  Oscr: oL,
  oscr: sL,
  Oslash: cL,
  oslash: iL,
  osol: aL,
  Otilde: lL,
  otilde: uL,
  otimesas: fL,
  Otimes: dL,
  otimes: pL,
  Ouml: hL,
  ouml: mL,
  ovbar: gL,
  OverBar: bL,
  OverBrace: vL,
  OverBracket: yL,
  OverParenthesis: wL,
  para: kL,
  parallel: xL,
  par: _L,
  parsim: AL,
  parsl: EL,
  part: CL,
  PartialD: DL,
  Pcy: SL,
  pcy: qL,
  percnt: RL,
  period: TL,
  permil: FL,
  perp: LL,
  pertenk: NL,
  Pfr: IL,
  pfr: BL,
  Phi: OL,
  phi: PL,
  phiv: zL,
  phmmat: ML,
  phone: UL,
  Pi: jL,
  pi: VL,
  pitchfork: GL,
  piv: $L,
  planck: HL,
  planckh: ZL,
  plankv: WL,
  plusacir: JL,
  plusb: QL,
  pluscir: YL,
  plus: XL,
  plusdo: KL,
  plusdu: e2,
  pluse: t22,
  PlusMinus: n2,
  plusmn: r2,
  plussim: o2,
  plustwo: s2,
  pm: c2,
  Poincareplane: i2,
  pointint: a2,
  popf: l2,
  Popf: u2,
  pound: f2,
  prap: d2,
  Pr: p2,
  pr: h2,
  prcue: m2,
  precapprox: g2,
  prec: b2,
  preccurlyeq: v2,
  Precedes: y2,
  PrecedesEqual: w2,
  PrecedesSlantEqual: k2,
  PrecedesTilde: x2,
  preceq: _2,
  precnapprox: A2,
  precneqq: E2,
  precnsim: C2,
  pre: D2,
  prE: S2,
  precsim: q2,
  prime: R2,
  Prime: T2,
  primes: F2,
  prnap: L2,
  prnE: N2,
  prnsim: I2,
  prod: B2,
  Product: O2,
  profalar: P2,
  profline: z2,
  profsurf: M2,
  prop: U2,
  Proportional: j2,
  Proportion: V2,
  propto: G2,
  prsim: $2,
  prurel: H2,
  Pscr: Z2,
  pscr: W2,
  Psi: J2,
  psi: Q2,
  puncsp: Y2,
  Qfr: X2,
  qfr: K2,
  qint: eN,
  qopf: tN,
  Qopf: nN,
  qprime: rN,
  Qscr: oN,
  qscr: sN,
  quaternions: cN,
  quatint: iN,
  quest: aN,
  questeq: lN,
  quot: uN,
  QUOT: fN,
  rAarr: dN,
  race: pN,
  Racute: hN,
  racute: mN,
  radic: gN,
  raemptyv: bN,
  rang: vN,
  Rang: yN,
  rangd: wN,
  range: kN,
  rangle: xN,
  raquo: _N,
  rarrap: AN,
  rarrb: EN,
  rarrbfs: CN,
  rarrc: DN,
  rarr: SN,
  Rarr: qN,
  rArr: RN,
  rarrfs: TN,
  rarrhk: FN,
  rarrlp: LN,
  rarrpl: NN,
  rarrsim: IN,
  Rarrtl: BN,
  rarrtl: ON,
  rarrw: PN,
  ratail: zN,
  rAtail: MN,
  ratio: UN,
  rationals: jN,
  rbarr: VN,
  rBarr: GN,
  RBarr: $N,
  rbbrk: HN,
  rbrace: ZN,
  rbrack: WN,
  rbrke: JN,
  rbrksld: QN,
  rbrkslu: YN,
  Rcaron: XN,
  rcaron: KN,
  Rcedil: eI,
  rcedil: tI,
  rceil: nI,
  rcub: rI,
  Rcy: oI,
  rcy: sI,
  rdca: cI,
  rdldhar: iI,
  rdquo: aI,
  rdquor: lI,
  rdsh: uI,
  real: fI,
  realine: dI,
  realpart: pI,
  reals: hI,
  Re: mI,
  rect: gI,
  reg: bI,
  REG: vI,
  ReverseElement: yI,
  ReverseEquilibrium: wI,
  ReverseUpEquilibrium: kI,
  rfisht: xI,
  rfloor: _I,
  rfr: AI,
  Rfr: EI,
  rHar: CI,
  rhard: DI,
  rharu: SI,
  rharul: qI,
  Rho: RI,
  rho: TI,
  rhov: FI,
  RightAngleBracket: LI,
  RightArrowBar: NI,
  rightarrow: II,
  RightArrow: BI,
  Rightarrow: OI,
  RightArrowLeftArrow: PI,
  rightarrowtail: zI,
  RightCeiling: MI,
  RightDoubleBracket: UI,
  RightDownTeeVector: jI,
  RightDownVectorBar: VI,
  RightDownVector: GI,
  RightFloor: $I,
  rightharpoondown: HI,
  rightharpoonup: ZI,
  rightleftarrows: WI,
  rightleftharpoons: JI,
  rightrightarrows: QI,
  rightsquigarrow: YI,
  RightTeeArrow: XI,
  RightTee: KI,
  RightTeeVector: eB,
  rightthreetimes: tB,
  RightTriangleBar: nB,
  RightTriangle: rB,
  RightTriangleEqual: oB,
  RightUpDownVector: sB,
  RightUpTeeVector: cB,
  RightUpVectorBar: iB,
  RightUpVector: aB,
  RightVectorBar: lB,
  RightVector: uB,
  ring: fB,
  risingdotseq: dB,
  rlarr: pB,
  rlhar: hB,
  rlm: mB,
  rmoustache: gB,
  rmoust: bB,
  rnmid: vB,
  roang: yB,
  roarr: wB,
  robrk: kB,
  ropar: xB,
  ropf: _B,
  Ropf: AB,
  roplus: EB,
  rotimes: CB,
  RoundImplies: DB,
  rpar: SB,
  rpargt: qB,
  rppolint: RB,
  rrarr: TB,
  Rrightarrow: FB,
  rsaquo: LB,
  rscr: NB,
  Rscr: IB,
  rsh: BB,
  Rsh: OB,
  rsqb: PB,
  rsquo: zB,
  rsquor: MB,
  rthree: UB,
  rtimes: jB,
  rtri: VB,
  rtrie: GB,
  rtrif: $B,
  rtriltri: HB,
  RuleDelayed: ZB,
  ruluhar: WB,
  rx: JB,
  Sacute: QB,
  sacute: YB,
  sbquo: XB,
  scap: KB,
  Scaron: eO,
  scaron: tO,
  Sc: nO,
  sc: rO,
  sccue: oO,
  sce: sO,
  scE: cO,
  Scedil: iO,
  scedil: aO,
  Scirc: lO,
  scirc: uO,
  scnap: fO,
  scnE: dO,
  scnsim: pO,
  scpolint: hO,
  scsim: mO,
  Scy: gO,
  scy: bO,
  sdotb: vO,
  sdot: yO,
  sdote: wO,
  searhk: kO,
  searr: xO,
  seArr: _O,
  searrow: AO,
  sect: EO,
  semi: CO,
  seswar: DO,
  setminus: SO,
  setmn: qO,
  sext: RO,
  Sfr: TO,
  sfr: FO,
  sfrown: LO,
  sharp: NO,
  SHCHcy: IO,
  shchcy: BO,
  SHcy: OO,
  shcy: PO,
  ShortDownArrow: zO,
  ShortLeftArrow: MO,
  shortmid: UO,
  shortparallel: jO,
  ShortRightArrow: VO,
  ShortUpArrow: GO,
  shy: $O,
  Sigma: HO,
  sigma: ZO,
  sigmaf: WO,
  sigmav: JO,
  sim: QO,
  simdot: YO,
  sime: XO,
  simeq: KO,
  simg: eP,
  simgE: tP,
  siml: nP,
  simlE: rP,
  simne: oP,
  simplus: sP,
  simrarr: cP,
  slarr: iP,
  SmallCircle: aP,
  smallsetminus: lP,
  smashp: uP,
  smeparsl: fP,
  smid: dP,
  smile: pP,
  smt: hP,
  smte: mP,
  smtes: gP,
  SOFTcy: bP,
  softcy: vP,
  solbar: yP,
  solb: wP,
  sol: kP,
  Sopf: xP,
  sopf: _P,
  spades: AP,
  spadesuit: EP,
  spar: CP,
  sqcap: DP,
  sqcaps: SP,
  sqcup: qP,
  sqcups: RP,
  Sqrt: TP,
  sqsub: FP,
  sqsube: LP,
  sqsubset: NP,
  sqsubseteq: IP,
  sqsup: BP,
  sqsupe: OP,
  sqsupset: PP,
  sqsupseteq: zP,
  square: MP,
  Square: UP,
  SquareIntersection: jP,
  SquareSubset: VP,
  SquareSubsetEqual: GP,
  SquareSuperset: $P,
  SquareSupersetEqual: HP,
  SquareUnion: ZP,
  squarf: WP,
  squ: JP,
  squf: QP,
  srarr: YP,
  Sscr: XP,
  sscr: KP,
  ssetmn: ez,
  ssmile: tz,
  sstarf: nz,
  Star: rz,
  star: oz,
  starf: sz,
  straightepsilon: cz,
  straightphi: iz,
  strns: az,
  sub: lz,
  Sub: uz,
  subdot: fz,
  subE: dz,
  sube: pz,
  subedot: hz,
  submult: mz,
  subnE: gz,
  subne: bz,
  subplus: vz,
  subrarr: yz,
  subset: wz,
  Subset: kz,
  subseteq: xz,
  subseteqq: _z,
  SubsetEqual: Az,
  subsetneq: Ez,
  subsetneqq: Cz,
  subsim: Dz,
  subsub: Sz,
  subsup: qz,
  succapprox: Rz,
  succ: Tz,
  succcurlyeq: Fz,
  Succeeds: Lz,
  SucceedsEqual: Nz,
  SucceedsSlantEqual: Iz,
  SucceedsTilde: Bz,
  succeq: Oz,
  succnapprox: Pz,
  succneqq: zz,
  succnsim: Mz,
  succsim: Uz,
  SuchThat: jz,
  sum: Vz,
  Sum: Gz,
  sung: $z,
  sup1: Hz,
  sup2: Zz,
  sup3: Wz,
  sup: Jz,
  Sup: Qz,
  supdot: Yz,
  supdsub: Xz,
  supE: Kz,
  supe: eM,
  supedot: tM,
  Superset: nM,
  SupersetEqual: rM,
  suphsol: oM,
  suphsub: sM,
  suplarr: cM,
  supmult: iM,
  supnE: aM,
  supne: lM,
  supplus: uM,
  supset: fM,
  Supset: dM,
  supseteq: pM,
  supseteqq: hM,
  supsetneq: mM,
  supsetneqq: gM,
  supsim: bM,
  supsub: vM,
  supsup: yM,
  swarhk: wM,
  swarr: kM,
  swArr: xM,
  swarrow: _M,
  swnwar: AM,
  szlig: EM,
  Tab: CM,
  target: DM,
  Tau: SM,
  tau: qM,
  tbrk: RM,
  Tcaron: TM,
  tcaron: FM,
  Tcedil: LM,
  tcedil: NM,
  Tcy: IM,
  tcy: BM,
  tdot: OM,
  telrec: PM,
  Tfr: zM,
  tfr: MM,
  there4: UM,
  therefore: jM,
  Therefore: VM,
  Theta: GM,
  theta: $M,
  thetasym: HM,
  thetav: ZM,
  thickapprox: WM,
  thicksim: JM,
  ThickSpace: QM,
  ThinSpace: YM,
  thinsp: XM,
  thkap: KM,
  thksim: eU,
  THORN: tU,
  thorn: nU,
  tilde: rU,
  Tilde: oU,
  TildeEqual: sU,
  TildeFullEqual: cU,
  TildeTilde: iU,
  timesbar: aU,
  timesb: lU,
  times: uU,
  timesd: fU,
  tint: dU,
  toea: pU,
  topbot: hU,
  topcir: mU,
  top: gU,
  Topf: bU,
  topf: vU,
  topfork: yU,
  tosa: wU,
  tprime: kU,
  trade: xU,
  TRADE: _U,
  triangle: AU,
  triangledown: EU,
  triangleleft: CU,
  trianglelefteq: DU,
  triangleq: SU,
  triangleright: qU,
  trianglerighteq: RU,
  tridot: TU,
  trie: FU,
  triminus: LU,
  TripleDot: NU,
  triplus: IU,
  trisb: BU,
  tritime: OU,
  trpezium: PU,
  Tscr: zU,
  tscr: MU,
  TScy: UU,
  tscy: jU,
  TSHcy: VU,
  tshcy: GU,
  Tstrok: $U,
  tstrok: HU,
  twixt: ZU,
  twoheadleftarrow: WU,
  twoheadrightarrow: JU,
  Uacute: QU,
  uacute: YU,
  uarr: XU,
  Uarr: KU,
  uArr: e3,
  Uarrocir: t32,
  Ubrcy: n3,
  ubrcy: r3,
  Ubreve: o3,
  ubreve: s3,
  Ucirc: c3,
  ucirc: i3,
  Ucy: a3,
  ucy: l3,
  udarr: u3,
  Udblac: f3,
  udblac: d3,
  udhar: p3,
  ufisht: h3,
  Ufr: m3,
  ufr: g3,
  Ugrave: b3,
  ugrave: v3,
  uHar: y3,
  uharl: w3,
  uharr: k3,
  uhblk: x3,
  ulcorn: _3,
  ulcorner: A3,
  ulcrop: E3,
  ultri: C3,
  Umacr: D3,
  umacr: S3,
  uml: q3,
  UnderBar: R3,
  UnderBrace: T3,
  UnderBracket: F3,
  UnderParenthesis: L3,
  Union: N3,
  UnionPlus: I3,
  Uogon: B3,
  uogon: O3,
  Uopf: P3,
  uopf: z3,
  UpArrowBar: M3,
  uparrow: U3,
  UpArrow: j3,
  Uparrow: V3,
  UpArrowDownArrow: G3,
  updownarrow: $3,
  UpDownArrow: H3,
  Updownarrow: Z3,
  UpEquilibrium: W3,
  upharpoonleft: J3,
  upharpoonright: Q3,
  uplus: Y3,
  UpperLeftArrow: X3,
  UpperRightArrow: K3,
  upsi: ej,
  Upsi: tj,
  upsih: nj,
  Upsilon: rj,
  upsilon: oj,
  UpTeeArrow: sj,
  UpTee: cj,
  upuparrows: ij,
  urcorn: aj,
  urcorner: lj,
  urcrop: uj,
  Uring: fj,
  uring: dj,
  urtri: pj,
  Uscr: hj,
  uscr: mj,
  utdot: gj,
  Utilde: bj,
  utilde: vj,
  utri: yj,
  utrif: wj,
  uuarr: kj,
  Uuml: xj,
  uuml: _j,
  uwangle: Aj,
  vangrt: Ej,
  varepsilon: Cj,
  varkappa: Dj,
  varnothing: Sj,
  varphi: qj,
  varpi: Rj,
  varpropto: Tj,
  varr: Fj,
  vArr: Lj,
  varrho: Nj,
  varsigma: Ij,
  varsubsetneq: Bj,
  varsubsetneqq: Oj,
  varsupsetneq: Pj,
  varsupsetneqq: zj,
  vartheta: Mj,
  vartriangleleft: Uj,
  vartriangleright: jj,
  vBar: Vj,
  Vbar: Gj,
  vBarv: $j,
  Vcy: Hj,
  vcy: Zj,
  vdash: Wj,
  vDash: Jj,
  Vdash: Qj,
  VDash: Yj,
  Vdashl: Xj,
  veebar: Kj,
  vee: eV,
  Vee: tV,
  veeeq: nV,
  vellip: rV,
  verbar: oV,
  Verbar: sV,
  vert: cV,
  Vert: iV,
  VerticalBar: aV,
  VerticalLine: lV,
  VerticalSeparator: uV,
  VerticalTilde: fV,
  VeryThinSpace: dV,
  Vfr: pV,
  vfr: hV,
  vltri: mV,
  vnsub: gV,
  vnsup: bV,
  Vopf: vV,
  vopf: yV,
  vprop: wV,
  vrtri: kV,
  Vscr: xV,
  vscr: _V,
  vsubnE: AV,
  vsubne: EV,
  vsupnE: CV,
  vsupne: DV,
  Vvdash: SV,
  vzigzag: qV,
  Wcirc: RV,
  wcirc: TV,
  wedbar: FV,
  wedge: LV,
  Wedge: NV,
  wedgeq: IV,
  weierp: BV,
  Wfr: OV,
  wfr: PV,
  Wopf: zV,
  wopf: MV,
  wp: UV,
  wr: jV,
  wreath: VV,
  Wscr: GV,
  wscr: $V,
  xcap: HV,
  xcirc: ZV,
  xcup: WV,
  xdtri: JV,
  Xfr: QV,
  xfr: YV,
  xharr: XV,
  xhArr: KV,
  Xi: eG,
  xi: tG,
  xlarr: nG,
  xlArr: rG,
  xmap: oG,
  xnis: sG,
  xodot: cG,
  Xopf: iG,
  xopf: aG,
  xoplus: lG,
  xotime: uG,
  xrarr: fG,
  xrArr: dG,
  Xscr: pG,
  xscr: hG,
  xsqcup: mG,
  xuplus: gG,
  xutri: bG,
  xvee: vG,
  xwedge: yG,
  Yacute: wG,
  yacute: kG,
  YAcy: xG,
  yacy: _G,
  Ycirc: AG,
  ycirc: EG,
  Ycy: CG,
  ycy: DG,
  yen: SG,
  Yfr: qG,
  yfr: RG,
  YIcy: TG,
  yicy: FG,
  Yopf: LG,
  yopf: NG,
  Yscr: IG,
  yscr: BG,
  YUcy: OG,
  yucy: PG,
  yuml: zG,
  Yuml: MG,
  Zacute: UG,
  zacute: jG,
  Zcaron: VG,
  zcaron: GG,
  Zcy: $G,
  zcy: HG,
  Zdot: ZG,
  zdot: WG,
  zeetrf: JG,
  ZeroWidthSpace: QG,
  Zeta: YG,
  zeta: XG,
  zfr: KG,
  Zfr: e$,
  ZHcy: t$,
  zhcy: n$,
  zigrarr: r$,
  zopf: o$,
  Zopf: s$,
  Zscr: c$,
  zscr: i$,
  zwj: a$,
  zwnj: l$
};
var es = u$;
var jn = /[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4E\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDF55-\uDF59]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDF3C-\uDF3E]|\uD806[\uDC3B\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/;
var Qe = {};
var Ar = {};
function f$(t7) {
  var e, n, r = Ar[t7];
  if (r)
    return r;
  for (r = Ar[t7] = [], e = 0; e < 128; e++)
    n = String.fromCharCode(e), /^[0-9a-z]$/i.test(n) ? r.push(n) : r.push("%" + ("0" + e.toString(16).toUpperCase()).slice(-2));
  for (e = 0; e < t7.length; e++)
    r[t7.charCodeAt(e)] = t7[e];
  return r;
}
function jt(t7, e, n) {
  var r, o, s, c, i, a = "";
  for (typeof e != "string" && (n = e, e = jt.defaultChars), typeof n > "u" && (n = true), i = f$(e), r = 0, o = t7.length; r < o; r++) {
    if (s = t7.charCodeAt(r), n && s === 37 && r + 2 < o && /^[0-9a-f]{2}$/i.test(t7.slice(r + 1, r + 3))) {
      a += t7.slice(r, r + 3), r += 2;
      continue;
    }
    if (s < 128) {
      a += i[s];
      continue;
    }
    if (s >= 55296 && s <= 57343) {
      if (s >= 55296 && s <= 56319 && r + 1 < o && (c = t7.charCodeAt(r + 1), c >= 56320 && c <= 57343)) {
        a += encodeURIComponent(t7[r] + t7[r + 1]), r++;
        continue;
      }
      a += "%EF%BF%BD";
      continue;
    }
    a += encodeURIComponent(t7[r]);
  }
  return a;
}
jt.defaultChars = ";/?:@&=+$,-_.!~*'()#";
jt.componentChars = "-_.!~*'()";
var d$ = jt;
var Er = {};
function p$(t7) {
  var e, n, r = Er[t7];
  if (r)
    return r;
  for (r = Er[t7] = [], e = 0; e < 128; e++)
    n = String.fromCharCode(e), r.push(n);
  for (e = 0; e < t7.length; e++)
    n = t7.charCodeAt(e), r[n] = "%" + ("0" + n.toString(16).toUpperCase()).slice(-2);
  return r;
}
function Vt(t7, e) {
  var n;
  return typeof e != "string" && (e = Vt.defaultChars), n = p$(e), t7.replace(/(%[a-f0-9]{2})+/gi, function(r) {
    var o, s, c, i, a, l, u, f = "";
    for (o = 0, s = r.length; o < s; o += 3) {
      if (c = parseInt(r.slice(o + 1, o + 3), 16), c < 128) {
        f += n[c];
        continue;
      }
      if ((c & 224) === 192 && o + 3 < s && (i = parseInt(r.slice(o + 4, o + 6), 16), (i & 192) === 128)) {
        u = c << 6 & 1984 | i & 63, u < 128 ? f += "��" : f += String.fromCharCode(u), o += 3;
        continue;
      }
      if ((c & 240) === 224 && o + 6 < s && (i = parseInt(r.slice(o + 4, o + 6), 16), a = parseInt(r.slice(o + 7, o + 9), 16), (i & 192) === 128 && (a & 192) === 128)) {
        u = c << 12 & 61440 | i << 6 & 4032 | a & 63, u < 2048 || u >= 55296 && u <= 57343 ? f += "���" : f += String.fromCharCode(u), o += 6;
        continue;
      }
      if ((c & 248) === 240 && o + 9 < s && (i = parseInt(r.slice(o + 4, o + 6), 16), a = parseInt(r.slice(o + 7, o + 9), 16), l = parseInt(r.slice(o + 10, o + 12), 16), (i & 192) === 128 && (a & 192) === 128 && (l & 192) === 128)) {
        u = c << 18 & 1835008 | i << 12 & 258048 | a << 6 & 4032 | l & 63, u < 65536 || u > 1114111 ? f += "����" : (u -= 65536, f += String.fromCharCode(55296 + (u >> 10), 56320 + (u & 1023))), o += 9;
        continue;
      }
      f += "�";
    }
    return f;
  });
}
Vt.defaultChars = ";/?:@&=+$,#";
Vt.componentChars = "";
var h$ = Vt;
var m$ = function(e) {
  var n = "";
  return n += e.protocol || "", n += e.slashes ? "//" : "", n += e.auth ? e.auth + "@" : "", e.hostname && e.hostname.indexOf(":") !== -1 ? n += "[" + e.hostname + "]" : n += e.hostname || "", n += e.port ? ":" + e.port : "", n += e.pathname || "", n += e.search || "", n += e.hash || "", n;
};
function Lt() {
  this.protocol = null, this.slashes = null, this.auth = null, this.port = null, this.hostname = null, this.hash = null, this.search = null, this.pathname = null;
}
var g$ = /^([a-z0-9.+-]+:)/i;
var b$ = /:[0-9]*$/;
var v$ = /^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/;
var y$ = ["<", ">", '"', "`", " ", "\r", `
`, "	"];
var w$ = ["{", "}", "|", "\\", "^", "`"].concat(y$);
var k$ = ["'"].concat(w$);
var Cr = ["%", "/", "?", ";", "#"].concat(k$);
var Dr = ["/", "?", "#"];
var x$ = 255;
var Sr = /^[+a-z0-9A-Z_-]{0,63}$/;
var _$ = /^([+a-z0-9A-Z_-]{0,63})(.*)$/;
var qr = {
  javascript: true,
  "javascript:": true
};
var Rr = {
  http: true,
  https: true,
  ftp: true,
  gopher: true,
  file: true,
  "http:": true,
  "https:": true,
  "ftp:": true,
  "gopher:": true,
  "file:": true
};
function A$(t7, e) {
  if (t7 && t7 instanceof Lt)
    return t7;
  var n = new Lt();
  return n.parse(t7, e), n;
}
Lt.prototype.parse = function(t7, e) {
  var n, r, o, s, c, i = t7;
  if (i = i.trim(), !e && t7.split("#").length === 1) {
    var a = v$.exec(i);
    if (a)
      return this.pathname = a[1], a[2] && (this.search = a[2]), this;
  }
  var l = g$.exec(i);
  if (l && (l = l[0], o = l.toLowerCase(), this.protocol = l, i = i.substr(l.length)), (e || l || i.match(/^\/\/[^@\/]+@[^@\/]+/)) && (c = i.substr(0, 2) === "//", c && !(l && qr[l]) && (i = i.substr(2), this.slashes = true)), !qr[l] && (c || l && !Rr[l])) {
    var u = -1;
    for (n = 0; n < Dr.length; n++)
      s = i.indexOf(Dr[n]), s !== -1 && (u === -1 || s < u) && (u = s);
    var f, d;
    for (u === -1 ? d = i.lastIndexOf("@") : d = i.lastIndexOf("@", u), d !== -1 && (f = i.slice(0, d), i = i.slice(d + 1), this.auth = f), u = -1, n = 0; n < Cr.length; n++)
      s = i.indexOf(Cr[n]), s !== -1 && (u === -1 || s < u) && (u = s);
    u === -1 && (u = i.length), i[u - 1] === ":" && u--;
    var h7 = i.slice(0, u);
    i = i.slice(u), this.parseHost(h7), this.hostname = this.hostname || "";
    var p = this.hostname[0] === "[" && this.hostname[this.hostname.length - 1] === "]";
    if (!p) {
      var g = this.hostname.split(/\./);
      for (n = 0, r = g.length; n < r; n++) {
        var m = g[n];
        if (m && !m.match(Sr)) {
          for (var v = "", y = 0, E = m.length; y < E; y++)
            m.charCodeAt(y) > 127 ? v += "x" : v += m[y];
          if (!v.match(Sr)) {
            var D = g.slice(0, n), S = g.slice(n + 1), _ = m.match(_$);
            _ && (D.push(_[1]), S.unshift(_[2])), S.length && (i = S.join(".") + i), this.hostname = D.join(".");
            break;
          }
        }
      }
    }
    this.hostname.length > x$ && (this.hostname = ""), p && (this.hostname = this.hostname.substr(1, this.hostname.length - 2));
  }
  var T = i.indexOf("#");
  T !== -1 && (this.hash = i.substr(T), i = i.slice(0, T));
  var U = i.indexOf("?");
  return U !== -1 && (this.search = i.substr(U), i = i.slice(0, U)), i && (this.pathname = i), Rr[o] && this.hostname && !this.pathname && (this.pathname = ""), this;
};
Lt.prototype.parseHost = function(t7) {
  var e = b$.exec(t7);
  e && (e = e[0], e !== ":" && (this.port = e.substr(1)), t7 = t7.substr(0, t7.length - e.length)), t7 && (this.hostname = t7);
};
var E$ = A$;
Qe.encode = d$;
Qe.decode = h$;
Qe.format = m$;
Qe.parse = E$;
var Te = {};
var ln;
var Tr;
function ts() {
  return Tr || (Tr = 1, ln = /[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/), ln;
}
var un;
var Fr;
function ns() {
  return Fr || (Fr = 1, un = /[\0-\x1F\x7F-\x9F]/), un;
}
var fn;
var Lr;
function C$() {
  return Lr || (Lr = 1, fn = /[\xAD\u0600-\u0605\u061C\u06DD\u070F\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/), fn;
}
var dn;
var Nr;
function rs() {
  return Nr || (Nr = 1, dn = /[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/), dn;
}
var Ir;
function D$() {
  return Ir || (Ir = 1, Te.Any = ts(), Te.Cc = ns(), Te.Cf = C$(), Te.P = jn, Te.Z = rs()), Te;
}
(function(t7) {
  function e(k) {
    return Object.prototype.toString.call(k);
  }
  function n(k) {
    return e(k) === "[object String]";
  }
  var r = Object.prototype.hasOwnProperty;
  function o(k, C) {
    return r.call(k, C);
  }
  function s(k) {
    var C = Array.prototype.slice.call(arguments, 1);
    return C.forEach(function(A) {
      if (A) {
        if (typeof A != "object")
          throw new TypeError(A + "must be object");
        Object.keys(A).forEach(function(N) {
          k[N] = A[N];
        });
      }
    }), k;
  }
  function c(k, C, A) {
    return [].concat(k.slice(0, C), A, k.slice(C + 1));
  }
  function i(k) {
    return !(k >= 55296 && k <= 57343 || k >= 64976 && k <= 65007 || (k & 65535) === 65535 || (k & 65535) === 65534 || k >= 0 && k <= 8 || k === 11 || k >= 14 && k <= 31 || k >= 127 && k <= 159 || k > 1114111);
  }
  function a(k) {
    if (k > 65535) {
      k -= 65536;
      var C = 55296 + (k >> 10), A = 56320 + (k & 1023);
      return String.fromCharCode(C, A);
    }
    return String.fromCharCode(k);
  }
  var l = /\\([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g, u = /&([a-z#][a-z0-9]{1,31});/gi, f = new RegExp(l.source + "|" + u.source, "gi"), d = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i, h7 = es;
  function p(k, C) {
    var A;
    return o(h7, C) ? h7[C] : C.charCodeAt(0) === 35 && d.test(C) && (A = C[1].toLowerCase() === "x" ? parseInt(C.slice(2), 16) : parseInt(C.slice(1), 10), i(A)) ? a(A) : k;
  }
  function g(k) {
    return k.indexOf("\\") < 0 ? k : k.replace(l, "$1");
  }
  function m(k) {
    return k.indexOf("\\") < 0 && k.indexOf("&") < 0 ? k : k.replace(f, function(C, A, N) {
      return A || p(C, N);
    });
  }
  var v = /[&<>"]/, y = /[&<>"]/g, E = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;"
  };
  function D(k) {
    return E[k];
  }
  function S(k) {
    return v.test(k) ? k.replace(y, D) : k;
  }
  var _ = /[.?*+^$[\]\\(){}|-]/g;
  function T(k) {
    return k.replace(_, "\\$&");
  }
  function U(k) {
    switch (k) {
      case 9:
      case 32:
        return true;
    }
    return false;
  }
  function K(k) {
    if (k >= 8192 && k <= 8202)
      return true;
    switch (k) {
      case 9:
      case 10:
      case 11:
      case 12:
      case 13:
      case 32:
      case 160:
      case 5760:
      case 8239:
      case 8287:
      case 12288:
        return true;
    }
    return false;
  }
  var P = jn;
  function w(k) {
    return P.test(k);
  }
  function x(k) {
    switch (k) {
      case 33:
      case 34:
      case 35:
      case 36:
      case 37:
      case 38:
      case 39:
      case 40:
      case 41:
      case 42:
      case 43:
      case 44:
      case 45:
      case 46:
      case 47:
      case 58:
      case 59:
      case 60:
      case 61:
      case 62:
      case 63:
      case 64:
      case 91:
      case 92:
      case 93:
      case 94:
      case 95:
      case 96:
      case 123:
      case 124:
      case 125:
      case 126:
        return true;
      default:
        return false;
    }
  }
  function R(k) {
    return k = k.trim().replace(/\s+/g, " "), "ẞ".toLowerCase() === "Ṿ" && (k = k.replace(/ẞ/g, "ß")), k.toLowerCase().toUpperCase();
  }
  t7.lib = {}, t7.lib.mdurl = Qe, t7.lib.ucmicro = D$(), t7.assign = s, t7.isString = n, t7.has = o, t7.unescapeMd = g, t7.unescapeAll = m, t7.isValidEntityCode = i, t7.fromCodePoint = a, t7.escapeHtml = S, t7.arrayReplaceAt = c, t7.isSpace = U, t7.isWhiteSpace = K, t7.isMdAsciiPunct = x, t7.isPunctChar = w, t7.escapeRE = T, t7.normalizeReference = R;
})(V);
var Gt = {};
var S$ = function(e, n, r) {
  var o, s, c, i, a = -1, l = e.posMax, u = e.pos;
  for (e.pos = n + 1, o = 1; e.pos < l; ) {
    if (c = e.src.charCodeAt(e.pos), c === 93 && (o--, o === 0)) {
      s = true;
      break;
    }
    if (i = e.pos, e.md.inline.skipToken(e), c === 91) {
      if (i === e.pos - 1)
        o++;
      else if (r)
        return e.pos = u, -1;
    }
  }
  return s && (a = e.pos), e.pos = u, a;
};
var Br = V.unescapeAll;
var q$ = function(e, n, r) {
  var o, s, c = n, i = {
    ok: false,
    pos: 0,
    lines: 0,
    str: ""
  };
  if (e.charCodeAt(c) === 60) {
    for (c++; c < r; ) {
      if (o = e.charCodeAt(c), o === 10 || o === 60)
        return i;
      if (o === 62)
        return i.pos = c + 1, i.str = Br(e.slice(n + 1, c)), i.ok = true, i;
      if (o === 92 && c + 1 < r) {
        c += 2;
        continue;
      }
      c++;
    }
    return i;
  }
  for (s = 0; c < r && (o = e.charCodeAt(c), !(o === 32 || o < 32 || o === 127)); ) {
    if (o === 92 && c + 1 < r) {
      if (e.charCodeAt(c + 1) === 32)
        break;
      c += 2;
      continue;
    }
    if (o === 40 && (s++, s > 32))
      return i;
    if (o === 41) {
      if (s === 0)
        break;
      s--;
    }
    c++;
  }
  return n === c || s !== 0 || (i.str = Br(e.slice(n, c)), i.pos = c, i.ok = true), i;
};
var R$ = V.unescapeAll;
var T$ = function(e, n, r) {
  var o, s, c = 0, i = n, a = {
    ok: false,
    pos: 0,
    lines: 0,
    str: ""
  };
  if (i >= r || (s = e.charCodeAt(i), s !== 34 && s !== 39 && s !== 40))
    return a;
  for (i++, s === 40 && (s = 41); i < r; ) {
    if (o = e.charCodeAt(i), o === s)
      return a.pos = i + 1, a.lines = c, a.str = R$(e.slice(n + 1, i)), a.ok = true, a;
    if (o === 40 && s === 41)
      return a;
    o === 10 ? c++ : o === 92 && i + 1 < r && (i++, e.charCodeAt(i) === 10 && c++), i++;
  }
  return a;
};
Gt.parseLinkLabel = S$;
Gt.parseLinkDestination = q$;
Gt.parseLinkTitle = T$;
var F$ = V.assign;
var L$ = V.unescapeAll;
var Ue = V.escapeHtml;
var xe = {};
xe.code_inline = function(t7, e, n, r, o) {
  var s = t7[e];
  return "<code" + o.renderAttrs(s) + ">" + Ue(s.content) + "</code>";
};
xe.code_block = function(t7, e, n, r, o) {
  var s = t7[e];
  return "<pre" + o.renderAttrs(s) + "><code>" + Ue(t7[e].content) + `</code></pre>
`;
};
xe.fence = function(t7, e, n, r, o) {
  var s = t7[e], c = s.info ? L$(s.info).trim() : "", i = "", a = "", l, u, f, d, h7;
  return c && (f = c.split(/(\s+)/g), i = f[0], a = f.slice(2).join("")), n.highlight ? l = n.highlight(s.content, i, a) || Ue(s.content) : l = Ue(s.content), l.indexOf("<pre") === 0 ? l + `
` : c ? (u = s.attrIndex("class"), d = s.attrs ? s.attrs.slice() : [], u < 0 ? d.push(["class", n.langPrefix + i]) : (d[u] = d[u].slice(), d[u][1] += " " + n.langPrefix + i), h7 = {
    attrs: d
  }, "<pre><code" + o.renderAttrs(h7) + ">" + l + `</code></pre>
`) : "<pre><code" + o.renderAttrs(s) + ">" + l + `</code></pre>
`;
};
xe.image = function(t7, e, n, r, o) {
  var s = t7[e];
  return s.attrs[s.attrIndex("alt")][1] = o.renderInlineAsText(s.children, n, r), o.renderToken(t7, e, n);
};
xe.hardbreak = function(t7, e, n) {
  return n.xhtmlOut ? `<br />
` : `<br>
`;
};
xe.softbreak = function(t7, e, n) {
  return n.breaks ? n.xhtmlOut ? `<br />
` : `<br>
` : `
`;
};
xe.text = function(t7, e) {
  return Ue(t7[e].content);
};
xe.html_block = function(t7, e) {
  return t7[e].content;
};
xe.html_inline = function(t7, e) {
  return t7[e].content;
};
function Ye() {
  this.rules = F$({}, xe);
}
Ye.prototype.renderAttrs = function(e) {
  var n, r, o;
  if (!e.attrs)
    return "";
  for (o = "", n = 0, r = e.attrs.length; n < r; n++)
    o += " " + Ue(e.attrs[n][0]) + '="' + Ue(e.attrs[n][1]) + '"';
  return o;
};
Ye.prototype.renderToken = function(e, n, r) {
  var o, s = "", c = false, i = e[n];
  return i.hidden ? "" : (i.block && i.nesting !== -1 && n && e[n - 1].hidden && (s += `
`), s += (i.nesting === -1 ? "</" : "<") + i.tag, s += this.renderAttrs(i), i.nesting === 0 && r.xhtmlOut && (s += " /"), i.block && (c = true, i.nesting === 1 && n + 1 < e.length && (o = e[n + 1], (o.type === "inline" || o.hidden || o.nesting === -1 && o.tag === i.tag) && (c = false))), s += c ? `>
` : ">", s);
};
Ye.prototype.renderInline = function(t7, e, n) {
  for (var r, o = "", s = this.rules, c = 0, i = t7.length; c < i; c++)
    r = t7[c].type, typeof s[r] < "u" ? o += s[r](t7, c, e, n, this) : o += this.renderToken(t7, c, e);
  return o;
};
Ye.prototype.renderInlineAsText = function(t7, e, n) {
  for (var r = "", o = 0, s = t7.length; o < s; o++)
    t7[o].type === "text" ? r += t7[o].content : t7[o].type === "image" ? r += this.renderInlineAsText(t7[o].children, e, n) : t7[o].type === "softbreak" && (r += `
`);
  return r;
};
Ye.prototype.render = function(t7, e, n) {
  var r, o, s, c = "", i = this.rules;
  for (r = 0, o = t7.length; r < o; r++)
    s = t7[r].type, s === "inline" ? c += this.renderInline(t7[r].children, e, n) : typeof i[s] < "u" ? c += i[s](t7, r, e, n, this) : c += this.renderToken(t7, r, e, n);
  return c;
};
var N$ = Ye;
function be() {
  this.__rules__ = [], this.__cache__ = null;
}
be.prototype.__find__ = function(t7) {
  for (var e = 0; e < this.__rules__.length; e++)
    if (this.__rules__[e].name === t7)
      return e;
  return -1;
};
be.prototype.__compile__ = function() {
  var t7 = this, e = [""];
  t7.__rules__.forEach(function(n) {
    n.enabled && n.alt.forEach(function(r) {
      e.indexOf(r) < 0 && e.push(r);
    });
  }), t7.__cache__ = {}, e.forEach(function(n) {
    t7.__cache__[n] = [], t7.__rules__.forEach(function(r) {
      r.enabled && (n && r.alt.indexOf(n) < 0 || t7.__cache__[n].push(r.fn));
    });
  });
};
be.prototype.at = function(t7, e, n) {
  var r = this.__find__(t7), o = n || {};
  if (r === -1)
    throw new Error("Parser rule not found: " + t7);
  this.__rules__[r].fn = e, this.__rules__[r].alt = o.alt || [], this.__cache__ = null;
};
be.prototype.before = function(t7, e, n, r) {
  var o = this.__find__(t7), s = r || {};
  if (o === -1)
    throw new Error("Parser rule not found: " + t7);
  this.__rules__.splice(o, 0, {
    name: e,
    enabled: true,
    fn: n,
    alt: s.alt || []
  }), this.__cache__ = null;
};
be.prototype.after = function(t7, e, n, r) {
  var o = this.__find__(t7), s = r || {};
  if (o === -1)
    throw new Error("Parser rule not found: " + t7);
  this.__rules__.splice(o + 1, 0, {
    name: e,
    enabled: true,
    fn: n,
    alt: s.alt || []
  }), this.__cache__ = null;
};
be.prototype.push = function(t7, e, n) {
  var r = n || {};
  this.__rules__.push({
    name: t7,
    enabled: true,
    fn: e,
    alt: r.alt || []
  }), this.__cache__ = null;
};
be.prototype.enable = function(t7, e) {
  Array.isArray(t7) || (t7 = [t7]);
  var n = [];
  return t7.forEach(function(r) {
    var o = this.__find__(r);
    if (o < 0) {
      if (e)
        return;
      throw new Error("Rules manager: invalid rule name " + r);
    }
    this.__rules__[o].enabled = true, n.push(r);
  }, this), this.__cache__ = null, n;
};
be.prototype.enableOnly = function(t7, e) {
  Array.isArray(t7) || (t7 = [t7]), this.__rules__.forEach(function(n) {
    n.enabled = false;
  }), this.enable(t7, e);
};
be.prototype.disable = function(t7, e) {
  Array.isArray(t7) || (t7 = [t7]);
  var n = [];
  return t7.forEach(function(r) {
    var o = this.__find__(r);
    if (o < 0) {
      if (e)
        return;
      throw new Error("Rules manager: invalid rule name " + r);
    }
    this.__rules__[o].enabled = false, n.push(r);
  }, this), this.__cache__ = null, n;
};
be.prototype.getRules = function(t7) {
  return this.__cache__ === null && this.__compile__(), this.__cache__[t7] || [];
};
var Vn = be;
var I$ = /\r\n?|\n/g;
var B$ = /\0/g;
var O$ = function(e) {
  var n;
  n = e.src.replace(I$, `
`), n = n.replace(B$, "�"), e.src = n;
};
var P$ = function(e) {
  var n;
  e.inlineMode ? (n = new e.Token("inline", "", 0), n.content = e.src, n.map = [0, 1], n.children = [], e.tokens.push(n)) : e.md.block.parse(e.src, e.md, e.env, e.tokens);
};
var z$ = function(e) {
  var n = e.tokens, r, o, s;
  for (o = 0, s = n.length; o < s; o++)
    r = n[o], r.type === "inline" && e.md.inline.parse(r.content, e.md, e.env, r.children);
};
var M$ = V.arrayReplaceAt;
function U$(t7) {
  return /^<a[>\s]/i.test(t7);
}
function j$(t7) {
  return /^<\/a\s*>/i.test(t7);
}
var V$ = function(e) {
  var n, r, o, s, c, i, a, l, u, f, d, h7, p, g, m, v, y = e.tokens, E;
  if (e.md.options.linkify) {
    for (r = 0, o = y.length; r < o; r++)
      if (!(y[r].type !== "inline" || !e.md.linkify.pretest(y[r].content)))
        for (s = y[r].children, p = 0, n = s.length - 1; n >= 0; n--) {
          if (i = s[n], i.type === "link_close") {
            for (n--; s[n].level !== i.level && s[n].type !== "link_open"; )
              n--;
            continue;
          }
          if (i.type === "html_inline" && (U$(i.content) && p > 0 && p--, j$(i.content) && p++), !(p > 0) && i.type === "text" && e.md.linkify.test(i.content)) {
            for (u = i.content, E = e.md.linkify.match(u), a = [], h7 = i.level, d = 0, E.length > 0 && E[0].index === 0 && n > 0 && s[n - 1].type === "text_special" && (E = E.slice(1)), l = 0; l < E.length; l++)
              g = E[l].url, m = e.md.normalizeLink(g), e.md.validateLink(m) && (v = E[l].text, E[l].schema ? E[l].schema === "mailto:" && !/^mailto:/i.test(v) ? v = e.md.normalizeLinkText("mailto:" + v).replace(/^mailto:/, "") : v = e.md.normalizeLinkText(v) : v = e.md.normalizeLinkText("http://" + v).replace(/^http:\/\//, ""), f = E[l].index, f > d && (c = new e.Token("text", "", 0), c.content = u.slice(d, f), c.level = h7, a.push(c)), c = new e.Token("link_open", "a", 1), c.attrs = [["href", m]], c.level = h7++, c.markup = "linkify", c.info = "auto", a.push(c), c = new e.Token("text", "", 0), c.content = v, c.level = h7, a.push(c), c = new e.Token("link_close", "a", -1), c.level = --h7, c.markup = "linkify", c.info = "auto", a.push(c), d = E[l].lastIndex);
            d < u.length && (c = new e.Token("text", "", 0), c.content = u.slice(d), c.level = h7, a.push(c)), y[r].children = s = M$(s, n, a);
          }
        }
  }
};
var os = /\+-|\.\.|\?\?\?\?|!!!!|,,|--/;
var G$ = /\((c|tm|r)\)/i;
var $$ = /\((c|tm|r)\)/ig;
var H$ = {
  c: "©",
  r: "®",
  tm: "™"
};
function Z$(t7, e) {
  return H$[e.toLowerCase()];
}
function W$(t7) {
  var e, n, r = 0;
  for (e = t7.length - 1; e >= 0; e--)
    n = t7[e], n.type === "text" && !r && (n.content = n.content.replace($$, Z$)), n.type === "link_open" && n.info === "auto" && r--, n.type === "link_close" && n.info === "auto" && r++;
}
function J$(t7) {
  var e, n, r = 0;
  for (e = t7.length - 1; e >= 0; e--)
    n = t7[e], n.type === "text" && !r && os.test(n.content) && (n.content = n.content.replace(/\+-/g, "±").replace(/\.{2,}/g, "…").replace(/([?!])…/g, "$1..").replace(/([?!]){4,}/g, "$1$1$1").replace(/,{2,}/g, ",").replace(/(^|[^-])---(?=[^-]|$)/mg, "$1—").replace(/(^|\s)--(?=\s|$)/mg, "$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/mg, "$1–")), n.type === "link_open" && n.info === "auto" && r--, n.type === "link_close" && n.info === "auto" && r++;
}
var Q$ = function(e) {
  var n;
  if (e.md.options.typographer)
    for (n = e.tokens.length - 1; n >= 0; n--)
      e.tokens[n].type === "inline" && (G$.test(e.tokens[n].content) && W$(e.tokens[n].children), os.test(e.tokens[n].content) && J$(e.tokens[n].children));
};
var Or = V.isWhiteSpace;
var Pr = V.isPunctChar;
var zr = V.isMdAsciiPunct;
var Y$ = /['"]/;
var Mr = /['"]/g;
var Ur = "’";
function bt(t7, e, n) {
  return t7.slice(0, e) + n + t7.slice(e + 1);
}
function X$(t7, e) {
  var n, r, o, s, c, i, a, l, u, f, d, h7, p, g, m, v, y, E, D, S, _;
  for (D = [], n = 0; n < t7.length; n++) {
    for (r = t7[n], a = t7[n].level, y = D.length - 1; y >= 0 && !(D[y].level <= a); y--)
      ;
    if (D.length = y + 1, r.type === "text") {
      o = r.content, c = 0, i = o.length;
      e:
        for (; c < i && (Mr.lastIndex = c, s = Mr.exec(o), !!s); ) {
          if (m = v = true, c = s.index + 1, E = s[0] === "'", u = 32, s.index - 1 >= 0)
            u = o.charCodeAt(s.index - 1);
          else
            for (y = n - 1; y >= 0 && !(t7[y].type === "softbreak" || t7[y].type === "hardbreak"); y--)
              if (t7[y].content) {
                u = t7[y].content.charCodeAt(t7[y].content.length - 1);
                break;
              }
          if (f = 32, c < i)
            f = o.charCodeAt(c);
          else
            for (y = n + 1; y < t7.length && !(t7[y].type === "softbreak" || t7[y].type === "hardbreak"); y++)
              if (t7[y].content) {
                f = t7[y].content.charCodeAt(0);
                break;
              }
          if (d = zr(u) || Pr(String.fromCharCode(u)), h7 = zr(f) || Pr(String.fromCharCode(f)), p = Or(u), g = Or(f), g ? m = false : h7 && (p || d || (m = false)), p ? v = false : d && (g || h7 || (v = false)), f === 34 && s[0] === '"' && u >= 48 && u <= 57 && (v = m = false), m && v && (m = d, v = h7), !m && !v) {
            E && (r.content = bt(r.content, s.index, Ur));
            continue;
          }
          if (v) {
            for (y = D.length - 1; y >= 0 && (l = D[y], !(D[y].level < a)); y--)
              if (l.single === E && D[y].level === a) {
                l = D[y], E ? (S = e.md.options.quotes[2], _ = e.md.options.quotes[3]) : (S = e.md.options.quotes[0], _ = e.md.options.quotes[1]), r.content = bt(r.content, s.index, _), t7[l.token].content = bt(
                  t7[l.token].content,
                  l.pos,
                  S
                ), c += _.length - 1, l.token === n && (c += S.length - 1), o = r.content, i = o.length, D.length = y;
                continue e;
              }
          }
          m ? D.push({
            token: n,
            pos: s.index,
            single: E,
            level: a
          }) : v && E && (r.content = bt(r.content, s.index, Ur));
        }
    }
  }
}
var K$ = function(e) {
  var n;
  if (e.md.options.typographer)
    for (n = e.tokens.length - 1; n >= 0; n--)
      e.tokens[n].type !== "inline" || !Y$.test(e.tokens[n].content) || X$(e.tokens[n].children, e);
};
var e4 = function(e) {
  var n, r, o, s, c, i, a = e.tokens;
  for (n = 0, r = a.length; n < r; n++)
    if (a[n].type === "inline") {
      for (o = a[n].children, c = o.length, s = 0; s < c; s++)
        o[s].type === "text_special" && (o[s].type = "text");
      for (s = i = 0; s < c; s++)
        o[s].type === "text" && s + 1 < c && o[s + 1].type === "text" ? o[s + 1].content = o[s].content + o[s + 1].content : (s !== i && (o[i] = o[s]), i++);
      s !== i && (o.length = i);
    }
};
function Xe(t7, e, n) {
  this.type = t7, this.tag = e, this.attrs = null, this.map = null, this.nesting = n, this.level = 0, this.children = null, this.content = "", this.markup = "", this.info = "", this.meta = null, this.block = false, this.hidden = false;
}
Xe.prototype.attrIndex = function(e) {
  var n, r, o;
  if (!this.attrs)
    return -1;
  for (n = this.attrs, r = 0, o = n.length; r < o; r++)
    if (n[r][0] === e)
      return r;
  return -1;
};
Xe.prototype.attrPush = function(e) {
  this.attrs ? this.attrs.push(e) : this.attrs = [e];
};
Xe.prototype.attrSet = function(e, n) {
  var r = this.attrIndex(e), o = [e, n];
  r < 0 ? this.attrPush(o) : this.attrs[r] = o;
};
Xe.prototype.attrGet = function(e) {
  var n = this.attrIndex(e), r = null;
  return n >= 0 && (r = this.attrs[n][1]), r;
};
Xe.prototype.attrJoin = function(e, n) {
  var r = this.attrIndex(e);
  r < 0 ? this.attrPush([e, n]) : this.attrs[r][1] = this.attrs[r][1] + " " + n;
};
var Gn = Xe;
var t4 = Gn;
function ss(t7, e, n) {
  this.src = t7, this.env = n, this.tokens = [], this.inlineMode = false, this.md = e;
}
ss.prototype.Token = t4;
var n4 = ss;
var r4 = Vn;
var pn = [
  ["normalize", O$],
  ["block", P$],
  ["inline", z$],
  ["linkify", V$],
  ["replacements", Q$],
  ["smartquotes", K$],
  // `text_join` finds `text_special` tokens (for escape sequences)
  // and joins them with the rest of the text
  ["text_join", e4]
];
function $n() {
  this.ruler = new r4();
  for (var t7 = 0; t7 < pn.length; t7++)
    this.ruler.push(pn[t7][0], pn[t7][1]);
}
$n.prototype.process = function(t7) {
  var e, n, r;
  for (r = this.ruler.getRules(""), e = 0, n = r.length; e < n; e++)
    r[e](t7);
};
$n.prototype.State = n4;
var o4 = $n;
var hn = V.isSpace;
function mn(t7, e) {
  var n = t7.bMarks[e] + t7.tShift[e], r = t7.eMarks[e];
  return t7.src.slice(n, r);
}
function jr(t7) {
  var e = [], n = 0, r = t7.length, o, s = false, c = 0, i = "";
  for (o = t7.charCodeAt(n); n < r; )
    o === 124 && (s ? (i += t7.substring(c, n - 1), c = n) : (e.push(i + t7.substring(c, n)), i = "", c = n + 1)), s = o === 92, n++, o = t7.charCodeAt(n);
  return e.push(i + t7.substring(c)), e;
}
var s4 = function(e, n, r, o) {
  var s, c, i, a, l, u, f, d, h7, p, g, m, v, y, E, D, S, _;
  if (n + 2 > r || (u = n + 1, e.sCount[u] < e.blkIndent) || e.sCount[u] - e.blkIndent >= 4 || (i = e.bMarks[u] + e.tShift[u], i >= e.eMarks[u]) || (S = e.src.charCodeAt(i++), S !== 124 && S !== 45 && S !== 58) || i >= e.eMarks[u] || (_ = e.src.charCodeAt(i++), _ !== 124 && _ !== 45 && _ !== 58 && !hn(_)) || S === 45 && hn(_))
    return false;
  for (; i < e.eMarks[u]; ) {
    if (s = e.src.charCodeAt(i), s !== 124 && s !== 45 && s !== 58 && !hn(s))
      return false;
    i++;
  }
  for (c = mn(e, n + 1), f = c.split("|"), p = [], a = 0; a < f.length; a++) {
    if (g = f[a].trim(), !g) {
      if (a === 0 || a === f.length - 1)
        continue;
      return false;
    }
    if (!/^:?-+:?$/.test(g))
      return false;
    g.charCodeAt(g.length - 1) === 58 ? p.push(g.charCodeAt(0) === 58 ? "center" : "right") : g.charCodeAt(0) === 58 ? p.push("left") : p.push("");
  }
  if (c = mn(e, n).trim(), c.indexOf("|") === -1 || e.sCount[n] - e.blkIndent >= 4 || (f = jr(c), f.length && f[0] === "" && f.shift(), f.length && f[f.length - 1] === "" && f.pop(), d = f.length, d === 0 || d !== p.length))
    return false;
  if (o)
    return true;
  for (y = e.parentType, e.parentType = "table", D = e.md.block.ruler.getRules("blockquote"), h7 = e.push("table_open", "table", 1), h7.map = m = [n, 0], h7 = e.push("thead_open", "thead", 1), h7.map = [n, n + 1], h7 = e.push("tr_open", "tr", 1), h7.map = [n, n + 1], a = 0; a < f.length; a++)
    h7 = e.push("th_open", "th", 1), p[a] && (h7.attrs = [["style", "text-align:" + p[a]]]), h7 = e.push("inline", "", 0), h7.content = f[a].trim(), h7.children = [], h7 = e.push("th_close", "th", -1);
  for (h7 = e.push("tr_close", "tr", -1), h7 = e.push("thead_close", "thead", -1), u = n + 2; u < r && !(e.sCount[u] < e.blkIndent); u++) {
    for (E = false, a = 0, l = D.length; a < l; a++)
      if (D[a](e, u, r, true)) {
        E = true;
        break;
      }
    if (E || (c = mn(e, u).trim(), !c) || e.sCount[u] - e.blkIndent >= 4)
      break;
    for (f = jr(c), f.length && f[0] === "" && f.shift(), f.length && f[f.length - 1] === "" && f.pop(), u === n + 2 && (h7 = e.push("tbody_open", "tbody", 1), h7.map = v = [n + 2, 0]), h7 = e.push("tr_open", "tr", 1), h7.map = [u, u + 1], a = 0; a < d; a++)
      h7 = e.push("td_open", "td", 1), p[a] && (h7.attrs = [["style", "text-align:" + p[a]]]), h7 = e.push("inline", "", 0), h7.content = f[a] ? f[a].trim() : "", h7.children = [], h7 = e.push("td_close", "td", -1);
    h7 = e.push("tr_close", "tr", -1);
  }
  return v && (h7 = e.push("tbody_close", "tbody", -1), v[1] = u), h7 = e.push("table_close", "table", -1), m[1] = u, e.parentType = y, e.line = u, true;
};
var c4 = function(e, n, r) {
  var o, s, c;
  if (e.sCount[n] - e.blkIndent < 4)
    return false;
  for (s = o = n + 1; o < r; ) {
    if (e.isEmpty(o)) {
      o++;
      continue;
    }
    if (e.sCount[o] - e.blkIndent >= 4) {
      o++, s = o;
      continue;
    }
    break;
  }
  return e.line = s, c = e.push("code_block", "code", 0), c.content = e.getLines(n, s, 4 + e.blkIndent, false) + `
`, c.map = [n, e.line], true;
};
var i4 = function(e, n, r, o) {
  var s, c, i, a, l, u, f, d = false, h7 = e.bMarks[n] + e.tShift[n], p = e.eMarks[n];
  if (e.sCount[n] - e.blkIndent >= 4 || h7 + 3 > p || (s = e.src.charCodeAt(h7), s !== 126 && s !== 96) || (l = h7, h7 = e.skipChars(h7, s), c = h7 - l, c < 3) || (f = e.src.slice(l, h7), i = e.src.slice(h7, p), s === 96 && i.indexOf(String.fromCharCode(s)) >= 0))
    return false;
  if (o)
    return true;
  for (a = n; a++, !(a >= r || (h7 = l = e.bMarks[a] + e.tShift[a], p = e.eMarks[a], h7 < p && e.sCount[a] < e.blkIndent)); )
    if (e.src.charCodeAt(h7) === s && !(e.sCount[a] - e.blkIndent >= 4) && (h7 = e.skipChars(h7, s), !(h7 - l < c) && (h7 = e.skipSpaces(h7), !(h7 < p)))) {
      d = true;
      break;
    }
  return c = e.sCount[n], e.line = a + (d ? 1 : 0), u = e.push("fence", "code", 0), u.info = i, u.content = e.getLines(n + 1, a, c, true), u.markup = f, u.map = [n, e.line], true;
};
var a4 = V.isSpace;
var l4 = function(e, n, r, o) {
  var s, c, i, a, l, u, f, d, h7, p, g, m, v, y, E, D, S, _, T, U, K = e.lineMax, P = e.bMarks[n] + e.tShift[n], w = e.eMarks[n];
  if (e.sCount[n] - e.blkIndent >= 4 || e.src.charCodeAt(P) !== 62)
    return false;
  if (o)
    return true;
  for (p = [], g = [], y = [], E = [], _ = e.md.block.ruler.getRules("blockquote"), v = e.parentType, e.parentType = "blockquote", d = n; d < r && (U = e.sCount[d] < e.blkIndent, P = e.bMarks[d] + e.tShift[d], w = e.eMarks[d], !(P >= w)); d++) {
    if (e.src.charCodeAt(P++) === 62 && !U) {
      for (a = e.sCount[d] + 1, e.src.charCodeAt(P) === 32 ? (P++, a++, s = false, D = true) : e.src.charCodeAt(P) === 9 ? (D = true, (e.bsCount[d] + a) % 4 === 3 ? (P++, a++, s = false) : s = true) : D = false, h7 = a, p.push(e.bMarks[d]), e.bMarks[d] = P; P < w && (c = e.src.charCodeAt(P), a4(c)); ) {
        c === 9 ? h7 += 4 - (h7 + e.bsCount[d] + (s ? 1 : 0)) % 4 : h7++;
        P++;
      }
      u = P >= w, g.push(e.bsCount[d]), e.bsCount[d] = e.sCount[d] + 1 + (D ? 1 : 0), y.push(e.sCount[d]), e.sCount[d] = h7 - a, E.push(e.tShift[d]), e.tShift[d] = P - e.bMarks[d];
      continue;
    }
    if (u)
      break;
    for (S = false, i = 0, l = _.length; i < l; i++)
      if (_[i](e, d, r, true)) {
        S = true;
        break;
      }
    if (S) {
      e.lineMax = d, e.blkIndent !== 0 && (p.push(e.bMarks[d]), g.push(e.bsCount[d]), E.push(e.tShift[d]), y.push(e.sCount[d]), e.sCount[d] -= e.blkIndent);
      break;
    }
    p.push(e.bMarks[d]), g.push(e.bsCount[d]), E.push(e.tShift[d]), y.push(e.sCount[d]), e.sCount[d] = -1;
  }
  for (m = e.blkIndent, e.blkIndent = 0, T = e.push("blockquote_open", "blockquote", 1), T.markup = ">", T.map = f = [n, 0], e.md.block.tokenize(e, n, d), T = e.push("blockquote_close", "blockquote", -1), T.markup = ">", e.lineMax = K, e.parentType = v, f[1] = e.line, i = 0; i < E.length; i++)
    e.bMarks[i + n] = p[i], e.tShift[i + n] = E[i], e.sCount[i + n] = y[i], e.bsCount[i + n] = g[i];
  return e.blkIndent = m, true;
};
var u4 = V.isSpace;
var f4 = function(e, n, r, o) {
  var s, c, i, a, l = e.bMarks[n] + e.tShift[n], u = e.eMarks[n];
  if (e.sCount[n] - e.blkIndent >= 4 || (s = e.src.charCodeAt(l++), s !== 42 && s !== 45 && s !== 95))
    return false;
  for (c = 1; l < u; ) {
    if (i = e.src.charCodeAt(l++), i !== s && !u4(i))
      return false;
    i === s && c++;
  }
  return c < 3 ? false : (o || (e.line = n + 1, a = e.push("hr", "hr", 0), a.map = [n, e.line], a.markup = Array(c + 1).join(String.fromCharCode(s))), true);
};
var cs = V.isSpace;
function Vr(t7, e) {
  var n, r, o, s;
  return r = t7.bMarks[e] + t7.tShift[e], o = t7.eMarks[e], n = t7.src.charCodeAt(r++), n !== 42 && n !== 45 && n !== 43 || r < o && (s = t7.src.charCodeAt(r), !cs(s)) ? -1 : r;
}
function Gr(t7, e) {
  var n, r = t7.bMarks[e] + t7.tShift[e], o = r, s = t7.eMarks[e];
  if (o + 1 >= s || (n = t7.src.charCodeAt(o++), n < 48 || n > 57))
    return -1;
  for (; ; ) {
    if (o >= s)
      return -1;
    if (n = t7.src.charCodeAt(o++), n >= 48 && n <= 57) {
      if (o - r >= 10)
        return -1;
      continue;
    }
    if (n === 41 || n === 46)
      break;
    return -1;
  }
  return o < s && (n = t7.src.charCodeAt(o), !cs(n)) ? -1 : o;
}
function d4(t7, e) {
  var n, r, o = t7.level + 2;
  for (n = e + 2, r = t7.tokens.length - 2; n < r; n++)
    t7.tokens[n].level === o && t7.tokens[n].type === "paragraph_open" && (t7.tokens[n + 2].hidden = true, t7.tokens[n].hidden = true, n += 2);
}
var p4 = function(e, n, r, o) {
  var s, c, i, a, l, u, f, d, h7, p, g, m, v, y, E, D, S, _, T, U, K, P, w, x, R, k, C, A = n, N = false, J = true;
  if (e.sCount[A] - e.blkIndent >= 4 || e.listIndent >= 0 && e.sCount[A] - e.listIndent >= 4 && e.sCount[A] < e.blkIndent)
    return false;
  if (o && e.parentType === "paragraph" && e.sCount[A] >= e.blkIndent && (N = true), (P = Gr(e, A)) >= 0) {
    if (f = true, x = e.bMarks[A] + e.tShift[A], v = Number(e.src.slice(x, P - 1)), N && v !== 1) return false;
  } else if ((P = Vr(e, A)) >= 0)
    f = false;
  else
    return false;
  if (N && e.skipSpaces(P) >= e.eMarks[A])
    return false;
  if (o)
    return true;
  for (m = e.src.charCodeAt(P - 1), g = e.tokens.length, f ? (C = e.push("ordered_list_open", "ol", 1), v !== 1 && (C.attrs = [["start", v]])) : C = e.push("bullet_list_open", "ul", 1), C.map = p = [A, 0], C.markup = String.fromCharCode(m), w = false, k = e.md.block.ruler.getRules("list"), S = e.parentType, e.parentType = "list"; A < r; ) {
    for (K = P, y = e.eMarks[A], u = E = e.sCount[A] + P - (e.bMarks[A] + e.tShift[A]); K < y; ) {
      if (s = e.src.charCodeAt(K), s === 9)
        E += 4 - (E + e.bsCount[A]) % 4;
      else if (s === 32)
        E++;
      else
        break;
      K++;
    }
    if (c = K, c >= y ? l = 1 : l = E - u, l > 4 && (l = 1), a = u + l, C = e.push("list_item_open", "li", 1), C.markup = String.fromCharCode(m), C.map = d = [A, 0], f && (C.info = e.src.slice(x, P - 1)), U = e.tight, T = e.tShift[A], _ = e.sCount[A], D = e.listIndent, e.listIndent = e.blkIndent, e.blkIndent = a, e.tight = true, e.tShift[A] = c - e.bMarks[A], e.sCount[A] = E, c >= y && e.isEmpty(A + 1) ? e.line = Math.min(e.line + 2, r) : e.md.block.tokenize(e, A, r, true), (!e.tight || w) && (J = false), w = e.line - A > 1 && e.isEmpty(e.line - 1), e.blkIndent = e.listIndent, e.listIndent = D, e.tShift[A] = T, e.sCount[A] = _, e.tight = U, C = e.push("list_item_close", "li", -1), C.markup = String.fromCharCode(m), A = e.line, d[1] = A, A >= r || e.sCount[A] < e.blkIndent || e.sCount[A] - e.blkIndent >= 4)
      break;
    for (R = false, i = 0, h7 = k.length; i < h7; i++)
      if (k[i](e, A, r, true)) {
        R = true;
        break;
      }
    if (R)
      break;
    if (f) {
      if (P = Gr(e, A), P < 0)
        break;
      x = e.bMarks[A] + e.tShift[A];
    } else if (P = Vr(e, A), P < 0)
      break;
    if (m !== e.src.charCodeAt(P - 1))
      break;
  }
  return f ? C = e.push("ordered_list_close", "ol", -1) : C = e.push("bullet_list_close", "ul", -1), C.markup = String.fromCharCode(m), p[1] = A, e.line = A, e.parentType = S, J && d4(e, g), true;
};
var h4 = V.normalizeReference;
var vt = V.isSpace;
var m4 = function(e, n, r, o) {
  var s, c, i, a, l, u, f, d, h7, p, g, m, v, y, E, D, S = 0, _ = e.bMarks[n] + e.tShift[n], T = e.eMarks[n], U = n + 1;
  if (e.sCount[n] - e.blkIndent >= 4 || e.src.charCodeAt(_) !== 91)
    return false;
  for (; ++_ < T; )
    if (e.src.charCodeAt(_) === 93 && e.src.charCodeAt(_ - 1) !== 92) {
      if (_ + 1 === T || e.src.charCodeAt(_ + 1) !== 58)
        return false;
      break;
    }
  for (a = e.lineMax, E = e.md.block.ruler.getRules("reference"), p = e.parentType, e.parentType = "reference"; U < a && !e.isEmpty(U); U++)
    if (!(e.sCount[U] - e.blkIndent > 3) && !(e.sCount[U] < 0)) {
      for (y = false, u = 0, f = E.length; u < f; u++)
        if (E[u](e, U, a, true)) {
          y = true;
          break;
        }
      if (y)
        break;
    }
  for (v = e.getLines(n, U, e.blkIndent, false).trim(), T = v.length, _ = 1; _ < T; _++) {
    if (s = v.charCodeAt(_), s === 91)
      return false;
    if (s === 93) {
      h7 = _;
      break;
    } else s === 10 ? S++ : s === 92 && (_++, _ < T && v.charCodeAt(_) === 10 && S++);
  }
  if (h7 < 0 || v.charCodeAt(h7 + 1) !== 58)
    return false;
  for (_ = h7 + 2; _ < T; _++)
    if (s = v.charCodeAt(_), s === 10)
      S++;
    else if (!vt(s)) break;
  if (g = e.md.helpers.parseLinkDestination(v, _, T), !g.ok || (l = e.md.normalizeLink(g.str), !e.md.validateLink(l)))
    return false;
  for (_ = g.pos, S += g.lines, c = _, i = S, m = _; _ < T; _++)
    if (s = v.charCodeAt(_), s === 10)
      S++;
    else if (!vt(s)) break;
  for (g = e.md.helpers.parseLinkTitle(v, _, T), _ < T && m !== _ && g.ok ? (D = g.str, _ = g.pos, S += g.lines) : (D = "", _ = c, S = i); _ < T && (s = v.charCodeAt(_), !!vt(s)); )
    _++;
  if (_ < T && v.charCodeAt(_) !== 10 && D)
    for (D = "", _ = c, S = i; _ < T && (s = v.charCodeAt(_), !!vt(s)); )
      _++;
  return _ < T && v.charCodeAt(_) !== 10 || (d = h4(v.slice(1, h7)), !d) ? false : (o || (typeof e.env.references > "u" && (e.env.references = {}), typeof e.env.references[d] > "u" && (e.env.references[d] = { title: D, href: l }), e.parentType = p, e.line = n + S + 1), true);
};
var g4 = [
  "address",
  "article",
  "aside",
  "base",
  "basefont",
  "blockquote",
  "body",
  "caption",
  "center",
  "col",
  "colgroup",
  "dd",
  "details",
  "dialog",
  "dir",
  "div",
  "dl",
  "dt",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "frame",
  "frameset",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hr",
  "html",
  "iframe",
  "legend",
  "li",
  "link",
  "main",
  "menu",
  "menuitem",
  "nav",
  "noframes",
  "ol",
  "optgroup",
  "option",
  "p",
  "param",
  "section",
  "source",
  "summary",
  "table",
  "tbody",
  "td",
  "tfoot",
  "th",
  "thead",
  "title",
  "tr",
  "track",
  "ul"
];
var $t = {};
var b4 = "[a-zA-Z_:][a-zA-Z0-9:._-]*";
var v4 = "[^\"'=<>`\\x00-\\x20]+";
var y4 = "'[^']*'";
var w4 = '"[^"]*"';
var k4 = "(?:" + v4 + "|" + y4 + "|" + w4 + ")";
var x4 = "(?:\\s+" + b4 + "(?:\\s*=\\s*" + k4 + ")?)";
var is = "<[A-Za-z][A-Za-z0-9\\-]*" + x4 + "*\\s*\\/?>";
var as = "<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>";
var _4 = "<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->";
var A4 = "<[?][\\s\\S]*?[?]>";
var E4 = "<![A-Z]+\\s+[^>]*>";
var C4 = "<!\\[CDATA\\[[\\s\\S]*?\\]\\]>";
var D4 = new RegExp("^(?:" + is + "|" + as + "|" + _4 + "|" + A4 + "|" + E4 + "|" + C4 + ")");
var S4 = new RegExp("^(?:" + is + "|" + as + ")");
$t.HTML_TAG_RE = D4;
$t.HTML_OPEN_CLOSE_TAG_RE = S4;
var q4 = g4;
var R4 = $t.HTML_OPEN_CLOSE_TAG_RE;
var Ge = [
  [/^<(script|pre|style|textarea)(?=(\s|>|$))/i, /<\/(script|pre|style|textarea)>/i, true],
  [/^<!--/, /-->/, true],
  [/^<\?/, /\?>/, true],
  [/^<![A-Z]/, />/, true],
  [/^<!\[CDATA\[/, /\]\]>/, true],
  [new RegExp("^</?(" + q4.join("|") + ")(?=(\\s|/?>|$))", "i"), /^$/, true],
  [new RegExp(R4.source + "\\s*$"), /^$/, false]
];
var T4 = function(e, n, r, o) {
  var s, c, i, a, l = e.bMarks[n] + e.tShift[n], u = e.eMarks[n];
  if (e.sCount[n] - e.blkIndent >= 4 || !e.md.options.html || e.src.charCodeAt(l) !== 60)
    return false;
  for (a = e.src.slice(l, u), s = 0; s < Ge.length && !Ge[s][0].test(a); s++)
    ;
  if (s === Ge.length)
    return false;
  if (o)
    return Ge[s][2];
  if (c = n + 1, !Ge[s][1].test(a)) {
    for (; c < r && !(e.sCount[c] < e.blkIndent); c++)
      if (l = e.bMarks[c] + e.tShift[c], u = e.eMarks[c], a = e.src.slice(l, u), Ge[s][1].test(a)) {
        a.length !== 0 && c++;
        break;
      }
  }
  return e.line = c, i = e.push("html_block", "", 0), i.map = [n, c], i.content = e.getLines(n, c, e.blkIndent, true), true;
};
var $r = V.isSpace;
var F4 = function(e, n, r, o) {
  var s, c, i, a, l = e.bMarks[n] + e.tShift[n], u = e.eMarks[n];
  if (e.sCount[n] - e.blkIndent >= 4 || (s = e.src.charCodeAt(l), s !== 35 || l >= u))
    return false;
  for (c = 1, s = e.src.charCodeAt(++l); s === 35 && l < u && c <= 6; )
    c++, s = e.src.charCodeAt(++l);
  return c > 6 || l < u && !$r(s) ? false : (o || (u = e.skipSpacesBack(u, l), i = e.skipCharsBack(u, 35, l), i > l && $r(e.src.charCodeAt(i - 1)) && (u = i), e.line = n + 1, a = e.push("heading_open", "h" + String(c), 1), a.markup = "########".slice(0, c), a.map = [n, e.line], a = e.push("inline", "", 0), a.content = e.src.slice(l, u).trim(), a.map = [n, e.line], a.children = [], a = e.push("heading_close", "h" + String(c), -1), a.markup = "########".slice(0, c)), true);
};
var L4 = function(e, n, r) {
  var o, s, c, i, a, l, u, f, d, h7 = n + 1, p, g = e.md.block.ruler.getRules("paragraph");
  if (e.sCount[n] - e.blkIndent >= 4)
    return false;
  for (p = e.parentType, e.parentType = "paragraph"; h7 < r && !e.isEmpty(h7); h7++)
    if (!(e.sCount[h7] - e.blkIndent > 3)) {
      if (e.sCount[h7] >= e.blkIndent && (l = e.bMarks[h7] + e.tShift[h7], u = e.eMarks[h7], l < u && (d = e.src.charCodeAt(l), (d === 45 || d === 61) && (l = e.skipChars(l, d), l = e.skipSpaces(l), l >= u)))) {
        f = d === 61 ? 1 : 2;
        break;
      }
      if (!(e.sCount[h7] < 0)) {
        for (s = false, c = 0, i = g.length; c < i; c++)
          if (g[c](e, h7, r, true)) {
            s = true;
            break;
          }
        if (s)
          break;
      }
    }
  return f ? (o = e.getLines(n, h7, e.blkIndent, false).trim(), e.line = h7 + 1, a = e.push("heading_open", "h" + String(f), 1), a.markup = String.fromCharCode(d), a.map = [n, e.line], a = e.push("inline", "", 0), a.content = o, a.map = [n, e.line - 1], a.children = [], a = e.push("heading_close", "h" + String(f), -1), a.markup = String.fromCharCode(d), e.parentType = p, true) : false;
};
var N4 = function(e, n, r) {
  var o, s, c, i, a, l, u = n + 1, f = e.md.block.ruler.getRules("paragraph");
  for (l = e.parentType, e.parentType = "paragraph"; u < r && !e.isEmpty(u); u++)
    if (!(e.sCount[u] - e.blkIndent > 3) && !(e.sCount[u] < 0)) {
      for (s = false, c = 0, i = f.length; c < i; c++)
        if (f[c](e, u, r, true)) {
          s = true;
          break;
        }
      if (s)
        break;
    }
  return o = e.getLines(n, u, e.blkIndent, false).trim(), e.line = u, a = e.push("paragraph_open", "p", 1), a.map = [n, e.line], a = e.push("inline", "", 0), a.content = o, a.map = [n, e.line], a.children = [], a = e.push("paragraph_close", "p", -1), e.parentType = l, true;
};
var ls = Gn;
var Ht = V.isSpace;
function _e(t7, e, n, r) {
  var o, s, c, i, a, l, u, f;
  for (this.src = t7, this.md = e, this.env = n, this.tokens = r, this.bMarks = [], this.eMarks = [], this.tShift = [], this.sCount = [], this.bsCount = [], this.blkIndent = 0, this.line = 0, this.lineMax = 0, this.tight = false, this.ddIndent = -1, this.listIndent = -1, this.parentType = "root", this.level = 0, this.result = "", s = this.src, f = false, c = i = l = u = 0, a = s.length; i < a; i++) {
    if (o = s.charCodeAt(i), !f)
      if (Ht(o)) {
        l++, o === 9 ? u += 4 - u % 4 : u++;
        continue;
      } else
        f = true;
    (o === 10 || i === a - 1) && (o !== 10 && i++, this.bMarks.push(c), this.eMarks.push(i), this.tShift.push(l), this.sCount.push(u), this.bsCount.push(0), f = false, l = 0, u = 0, c = i + 1);
  }
  this.bMarks.push(s.length), this.eMarks.push(s.length), this.tShift.push(0), this.sCount.push(0), this.bsCount.push(0), this.lineMax = this.bMarks.length - 1;
}
_e.prototype.push = function(t7, e, n) {
  var r = new ls(t7, e, n);
  return r.block = true, n < 0 && this.level--, r.level = this.level, n > 0 && this.level++, this.tokens.push(r), r;
};
_e.prototype.isEmpty = function(e) {
  return this.bMarks[e] + this.tShift[e] >= this.eMarks[e];
};
_e.prototype.skipEmptyLines = function(e) {
  for (var n = this.lineMax; e < n && !(this.bMarks[e] + this.tShift[e] < this.eMarks[e]); e++)
    ;
  return e;
};
_e.prototype.skipSpaces = function(e) {
  for (var n, r = this.src.length; e < r && (n = this.src.charCodeAt(e), !!Ht(n)); e++)
    ;
  return e;
};
_e.prototype.skipSpacesBack = function(e, n) {
  if (e <= n)
    return e;
  for (; e > n; )
    if (!Ht(this.src.charCodeAt(--e)))
      return e + 1;
  return e;
};
_e.prototype.skipChars = function(e, n) {
  for (var r = this.src.length; e < r && this.src.charCodeAt(e) === n; e++)
    ;
  return e;
};
_e.prototype.skipCharsBack = function(e, n, r) {
  if (e <= r)
    return e;
  for (; e > r; )
    if (n !== this.src.charCodeAt(--e))
      return e + 1;
  return e;
};
_e.prototype.getLines = function(e, n, r, o) {
  var s, c, i, a, l, u, f, d = e;
  if (e >= n)
    return "";
  for (u = new Array(n - e), s = 0; d < n; d++, s++) {
    for (c = 0, f = a = this.bMarks[d], d + 1 < n || o ? l = this.eMarks[d] + 1 : l = this.eMarks[d]; a < l && c < r; ) {
      if (i = this.src.charCodeAt(a), Ht(i))
        i === 9 ? c += 4 - (c + this.bsCount[d]) % 4 : c++;
      else if (a - f < this.tShift[d])
        c++;
      else
        break;
      a++;
    }
    c > r ? u[s] = new Array(c - r + 1).join(" ") + this.src.slice(a, l) : u[s] = this.src.slice(a, l);
  }
  return u.join("");
};
_e.prototype.Token = ls;
var I4 = _e;
var B4 = Vn;
var yt = [
  // First 2 params - rule name & source. Secondary array - list of rules,
  // which can be terminated by this one.
  ["table", s4, ["paragraph", "reference"]],
  ["code", c4],
  ["fence", i4, ["paragraph", "reference", "blockquote", "list"]],
  ["blockquote", l4, ["paragraph", "reference", "blockquote", "list"]],
  ["hr", f4, ["paragraph", "reference", "blockquote", "list"]],
  ["list", p4, ["paragraph", "reference", "blockquote"]],
  ["reference", m4],
  ["html_block", T4, ["paragraph", "reference", "blockquote"]],
  ["heading", F4, ["paragraph", "reference", "blockquote"]],
  ["lheading", L4],
  ["paragraph", N4]
];
function Zt() {
  this.ruler = new B4();
  for (var t7 = 0; t7 < yt.length; t7++)
    this.ruler.push(yt[t7][0], yt[t7][1], { alt: (yt[t7][2] || []).slice() });
}
Zt.prototype.tokenize = function(t7, e, n) {
  for (var r, o, s, c = this.ruler.getRules(""), i = c.length, a = e, l = false, u = t7.md.options.maxNesting; a < n && (t7.line = a = t7.skipEmptyLines(a), !(a >= n || t7.sCount[a] < t7.blkIndent)); ) {
    if (t7.level >= u) {
      t7.line = n;
      break;
    }
    for (s = t7.line, o = 0; o < i; o++)
      if (r = c[o](t7, a, n, false), r) {
        if (s >= t7.line)
          throw new Error("block rule didn't increment state.line");
        break;
      }
    if (!r) throw new Error("none of the block rules matched");
    t7.tight = !l, t7.isEmpty(t7.line - 1) && (l = true), a = t7.line, a < n && t7.isEmpty(a) && (l = true, a++, t7.line = a);
  }
};
Zt.prototype.parse = function(t7, e, n, r) {
  var o;
  t7 && (o = new this.State(t7, e, n, r), this.tokenize(o, o.line, o.lineMax));
};
Zt.prototype.State = I4;
var O4 = Zt;
function P4(t7) {
  switch (t7) {
    case 10:
    case 33:
    case 35:
    case 36:
    case 37:
    case 38:
    case 42:
    case 43:
    case 45:
    case 58:
    case 60:
    case 61:
    case 62:
    case 64:
    case 91:
    case 92:
    case 93:
    case 94:
    case 95:
    case 96:
    case 123:
    case 125:
    case 126:
      return true;
    default:
      return false;
  }
}
var z4 = function(e, n) {
  for (var r = e.pos; r < e.posMax && !P4(e.src.charCodeAt(r)); )
    r++;
  return r === e.pos ? false : (n || (e.pending += e.src.slice(e.pos, r)), e.pos = r, true);
};
var M4 = /(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;
var U4 = function(e, n) {
  var r, o, s, c, i, a, l, u;
  return !e.md.options.linkify || e.linkLevel > 0 || (r = e.pos, o = e.posMax, r + 3 > o) || e.src.charCodeAt(r) !== 58 || e.src.charCodeAt(r + 1) !== 47 || e.src.charCodeAt(r + 2) !== 47 || (s = e.pending.match(M4), !s) || (c = s[1], i = e.md.linkify.matchAtStart(e.src.slice(r - c.length)), !i) || (a = i.url, a.length <= c.length) || (a = a.replace(/\*+$/, ""), l = e.md.normalizeLink(a), !e.md.validateLink(l)) ? false : (n || (e.pending = e.pending.slice(0, -c.length), u = e.push("link_open", "a", 1), u.attrs = [["href", l]], u.markup = "linkify", u.info = "auto", u = e.push("text", "", 0), u.content = e.md.normalizeLinkText(a), u = e.push("link_close", "a", -1), u.markup = "linkify", u.info = "auto"), e.pos += a.length - c.length, true);
};
var j4 = V.isSpace;
var V4 = function(e, n) {
  var r, o, s, c = e.pos;
  if (e.src.charCodeAt(c) !== 10)
    return false;
  if (r = e.pending.length - 1, o = e.posMax, !n)
    if (r >= 0 && e.pending.charCodeAt(r) === 32)
      if (r >= 1 && e.pending.charCodeAt(r - 1) === 32) {
        for (s = r - 1; s >= 1 && e.pending.charCodeAt(s - 1) === 32; ) s--;
        e.pending = e.pending.slice(0, s), e.push("hardbreak", "br", 0);
      } else
        e.pending = e.pending.slice(0, -1), e.push("softbreak", "br", 0);
    else
      e.push("softbreak", "br", 0);
  for (c++; c < o && j4(e.src.charCodeAt(c)); )
    c++;
  return e.pos = c, true;
};
var G4 = V.isSpace;
var Hn = [];
for (Hr = 0; Hr < 256; Hr++)
  Hn.push(0);
var Hr;
"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(t7) {
  Hn[t7.charCodeAt(0)] = 1;
});
var $4 = function(e, n) {
  var r, o, s, c, i, a = e.pos, l = e.posMax;
  if (e.src.charCodeAt(a) !== 92 || (a++, a >= l)) return false;
  if (r = e.src.charCodeAt(a), r === 10) {
    for (n || e.push("hardbreak", "br", 0), a++; a < l && (r = e.src.charCodeAt(a), !!G4(r)); )
      a++;
    return e.pos = a, true;
  }
  return c = e.src[a], r >= 55296 && r <= 56319 && a + 1 < l && (o = e.src.charCodeAt(a + 1), o >= 56320 && o <= 57343 && (c += e.src[a + 1], a++)), s = "\\" + c, n || (i = e.push("text_special", "", 0), r < 256 && Hn[r] !== 0 ? i.content = c : i.content = s, i.markup = s, i.info = "escape"), e.pos = a + 1, true;
};
var H4 = function(e, n) {
  var r, o, s, c, i, a, l, u, f = e.pos, d = e.src.charCodeAt(f);
  if (d !== 96)
    return false;
  for (r = f, f++, o = e.posMax; f < o && e.src.charCodeAt(f) === 96; )
    f++;
  if (s = e.src.slice(r, f), l = s.length, e.backticksScanned && (e.backticks[l] || 0) <= r)
    return n || (e.pending += s), e.pos += l, true;
  for (a = f; (i = e.src.indexOf("`", a)) !== -1; ) {
    for (a = i + 1; a < o && e.src.charCodeAt(a) === 96; )
      a++;
    if (u = a - i, u === l)
      return n || (c = e.push("code_inline", "code", 0), c.markup = s, c.content = e.src.slice(f, i).replace(/\n/g, " ").replace(/^ (.+) $/, "$1")), e.pos = a, true;
    e.backticks[u] = i;
  }
  return e.backticksScanned = true, n || (e.pending += s), e.pos += l, true;
};
var Wt = {};
Wt.tokenize = function(e, n) {
  var r, o, s, c, i, a = e.pos, l = e.src.charCodeAt(a);
  if (n || l !== 126 || (o = e.scanDelims(e.pos, true), c = o.length, i = String.fromCharCode(l), c < 2))
    return false;
  for (c % 2 && (s = e.push("text", "", 0), s.content = i, c--), r = 0; r < c; r += 2)
    s = e.push("text", "", 0), s.content = i + i, e.delimiters.push({
      marker: l,
      length: 0,
      // disable "rule of 3" length checks meant for emphasis
      token: e.tokens.length - 1,
      end: -1,
      open: o.can_open,
      close: o.can_close
    });
  return e.pos += o.length, true;
};
function Zr(t7, e) {
  var n, r, o, s, c, i = [], a = e.length;
  for (n = 0; n < a; n++)
    o = e[n], o.marker === 126 && o.end !== -1 && (s = e[o.end], c = t7.tokens[o.token], c.type = "s_open", c.tag = "s", c.nesting = 1, c.markup = "~~", c.content = "", c = t7.tokens[s.token], c.type = "s_close", c.tag = "s", c.nesting = -1, c.markup = "~~", c.content = "", t7.tokens[s.token - 1].type === "text" && t7.tokens[s.token - 1].content === "~" && i.push(s.token - 1));
  for (; i.length; ) {
    for (n = i.pop(), r = n + 1; r < t7.tokens.length && t7.tokens[r].type === "s_close"; )
      r++;
    r--, n !== r && (c = t7.tokens[r], t7.tokens[r] = t7.tokens[n], t7.tokens[n] = c);
  }
}
Wt.postProcess = function(e) {
  var n, r = e.tokens_meta, o = e.tokens_meta.length;
  for (Zr(e, e.delimiters), n = 0; n < o; n++)
    r[n] && r[n].delimiters && Zr(e, r[n].delimiters);
};
var Jt = {};
Jt.tokenize = function(e, n) {
  var r, o, s, c = e.pos, i = e.src.charCodeAt(c);
  if (n || i !== 95 && i !== 42)
    return false;
  for (o = e.scanDelims(e.pos, i === 42), r = 0; r < o.length; r++)
    s = e.push("text", "", 0), s.content = String.fromCharCode(i), e.delimiters.push({
      // Char code of the starting marker (number).
      //
      marker: i,
      // Total length of these series of delimiters.
      //
      length: o.length,
      // A position of the token this delimiter corresponds to.
      //
      token: e.tokens.length - 1,
      // If this delimiter is matched as a valid opener, `end` will be
      // equal to its position, otherwise it's `-1`.
      //
      end: -1,
      // Boolean flags that determine if this delimiter could open or close
      // an emphasis.
      //
      open: o.can_open,
      close: o.can_close
    });
  return e.pos += o.length, true;
};
function Wr(t7, e) {
  var n, r, o, s, c, i, a = e.length;
  for (n = a - 1; n >= 0; n--)
    r = e[n], !(r.marker !== 95 && r.marker !== 42) && r.end !== -1 && (o = e[r.end], i = n > 0 && e[n - 1].end === r.end + 1 && // check that first two markers match and adjacent
    e[n - 1].marker === r.marker && e[n - 1].token === r.token - 1 && // check that last two markers are adjacent (we can safely assume they match)
    e[r.end + 1].token === o.token + 1, c = String.fromCharCode(r.marker), s = t7.tokens[r.token], s.type = i ? "strong_open" : "em_open", s.tag = i ? "strong" : "em", s.nesting = 1, s.markup = i ? c + c : c, s.content = "", s = t7.tokens[o.token], s.type = i ? "strong_close" : "em_close", s.tag = i ? "strong" : "em", s.nesting = -1, s.markup = i ? c + c : c, s.content = "", i && (t7.tokens[e[n - 1].token].content = "", t7.tokens[e[r.end + 1].token].content = "", n--));
}
Jt.postProcess = function(e) {
  var n, r = e.tokens_meta, o = e.tokens_meta.length;
  for (Wr(e, e.delimiters), n = 0; n < o; n++)
    r[n] && r[n].delimiters && Wr(e, r[n].delimiters);
};
var Z4 = V.normalizeReference;
var gn = V.isSpace;
var W4 = function(e, n) {
  var r, o, s, c, i, a, l, u, f, d = "", h7 = "", p = e.pos, g = e.posMax, m = e.pos, v = true;
  if (e.src.charCodeAt(e.pos) !== 91 || (i = e.pos + 1, c = e.md.helpers.parseLinkLabel(e, e.pos, true), c < 0))
    return false;
  if (a = c + 1, a < g && e.src.charCodeAt(a) === 40) {
    for (v = false, a++; a < g && (o = e.src.charCodeAt(a), !(!gn(o) && o !== 10)); a++)
      ;
    if (a >= g)
      return false;
    if (m = a, l = e.md.helpers.parseLinkDestination(e.src, a, e.posMax), l.ok) {
      for (d = e.md.normalizeLink(l.str), e.md.validateLink(d) ? a = l.pos : d = "", m = a; a < g && (o = e.src.charCodeAt(a), !(!gn(o) && o !== 10)); a++)
        ;
      if (l = e.md.helpers.parseLinkTitle(e.src, a, e.posMax), a < g && m !== a && l.ok)
        for (h7 = l.str, a = l.pos; a < g && (o = e.src.charCodeAt(a), !(!gn(o) && o !== 10)); a++)
          ;
    }
    (a >= g || e.src.charCodeAt(a) !== 41) && (v = true), a++;
  }
  if (v) {
    if (typeof e.env.references > "u")
      return false;
    if (a < g && e.src.charCodeAt(a) === 91 ? (m = a + 1, a = e.md.helpers.parseLinkLabel(e, a), a >= 0 ? s = e.src.slice(m, a++) : a = c + 1) : a = c + 1, s || (s = e.src.slice(i, c)), u = e.env.references[Z4(s)], !u)
      return e.pos = p, false;
    d = u.href, h7 = u.title;
  }
  return n || (e.pos = i, e.posMax = c, f = e.push("link_open", "a", 1), f.attrs = r = [["href", d]], h7 && r.push(["title", h7]), e.linkLevel++, e.md.inline.tokenize(e), e.linkLevel--, f = e.push("link_close", "a", -1)), e.pos = a, e.posMax = g, true;
};
var J4 = V.normalizeReference;
var bn = V.isSpace;
var Q4 = function(e, n) {
  var r, o, s, c, i, a, l, u, f, d, h7, p, g, m = "", v = e.pos, y = e.posMax;
  if (e.src.charCodeAt(e.pos) !== 33 || e.src.charCodeAt(e.pos + 1) !== 91 || (a = e.pos + 2, i = e.md.helpers.parseLinkLabel(e, e.pos + 1, false), i < 0))
    return false;
  if (l = i + 1, l < y && e.src.charCodeAt(l) === 40) {
    for (l++; l < y && (o = e.src.charCodeAt(l), !(!bn(o) && o !== 10)); l++)
      ;
    if (l >= y)
      return false;
    for (g = l, f = e.md.helpers.parseLinkDestination(e.src, l, e.posMax), f.ok && (m = e.md.normalizeLink(f.str), e.md.validateLink(m) ? l = f.pos : m = ""), g = l; l < y && (o = e.src.charCodeAt(l), !(!bn(o) && o !== 10)); l++)
      ;
    if (f = e.md.helpers.parseLinkTitle(e.src, l, e.posMax), l < y && g !== l && f.ok)
      for (d = f.str, l = f.pos; l < y && (o = e.src.charCodeAt(l), !(!bn(o) && o !== 10)); l++)
        ;
    else
      d = "";
    if (l >= y || e.src.charCodeAt(l) !== 41)
      return e.pos = v, false;
    l++;
  } else {
    if (typeof e.env.references > "u")
      return false;
    if (l < y && e.src.charCodeAt(l) === 91 ? (g = l + 1, l = e.md.helpers.parseLinkLabel(e, l), l >= 0 ? c = e.src.slice(g, l++) : l = i + 1) : l = i + 1, c || (c = e.src.slice(a, i)), u = e.env.references[J4(c)], !u)
      return e.pos = v, false;
    m = u.href, d = u.title;
  }
  return n || (s = e.src.slice(a, i), e.md.inline.parse(
    s,
    e.md,
    e.env,
    p = []
  ), h7 = e.push("image", "img", 0), h7.attrs = r = [["src", m], ["alt", ""]], h7.children = p, h7.content = s, d && r.push(["title", d])), e.pos = l, e.posMax = y, true;
};
var Y4 = /^([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/;
var X4 = /^([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)$/;
var K4 = function(e, n) {
  var r, o, s, c, i, a, l = e.pos;
  if (e.src.charCodeAt(l) !== 60)
    return false;
  for (i = e.pos, a = e.posMax; ; ) {
    if (++l >= a || (c = e.src.charCodeAt(l), c === 60)) return false;
    if (c === 62) break;
  }
  return r = e.src.slice(i + 1, l), X4.test(r) ? (o = e.md.normalizeLink(r), e.md.validateLink(o) ? (n || (s = e.push("link_open", "a", 1), s.attrs = [["href", o]], s.markup = "autolink", s.info = "auto", s = e.push("text", "", 0), s.content = e.md.normalizeLinkText(r), s = e.push("link_close", "a", -1), s.markup = "autolink", s.info = "auto"), e.pos += r.length + 2, true) : false) : Y4.test(r) ? (o = e.md.normalizeLink("mailto:" + r), e.md.validateLink(o) ? (n || (s = e.push("link_open", "a", 1), s.attrs = [["href", o]], s.markup = "autolink", s.info = "auto", s = e.push("text", "", 0), s.content = e.md.normalizeLinkText(r), s = e.push("link_close", "a", -1), s.markup = "autolink", s.info = "auto"), e.pos += r.length + 2, true) : false) : false;
};
var e5 = $t.HTML_TAG_RE;
function t5(t7) {
  return /^<a[>\s]/i.test(t7);
}
function n5(t7) {
  return /^<\/a\s*>/i.test(t7);
}
function r5(t7) {
  var e = t7 | 32;
  return e >= 97 && e <= 122;
}
var o5 = function(e, n) {
  var r, o, s, c, i = e.pos;
  return !e.md.options.html || (s = e.posMax, e.src.charCodeAt(i) !== 60 || i + 2 >= s) || (r = e.src.charCodeAt(i + 1), r !== 33 && r !== 63 && r !== 47 && !r5(r)) || (o = e.src.slice(i).match(e5), !o) ? false : (n || (c = e.push("html_inline", "", 0), c.content = o[0], t5(c.content) && e.linkLevel++, n5(c.content) && e.linkLevel--), e.pos += o[0].length, true);
};
var Jr = es;
var s5 = V.has;
var c5 = V.isValidEntityCode;
var Qr = V.fromCodePoint;
var i5 = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i;
var a5 = /^&([a-z][a-z0-9]{1,31});/i;
var l5 = function(e, n) {
  var r, o, s, c, i = e.pos, a = e.posMax;
  if (e.src.charCodeAt(i) !== 38 || i + 1 >= a) return false;
  if (r = e.src.charCodeAt(i + 1), r === 35) {
    if (s = e.src.slice(i).match(i5), s)
      return n || (o = s[1][0].toLowerCase() === "x" ? parseInt(s[1].slice(1), 16) : parseInt(s[1], 10), c = e.push("text_special", "", 0), c.content = c5(o) ? Qr(o) : Qr(65533), c.markup = s[0], c.info = "entity"), e.pos += s[0].length, true;
  } else if (s = e.src.slice(i).match(a5), s && s5(Jr, s[1]))
    return n || (c = e.push("text_special", "", 0), c.content = Jr[s[1]], c.markup = s[0], c.info = "entity"), e.pos += s[0].length, true;
  return false;
};
function Yr(t7) {
  var e, n, r, o, s, c, i, a, l = {}, u = t7.length;
  if (u) {
    var f = 0, d = -2, h7 = [];
    for (e = 0; e < u; e++)
      if (r = t7[e], h7.push(0), (t7[f].marker !== r.marker || d !== r.token - 1) && (f = e), d = r.token, r.length = r.length || 0, !!r.close) {
        for (l.hasOwnProperty(r.marker) || (l[r.marker] = [-1, -1, -1, -1, -1, -1]), s = l[r.marker][(r.open ? 3 : 0) + r.length % 3], n = f - h7[f] - 1, c = n; n > s; n -= h7[n] + 1)
          if (o = t7[n], o.marker === r.marker && o.open && o.end < 0 && (i = false, (o.close || r.open) && (o.length + r.length) % 3 === 0 && (o.length % 3 !== 0 || r.length % 3 !== 0) && (i = true), !i)) {
            a = n > 0 && !t7[n - 1].open ? h7[n - 1] + 1 : 0, h7[e] = e - n + a, h7[n] = a, r.open = false, o.end = e, o.close = false, c = -1, d = -2;
            break;
          }
        c !== -1 && (l[r.marker][(r.open ? 3 : 0) + (r.length || 0) % 3] = c);
      }
  }
}
var u5 = function(e) {
  var n, r = e.tokens_meta, o = e.tokens_meta.length;
  for (Yr(e.delimiters), n = 0; n < o; n++)
    r[n] && r[n].delimiters && Yr(r[n].delimiters);
};
var f5 = function(e) {
  var n, r, o = 0, s = e.tokens, c = e.tokens.length;
  for (n = r = 0; n < c; n++)
    s[n].nesting < 0 && o--, s[n].level = o, s[n].nesting > 0 && o++, s[n].type === "text" && n + 1 < c && s[n + 1].type === "text" ? s[n + 1].content = s[n].content + s[n + 1].content : (n !== r && (s[r] = s[n]), r++);
  n !== r && (s.length = r);
};
var Zn = Gn;
var Xr = V.isWhiteSpace;
var Kr = V.isPunctChar;
var eo = V.isMdAsciiPunct;
function pt(t7, e, n, r) {
  this.src = t7, this.env = n, this.md = e, this.tokens = r, this.tokens_meta = Array(r.length), this.pos = 0, this.posMax = this.src.length, this.level = 0, this.pending = "", this.pendingLevel = 0, this.cache = {}, this.delimiters = [], this._prev_delimiters = [], this.backticks = {}, this.backticksScanned = false, this.linkLevel = 0;
}
pt.prototype.pushPending = function() {
  var t7 = new Zn("text", "", 0);
  return t7.content = this.pending, t7.level = this.pendingLevel, this.tokens.push(t7), this.pending = "", t7;
};
pt.prototype.push = function(t7, e, n) {
  this.pending && this.pushPending();
  var r = new Zn(t7, e, n), o = null;
  return n < 0 && (this.level--, this.delimiters = this._prev_delimiters.pop()), r.level = this.level, n > 0 && (this.level++, this._prev_delimiters.push(this.delimiters), this.delimiters = [], o = { delimiters: this.delimiters }), this.pendingLevel = this.level, this.tokens.push(r), this.tokens_meta.push(o), r;
};
pt.prototype.scanDelims = function(t7, e) {
  var n = t7, r, o, s, c, i, a, l, u, f, d = true, h7 = true, p = this.posMax, g = this.src.charCodeAt(t7);
  for (r = t7 > 0 ? this.src.charCodeAt(t7 - 1) : 32; n < p && this.src.charCodeAt(n) === g; )
    n++;
  return s = n - t7, o = n < p ? this.src.charCodeAt(n) : 32, l = eo(r) || Kr(String.fromCharCode(r)), f = eo(o) || Kr(String.fromCharCode(o)), a = Xr(r), u = Xr(o), u ? d = false : f && (a || l || (d = false)), a ? h7 = false : l && (u || f || (h7 = false)), e ? (c = d, i = h7) : (c = d && (!h7 || l), i = h7 && (!d || f)), {
    can_open: c,
    can_close: i,
    length: s
  };
};
pt.prototype.Token = Zn;
var d5 = pt;
var to = Vn;
var vn = [
  ["text", z4],
  ["linkify", U4],
  ["newline", V4],
  ["escape", $4],
  ["backticks", H4],
  ["strikethrough", Wt.tokenize],
  ["emphasis", Jt.tokenize],
  ["link", W4],
  ["image", Q4],
  ["autolink", K4],
  ["html_inline", o5],
  ["entity", l5]
];
var yn = [
  ["balance_pairs", u5],
  ["strikethrough", Wt.postProcess],
  ["emphasis", Jt.postProcess],
  // rules for pairs separate '**' into its own text tokens, which may be left unused,
  // rule below merges unused segments back with the rest of the text
  ["fragments_join", f5]
];
function ht() {
  var t7;
  for (this.ruler = new to(), t7 = 0; t7 < vn.length; t7++)
    this.ruler.push(vn[t7][0], vn[t7][1]);
  for (this.ruler2 = new to(), t7 = 0; t7 < yn.length; t7++)
    this.ruler2.push(yn[t7][0], yn[t7][1]);
}
ht.prototype.skipToken = function(t7) {
  var e, n, r = t7.pos, o = this.ruler.getRules(""), s = o.length, c = t7.md.options.maxNesting, i = t7.cache;
  if (typeof i[r] < "u") {
    t7.pos = i[r];
    return;
  }
  if (t7.level < c) {
    for (n = 0; n < s; n++)
      if (t7.level++, e = o[n](t7, true), t7.level--, e) {
        if (r >= t7.pos)
          throw new Error("inline rule didn't increment state.pos");
        break;
      }
  } else
    t7.pos = t7.posMax;
  e || t7.pos++, i[r] = t7.pos;
};
ht.prototype.tokenize = function(t7) {
  for (var e, n, r, o = this.ruler.getRules(""), s = o.length, c = t7.posMax, i = t7.md.options.maxNesting; t7.pos < c; ) {
    if (r = t7.pos, t7.level < i) {
      for (n = 0; n < s; n++)
        if (e = o[n](t7, false), e) {
          if (r >= t7.pos)
            throw new Error("inline rule didn't increment state.pos");
          break;
        }
    }
    if (e) {
      if (t7.pos >= c)
        break;
      continue;
    }
    t7.pending += t7.src[t7.pos++];
  }
  t7.pending && t7.pushPending();
};
ht.prototype.parse = function(t7, e, n, r) {
  var o, s, c, i = new this.State(t7, e, n, r);
  for (this.tokenize(i), s = this.ruler2.getRules(""), c = s.length, o = 0; o < c; o++)
    s[o](i);
};
ht.prototype.State = d5;
var p5 = ht;
var wn;
var no;
function h5() {
  return no || (no = 1, wn = function(t7) {
    var e = {};
    t7 = t7 || {}, e.src_Any = ts().source, e.src_Cc = ns().source, e.src_Z = rs().source, e.src_P = jn.source, e.src_ZPCc = [e.src_Z, e.src_P, e.src_Cc].join("|"), e.src_ZCc = [e.src_Z, e.src_Cc].join("|");
    var n = "[><｜]";
    return e.src_pseudo_letter = "(?:(?!" + n + "|" + e.src_ZPCc + ")" + e.src_Any + ")", e.src_ip4 = "(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)", e.src_auth = "(?:(?:(?!" + e.src_ZCc + "|[@/\\[\\]()]).)+@)?", e.src_port = "(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?", e.src_host_terminator = "(?=$|" + n + "|" + e.src_ZPCc + ")(?!" + (t7["---"] ? "-(?!--)|" : "-|") + "_|:\\d|\\.-|\\.(?!$|" + e.src_ZPCc + "))", e.src_path = "(?:[/?#](?:(?!" + e.src_ZCc + "|" + n + `|[()[\\]{}.,"'?!\\-;]).|\\[(?:(?!` + e.src_ZCc + "|\\]).)*\\]|\\((?:(?!" + e.src_ZCc + "|[)]).)*\\)|\\{(?:(?!" + e.src_ZCc + '|[}]).)*\\}|\\"(?:(?!' + e.src_ZCc + `|["]).)+\\"|\\'(?:(?!` + e.src_ZCc + "|[']).)+\\'|\\'(?=" + e.src_pseudo_letter + "|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!" + e.src_ZCc + "|[.]|$)|" + (t7["---"] ? "\\-(?!--(?:[^-]|$))(?:-*)|" : "\\-+|") + ",(?!" + e.src_ZCc + "|$)|;(?!" + e.src_ZCc + "|$)|\\!+(?!" + e.src_ZCc + "|[!]|$)|\\?(?!" + e.src_ZCc + "|[?]|$))+|\\/)?", e.src_email_name = '[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*', e.src_xn = "xn--[a-z0-9\\-]{1,59}", e.src_domain_root = // Allow letters & digits (http://test1)
    "(?:" + e.src_xn + "|" + e.src_pseudo_letter + "{1,63})", e.src_domain = "(?:" + e.src_xn + "|(?:" + e.src_pseudo_letter + ")|(?:" + e.src_pseudo_letter + "(?:-|" + e.src_pseudo_letter + "){0,61}" + e.src_pseudo_letter + "))", e.src_host = "(?:(?:(?:(?:" + e.src_domain + ")\\.)*" + e.src_domain + "))", e.tpl_host_fuzzy = "(?:" + e.src_ip4 + "|(?:(?:(?:" + e.src_domain + ")\\.)+(?:%TLDS%)))", e.tpl_host_no_ip_fuzzy = "(?:(?:(?:" + e.src_domain + ")\\.)+(?:%TLDS%))", e.src_host_strict = e.src_host + e.src_host_terminator, e.tpl_host_fuzzy_strict = e.tpl_host_fuzzy + e.src_host_terminator, e.src_host_port_strict = e.src_host + e.src_port + e.src_host_terminator, e.tpl_host_port_fuzzy_strict = e.tpl_host_fuzzy + e.src_port + e.src_host_terminator, e.tpl_host_port_no_ip_fuzzy_strict = e.tpl_host_no_ip_fuzzy + e.src_port + e.src_host_terminator, e.tpl_host_fuzzy_test = "localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:" + e.src_ZPCc + "|>|$))", e.tpl_email_fuzzy = "(^|" + n + '|"|\\(|' + e.src_ZCc + ")(" + e.src_email_name + "@" + e.tpl_host_fuzzy_strict + ")", e.tpl_link_fuzzy = // Fuzzy link can't be prepended with .:/\- and non punctuation.
    // but can start with > (markdown blockquote)
    "(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|" + e.src_ZPCc + "))((?![$+<=>^`|｜])" + e.tpl_host_port_fuzzy_strict + e.src_path + ")", e.tpl_link_no_ip_fuzzy = // Fuzzy link can't be prepended with .:/\- and non punctuation.
    // but can start with > (markdown blockquote)
    "(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|" + e.src_ZPCc + "))((?![$+<=>^`|｜])" + e.tpl_host_port_no_ip_fuzzy_strict + e.src_path + ")", e;
  }), wn;
}
function Nn(t7) {
  var e = Array.prototype.slice.call(arguments, 1);
  return e.forEach(function(n) {
    n && Object.keys(n).forEach(function(r) {
      t7[r] = n[r];
    });
  }), t7;
}
function Qt(t7) {
  return Object.prototype.toString.call(t7);
}
function m5(t7) {
  return Qt(t7) === "[object String]";
}
function g5(t7) {
  return Qt(t7) === "[object Object]";
}
function b5(t7) {
  return Qt(t7) === "[object RegExp]";
}
function ro(t7) {
  return Qt(t7) === "[object Function]";
}
function v5(t7) {
  return t7.replace(/[.?*+^$[\]\\(){}|-]/g, "\\$&");
}
var us = {
  fuzzyLink: true,
  fuzzyEmail: true,
  fuzzyIP: false
};
function y5(t7) {
  return Object.keys(t7 || {}).reduce(function(e, n) {
    return e || us.hasOwnProperty(n);
  }, false);
}
var w5 = {
  "http:": {
    validate: function(t7, e, n) {
      var r = t7.slice(e);
      return n.re.http || (n.re.http = new RegExp(
        "^\\/\\/" + n.re.src_auth + n.re.src_host_port_strict + n.re.src_path,
        "i"
      )), n.re.http.test(r) ? r.match(n.re.http)[0].length : 0;
    }
  },
  "https:": "http:",
  "ftp:": "http:",
  "//": {
    validate: function(t7, e, n) {
      var r = t7.slice(e);
      return n.re.no_http || (n.re.no_http = new RegExp(
        "^" + n.re.src_auth + // Don't allow single-level domains, because of false positives like '//test'
        // with code comments
        "(?:localhost|(?:(?:" + n.re.src_domain + ")\\.)+" + n.re.src_domain_root + ")" + n.re.src_port + n.re.src_host_terminator + n.re.src_path,
        "i"
      )), n.re.no_http.test(r) ? e >= 3 && t7[e - 3] === ":" || e >= 3 && t7[e - 3] === "/" ? 0 : r.match(n.re.no_http)[0].length : 0;
    }
  },
  "mailto:": {
    validate: function(t7, e, n) {
      var r = t7.slice(e);
      return n.re.mailto || (n.re.mailto = new RegExp(
        "^" + n.re.src_email_name + "@" + n.re.src_host_strict,
        "i"
      )), n.re.mailto.test(r) ? r.match(n.re.mailto)[0].length : 0;
    }
  }
};
var k5 = "a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]";
var x5 = "biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");
function _5(t7) {
  t7.__index__ = -1, t7.__text_cache__ = "";
}
function A5(t7) {
  return function(e, n) {
    var r = e.slice(n);
    return t7.test(r) ? r.match(t7)[0].length : 0;
  };
}
function oo() {
  return function(t7, e) {
    e.normalize(t7);
  };
}
function Nt(t7) {
  var e = t7.re = h5()(t7.__opts__), n = t7.__tlds__.slice();
  t7.onCompile(), t7.__tlds_replaced__ || n.push(k5), n.push(e.src_xn), e.src_tlds = n.join("|");
  function r(i) {
    return i.replace("%TLDS%", e.src_tlds);
  }
  e.email_fuzzy = RegExp(r(e.tpl_email_fuzzy), "i"), e.link_fuzzy = RegExp(r(e.tpl_link_fuzzy), "i"), e.link_no_ip_fuzzy = RegExp(r(e.tpl_link_no_ip_fuzzy), "i"), e.host_fuzzy_test = RegExp(r(e.tpl_host_fuzzy_test), "i");
  var o = [];
  t7.__compiled__ = {};
  function s(i, a) {
    throw new Error('(LinkifyIt) Invalid schema "' + i + '": ' + a);
  }
  Object.keys(t7.__schemas__).forEach(function(i) {
    var a = t7.__schemas__[i];
    if (a !== null) {
      var l = { validate: null, link: null };
      if (t7.__compiled__[i] = l, g5(a)) {
        b5(a.validate) ? l.validate = A5(a.validate) : ro(a.validate) ? l.validate = a.validate : s(i, a), ro(a.normalize) ? l.normalize = a.normalize : a.normalize ? s(i, a) : l.normalize = oo();
        return;
      }
      if (m5(a)) {
        o.push(i);
        return;
      }
      s(i, a);
    }
  }), o.forEach(function(i) {
    t7.__compiled__[t7.__schemas__[i]] && (t7.__compiled__[i].validate = t7.__compiled__[t7.__schemas__[i]].validate, t7.__compiled__[i].normalize = t7.__compiled__[t7.__schemas__[i]].normalize);
  }), t7.__compiled__[""] = { validate: null, normalize: oo() };
  var c = Object.keys(t7.__compiled__).filter(function(i) {
    return i.length > 0 && t7.__compiled__[i];
  }).map(v5).join("|");
  t7.re.schema_test = RegExp("(^|(?!_)(?:[><｜]|" + e.src_ZPCc + "))(" + c + ")", "i"), t7.re.schema_search = RegExp("(^|(?!_)(?:[><｜]|" + e.src_ZPCc + "))(" + c + ")", "ig"), t7.re.schema_at_start = RegExp("^" + t7.re.schema_search.source, "i"), t7.re.pretest = RegExp(
    "(" + t7.re.schema_test.source + ")|(" + t7.re.host_fuzzy_test.source + ")|@",
    "i"
  ), _5(t7);
}
function E5(t7, e) {
  var n = t7.__index__, r = t7.__last_index__, o = t7.__text_cache__.slice(n, r);
  this.schema = t7.__schema__.toLowerCase(), this.index = n + e, this.lastIndex = r + e, this.raw = o, this.text = o, this.url = o;
}
function In(t7, e) {
  var n = new E5(t7, e);
  return t7.__compiled__[n.schema].normalize(n, t7), n;
}
function fe(t7, e) {
  if (!(this instanceof fe))
    return new fe(t7, e);
  e || y5(t7) && (e = t7, t7 = {}), this.__opts__ = Nn({}, us, e), this.__index__ = -1, this.__last_index__ = -1, this.__schema__ = "", this.__text_cache__ = "", this.__schemas__ = Nn({}, w5, t7), this.__compiled__ = {}, this.__tlds__ = x5, this.__tlds_replaced__ = false, this.re = {}, Nt(this);
}
fe.prototype.add = function(e, n) {
  return this.__schemas__[e] = n, Nt(this), this;
};
fe.prototype.set = function(e) {
  return this.__opts__ = Nn(this.__opts__, e), this;
};
fe.prototype.test = function(e) {
  if (this.__text_cache__ = e, this.__index__ = -1, !e.length)
    return false;
  var n, r, o, s, c, i, a, l, u;
  if (this.re.schema_test.test(e)) {
    for (a = this.re.schema_search, a.lastIndex = 0; (n = a.exec(e)) !== null; )
      if (s = this.testSchemaAt(e, n[2], a.lastIndex), s) {
        this.__schema__ = n[2], this.__index__ = n.index + n[1].length, this.__last_index__ = n.index + n[0].length + s;
        break;
      }
  }
  return this.__opts__.fuzzyLink && this.__compiled__["http:"] && (l = e.search(this.re.host_fuzzy_test), l >= 0 && (this.__index__ < 0 || l < this.__index__) && (r = e.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) !== null && (c = r.index + r[1].length, (this.__index__ < 0 || c < this.__index__) && (this.__schema__ = "", this.__index__ = c, this.__last_index__ = r.index + r[0].length))), this.__opts__.fuzzyEmail && this.__compiled__["mailto:"] && (u = e.indexOf("@"), u >= 0 && (o = e.match(this.re.email_fuzzy)) !== null && (c = o.index + o[1].length, i = o.index + o[0].length, (this.__index__ < 0 || c < this.__index__ || c === this.__index__ && i > this.__last_index__) && (this.__schema__ = "mailto:", this.__index__ = c, this.__last_index__ = i))), this.__index__ >= 0;
};
fe.prototype.pretest = function(e) {
  return this.re.pretest.test(e);
};
fe.prototype.testSchemaAt = function(e, n, r) {
  return this.__compiled__[n.toLowerCase()] ? this.__compiled__[n.toLowerCase()].validate(e, r, this) : 0;
};
fe.prototype.match = function(e) {
  var n = 0, r = [];
  this.__index__ >= 0 && this.__text_cache__ === e && (r.push(In(this, n)), n = this.__last_index__);
  for (var o = n ? e.slice(n) : e; this.test(o); )
    r.push(In(this, n)), o = o.slice(this.__last_index__), n += this.__last_index__;
  return r.length ? r : null;
};
fe.prototype.matchAtStart = function(e) {
  if (this.__text_cache__ = e, this.__index__ = -1, !e.length) return null;
  var n = this.re.schema_at_start.exec(e);
  if (!n) return null;
  var r = this.testSchemaAt(e, n[2], n[0].length);
  return r ? (this.__schema__ = n[2], this.__index__ = n.index + n[1].length, this.__last_index__ = n.index + n[0].length + r, In(this, 0)) : null;
};
fe.prototype.tlds = function(e, n) {
  return e = Array.isArray(e) ? e : [e], n ? (this.__tlds__ = this.__tlds__.concat(e).sort().filter(function(r, o, s) {
    return r !== s[o - 1];
  }).reverse(), Nt(this), this) : (this.__tlds__ = e.slice(), this.__tlds_replaced__ = true, Nt(this), this);
};
fe.prototype.normalize = function(e) {
  e.schema || (e.url = "http://" + e.url), e.schema === "mailto:" && !/^mailto:/i.test(e.url) && (e.url = "mailto:" + e.url);
};
fe.prototype.onCompile = function() {
};
var C5 = fe;
var $e = 2147483647;
var we = 36;
var Wn = 1;
var ct = 26;
var D5 = 38;
var S5 = 700;
var fs = 72;
var ds = 128;
var ps = "-";
var q5 = /^xn--/;
var R5 = /[^\0-\x7F]/;
var T5 = /[\x2E\u3002\uFF0E\uFF61]/g;
var F5 = {
  overflow: "Overflow: input needs wider integers to process",
  "not-basic": "Illegal input >= 0x80 (not a basic code point)",
  "invalid-input": "Invalid input"
};
var kn = we - Wn;
var ke = Math.floor;
var xn = String.fromCharCode;
function De(t7) {
  throw new RangeError(F5[t7]);
}
function L5(t7, e) {
  const n = [];
  let r = t7.length;
  for (; r--; )
    n[r] = e(t7[r]);
  return n;
}
function hs(t7, e) {
  const n = t7.split("@");
  let r = "";
  n.length > 1 && (r = n[0] + "@", t7 = n[1]), t7 = t7.replace(T5, ".");
  const o = t7.split("."), s = L5(o, e).join(".");
  return r + s;
}
function Jn(t7) {
  const e = [];
  let n = 0;
  const r = t7.length;
  for (; n < r; ) {
    const o = t7.charCodeAt(n++);
    if (o >= 55296 && o <= 56319 && n < r) {
      const s = t7.charCodeAt(n++);
      (s & 64512) == 56320 ? e.push(((o & 1023) << 10) + (s & 1023) + 65536) : (e.push(o), n--);
    } else
      e.push(o);
  }
  return e;
}
var ms = (t7) => String.fromCodePoint(...t7);
var N5 = function(t7) {
  return t7 >= 48 && t7 < 58 ? 26 + (t7 - 48) : t7 >= 65 && t7 < 91 ? t7 - 65 : t7 >= 97 && t7 < 123 ? t7 - 97 : we;
};
var so = function(t7, e) {
  return t7 + 22 + 75 * (t7 < 26) - ((e != 0) << 5);
};
var gs = function(t7, e, n) {
  let r = 0;
  for (t7 = n ? ke(t7 / S5) : t7 >> 1, t7 += ke(t7 / e); t7 > kn * ct >> 1; r += we)
    t7 = ke(t7 / kn);
  return ke(r + (kn + 1) * t7 / (t7 + D5));
};
var Qn = function(t7) {
  const e = [], n = t7.length;
  let r = 0, o = ds, s = fs, c = t7.lastIndexOf(ps);
  c < 0 && (c = 0);
  for (let i = 0; i < c; ++i)
    t7.charCodeAt(i) >= 128 && De("not-basic"), e.push(t7.charCodeAt(i));
  for (let i = c > 0 ? c + 1 : 0; i < n; ) {
    const a = r;
    for (let u = 1, f = we; ; f += we) {
      i >= n && De("invalid-input");
      const d = N5(t7.charCodeAt(i++));
      d >= we && De("invalid-input"), d > ke(($e - r) / u) && De("overflow"), r += d * u;
      const h7 = f <= s ? Wn : f >= s + ct ? ct : f - s;
      if (d < h7)
        break;
      const p = we - h7;
      u > ke($e / p) && De("overflow"), u *= p;
    }
    const l = e.length + 1;
    s = gs(r - a, l, a == 0), ke(r / l) > $e - o && De("overflow"), o += ke(r / l), r %= l, e.splice(r++, 0, o);
  }
  return String.fromCodePoint(...e);
};
var Yn = function(t7) {
  const e = [];
  t7 = Jn(t7);
  const n = t7.length;
  let r = ds, o = 0, s = fs;
  for (const a of t7)
    a < 128 && e.push(xn(a));
  const c = e.length;
  let i = c;
  for (c && e.push(ps); i < n; ) {
    let a = $e;
    for (const u of t7)
      u >= r && u < a && (a = u);
    const l = i + 1;
    a - r > ke(($e - o) / l) && De("overflow"), o += (a - r) * l, r = a;
    for (const u of t7)
      if (u < r && ++o > $e && De("overflow"), u === r) {
        let f = o;
        for (let d = we; ; d += we) {
          const h7 = d <= s ? Wn : d >= s + ct ? ct : d - s;
          if (f < h7)
            break;
          const p = f - h7, g = we - h7;
          e.push(
            xn(so(h7 + p % g, 0))
          ), f = ke(p / g);
        }
        e.push(xn(so(f, 0))), s = gs(o, l, i === c), o = 0, ++i;
      }
    ++o, ++r;
  }
  return e.join("");
};
var bs = function(t7) {
  return hs(t7, function(e) {
    return q5.test(e) ? Qn(e.slice(4).toLowerCase()) : e;
  });
};
var vs = function(t7) {
  return hs(t7, function(e) {
    return R5.test(e) ? "xn--" + Yn(e) : e;
  });
};
var I5 = {
  /**
   * A string representing the current Punycode.js version number.
   * @memberOf punycode
   * @type String
   */
  version: "2.3.1",
  /**
   * An object of methods to convert from JavaScript's internal character
   * representation (UCS-2) to Unicode code points, and back.
   * @see <https://mathiasbynens.be/notes/javascript-encoding>
   * @memberOf punycode
   * @type Object
   */
  ucs2: {
    decode: Jn,
    encode: ms
  },
  decode: Qn,
  encode: Yn,
  toASCII: vs,
  toUnicode: bs
};
var B5 = Object.freeze(Object.defineProperty({
  __proto__: null,
  decode: Qn,
  default: I5,
  encode: Yn,
  toASCII: vs,
  toUnicode: bs,
  ucs2decode: Jn,
  ucs2encode: ms
}, Symbol.toStringTag, { value: "Module" }));
var O5 = $l(B5);
var P5 = {
  options: {
    html: false,
    // Enable HTML tags in source
    xhtmlOut: false,
    // Use '/' to close single tags (<br />)
    breaks: false,
    // Convert '\n' in paragraphs into <br>
    langPrefix: "language-",
    // CSS language prefix for fenced blocks
    linkify: false,
    // autoconvert URL-like texts to links
    // Enable some language-neutral replacements + quotes beautification
    typographer: false,
    // Double + single quotes replacement pairs, when typographer enabled,
    // and smartquotes on. Could be either a String or an Array.
    //
    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,
    // and ['«\xA0', '\xA0»', '‹\xA0', '\xA0›'] for French (including nbsp).
    quotes: "“”‘’",
    /* “”‘’ */
    // Highlighter function. Should return escaped HTML,
    // or '' if the source string is not changed and should be escaped externaly.
    // If result starts with <pre... internal wrapper is skipped.
    //
    // function (/*str, lang*/) { return ''; }
    //
    highlight: null,
    maxNesting: 100
    // Internal protection, recursion limit
  },
  components: {
    core: {},
    block: {},
    inline: {}
  }
};
var z5 = {
  options: {
    html: false,
    // Enable HTML tags in source
    xhtmlOut: false,
    // Use '/' to close single tags (<br />)
    breaks: false,
    // Convert '\n' in paragraphs into <br>
    langPrefix: "language-",
    // CSS language prefix for fenced blocks
    linkify: false,
    // autoconvert URL-like texts to links
    // Enable some language-neutral replacements + quotes beautification
    typographer: false,
    // Double + single quotes replacement pairs, when typographer enabled,
    // and smartquotes on. Could be either a String or an Array.
    //
    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,
    // and ['«\xA0', '\xA0»', '‹\xA0', '\xA0›'] for French (including nbsp).
    quotes: "“”‘’",
    /* “”‘’ */
    // Highlighter function. Should return escaped HTML,
    // or '' if the source string is not changed and should be escaped externaly.
    // If result starts with <pre... internal wrapper is skipped.
    //
    // function (/*str, lang*/) { return ''; }
    //
    highlight: null,
    maxNesting: 20
    // Internal protection, recursion limit
  },
  components: {
    core: {
      rules: [
        "normalize",
        "block",
        "inline",
        "text_join"
      ]
    },
    block: {
      rules: [
        "paragraph"
      ]
    },
    inline: {
      rules: [
        "text"
      ],
      rules2: [
        "balance_pairs",
        "fragments_join"
      ]
    }
  }
};
var M5 = {
  options: {
    html: true,
    // Enable HTML tags in source
    xhtmlOut: true,
    // Use '/' to close single tags (<br />)
    breaks: false,
    // Convert '\n' in paragraphs into <br>
    langPrefix: "language-",
    // CSS language prefix for fenced blocks
    linkify: false,
    // autoconvert URL-like texts to links
    // Enable some language-neutral replacements + quotes beautification
    typographer: false,
    // Double + single quotes replacement pairs, when typographer enabled,
    // and smartquotes on. Could be either a String or an Array.
    //
    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,
    // and ['«\xA0', '\xA0»', '‹\xA0', '\xA0›'] for French (including nbsp).
    quotes: "“”‘’",
    /* “”‘’ */
    // Highlighter function. Should return escaped HTML,
    // or '' if the source string is not changed and should be escaped externaly.
    // If result starts with <pre... internal wrapper is skipped.
    //
    // function (/*str, lang*/) { return ''; }
    //
    highlight: null,
    maxNesting: 20
    // Internal protection, recursion limit
  },
  components: {
    core: {
      rules: [
        "normalize",
        "block",
        "inline",
        "text_join"
      ]
    },
    block: {
      rules: [
        "blockquote",
        "code",
        "fence",
        "heading",
        "hr",
        "html_block",
        "lheading",
        "list",
        "reference",
        "paragraph"
      ]
    },
    inline: {
      rules: [
        "autolink",
        "backticks",
        "emphasis",
        "entity",
        "escape",
        "html_inline",
        "image",
        "link",
        "newline",
        "text"
      ],
      rules2: [
        "balance_pairs",
        "emphasis",
        "fragments_join"
      ]
    }
  }
};
var ot = V;
var U5 = Gt;
var j5 = N$;
var V5 = o4;
var G5 = O4;
var $5 = p5;
var H5 = C5;
var Ie = Qe;
var ys = O5;
var Z5 = {
  default: P5,
  zero: z5,
  commonmark: M5
};
var W5 = /^(vbscript|javascript|file|data):/;
var J5 = /^data:image\/(gif|png|jpeg|webp);/;
function Q5(t7) {
  var e = t7.trim().toLowerCase();
  return W5.test(e) ? !!J5.test(e) : true;
}
var ws = ["http:", "https:", "mailto:"];
function Y5(t7) {
  var e = Ie.parse(t7, true);
  if (e.hostname && (!e.protocol || ws.indexOf(e.protocol) >= 0))
    try {
      e.hostname = ys.toASCII(e.hostname);
    } catch {
    }
  return Ie.encode(Ie.format(e));
}
function X5(t7) {
  var e = Ie.parse(t7, true);
  if (e.hostname && (!e.protocol || ws.indexOf(e.protocol) >= 0))
    try {
      e.hostname = ys.toUnicode(e.hostname);
    } catch {
    }
  return Ie.decode(Ie.format(e), Ie.decode.defaultChars + "%");
}
function pe(t7, e) {
  if (!(this instanceof pe))
    return new pe(t7, e);
  e || ot.isString(t7) || (e = t7 || {}, t7 = "default"), this.inline = new $5(), this.block = new G5(), this.core = new V5(), this.renderer = new j5(), this.linkify = new H5(), this.validateLink = Q5, this.normalizeLink = Y5, this.normalizeLinkText = X5, this.utils = ot, this.helpers = ot.assign({}, U5), this.options = {}, this.configure(t7), e && this.set(e);
}
pe.prototype.set = function(t7) {
  return ot.assign(this.options, t7), this;
};
pe.prototype.configure = function(t7) {
  var e = this, n;
  if (ot.isString(t7) && (n = t7, t7 = Z5[n], !t7))
    throw new Error('Wrong `markdown-it` preset "' + n + '", check name');
  if (!t7)
    throw new Error("Wrong `markdown-it` preset, can't be empty");
  return t7.options && e.set(t7.options), t7.components && Object.keys(t7.components).forEach(function(r) {
    t7.components[r].rules && e[r].ruler.enableOnly(t7.components[r].rules), t7.components[r].rules2 && e[r].ruler2.enableOnly(t7.components[r].rules2);
  }), this;
};
pe.prototype.enable = function(t7, e) {
  var n = [];
  Array.isArray(t7) || (t7 = [t7]), ["core", "block", "inline"].forEach(function(o) {
    n = n.concat(this[o].ruler.enable(t7, true));
  }, this), n = n.concat(this.inline.ruler2.enable(t7, true));
  var r = t7.filter(function(o) {
    return n.indexOf(o) < 0;
  });
  if (r.length && !e)
    throw new Error("MarkdownIt. Failed to enable unknown rule(s): " + r);
  return this;
};
pe.prototype.disable = function(t7, e) {
  var n = [];
  Array.isArray(t7) || (t7 = [t7]), ["core", "block", "inline"].forEach(function(o) {
    n = n.concat(this[o].ruler.disable(t7, true));
  }, this), n = n.concat(this.inline.ruler2.disable(t7, true));
  var r = t7.filter(function(o) {
    return n.indexOf(o) < 0;
  });
  if (r.length && !e)
    throw new Error("MarkdownIt. Failed to disable unknown rule(s): " + r);
  return this;
};
pe.prototype.use = function(t7) {
  var e = [this].concat(Array.prototype.slice.call(arguments, 1));
  return t7.apply(t7, e), this;
};
pe.prototype.parse = function(t7, e) {
  if (typeof t7 != "string")
    throw new Error("Input data should be a String");
  var n = new this.core.State(t7, this, e);
  return this.core.process(n), n.tokens;
};
pe.prototype.render = function(t7, e) {
  return e = e || {}, this.renderer.render(this.parse(t7, e), this.options, e);
};
pe.prototype.parseInline = function(t7, e) {
  var n = new this.core.State(t7, this, e);
  return n.inlineMode = true, this.core.process(n), n.tokens;
};
pe.prototype.renderInline = function(t7, e) {
  return e = e || {}, this.renderer.render(this.parseInline(t7, e), this.options, e);
};
var K5 = pe;
var e6 = K5;
var t6 = Gl(e6);
var n6 = defineComponent({
  name: "VueMarkdown",
  props: {
    source: {
      type: String,
      required: true
    },
    options: {
      type: Object,
      required: false
    },
    plugins: {
      type: Array,
      required: false
    }
  },
  setup(t7) {
    const e = ref(new t6(t7.options ?? {}));
    for (const r of t7.plugins ?? [])
      e.value.use(r);
    const n = computed(() => e.value.render(t7.source));
    return () => h("div", { innerHTML: n.value });
  }
});
var r6 = { class: "q-chatbot__message-container" };
var o6 = { class: "q-chatbot__message-wrapper" };
var s6 = {
  key: 0,
  class: "q-chatbot__image-preview"
};
var c6 = ["src", "alt"];
var i6 = { class: "q-chatbot__message" };
var a6 = {
  key: 1,
  class: "q-chatbot__text"
};
var l6 = { class: "q-chatbot__dialog-title" };
var u6 = {
  key: 1,
  class: "q-chatbot__feedback-buttons"
};
var f6 = { class: "q-chatbot__sender" };
var d6 = defineComponent({
  __name: "CBMessage",
  props: {
    sender: { default: "user" },
    message: {},
    date: { default: () => /* @__PURE__ */ new Date() },
    loading: { type: Boolean },
    dateFormat: {},
    imagePreviewUrl: {},
    texts: { default: () => ({
      commentDialogTitle: "Would you like to add a comment?",
      commentPlaceholder: "Type your comment here (optional)...",
      goodResponse: "Good response",
      badResponse: "Bad response",
      copyResponse: "Copy response",
      submitButton: "Submit",
      cancelButton: "Cancel",
      senderImage: "Sender Image",
      imagePreview: "Image preview"
    }) },
    apiEndpoint: {},
    sessionID: {},
    userImage: { default: void 0 },
    chatbotImage: {},
    isWelcomeMessage: { type: Boolean }
  },
  setup(t7) {
    const e = t7, n = ref(false), r = ref(""), o = ref(null), s = [
      {
        id: "confirm-btn",
        action: d,
        props: {
          label: e.texts.submitButton,
          variant: "bold"
        },
        icon: {
          icon: "submit"
        }
      },
      {
        id: "cancel-btn",
        props: {
          label: e.texts.cancelButton
        },
        icon: {
          icon: "cancel"
        }
      }
    ], c = computed(() => e.sender === "bot" && !Object.values(e.texts || {}).includes(e.message || "") && !e.isWelcomeMessage), i = computed(() => e.dateFormat ? u(e.date, "HH:mm") : e.date.toLocaleString()), a = computed(() => `${i.value}`), l = computed(
      () => e.sender === "bot" ? e.chatbotImage : e.userImage
    );
    function u(g, m) {
      const v = g.getDate().toString().padStart(2, "0"), y = (g.getMonth() + 1).toString().padStart(2, "0"), E = g.getFullYear().toString().padStart(2, "0"), D = g.getHours().toString().padStart(2, "0"), S = g.getMinutes().toString().padStart(2, "0"), _ = g.getSeconds().toString().padStart(2, "0");
      return m.replace("dd", v).replace("MM", y).replace("yyyy", E).replace("HH", D).replace("mm", S).replace("ss", _);
    }
    function f(g) {
      n.value = true, r.value = "", o.value = g;
    }
    function d() {
      o.value != null && h7(o.value, r.value);
    }
    function h7(g, m) {
      Y.post(e.apiEndpoint + "/prompt/feedback", {
        messageSessionID: e.sessionID,
        feedbackValue: g,
        feedbackComment: m
      }).catch((v) => {
        console.error("Error sending message feedback: ", v);
      });
    }
    function p() {
      e.message && navigator.clipboard.writeText(e.message).then(() => {
        console.log("Message copied to clipboard");
      }).catch((g) => {
        console.error("Failed to copy message to clipboard: ", g);
      });
    }
    return (g, m) => {
      const v = resolveComponent("q-button-group");
      return openBlock(), createElementBlock("div", r6, [
        createVNode(unref(de), {
          type: "img",
          icon: l.value,
          alt: e.texts.senderImage,
          class: "q-chatbot__profile"
        }, null, 8, ["icon", "alt"]),
        createBaseVNode("div", o6, [
          e.imagePreviewUrl && e.imagePreviewUrl.length > 0 ? (openBlock(), createElementBlock("div", s6, [
            createBaseVNode("img", {
              src: e.imagePreviewUrl,
              alt: e.texts.imagePreview
            }, null, 8, c6)
          ])) : createCommentVNode("", true),
          createBaseVNode("div", i6, [
            g.loading ? (openBlock(), createBlock(Vl, { key: 0 })) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
              e.sender === "bot" ? (openBlock(), createBlock(unref(n6), {
                key: 0,
                class: "q-chatbot__text",
                source: e.message || ""
              }, null, 8, ["source"])) : (openBlock(), createElementBlock("div", a6, toDisplayString(e.message), 1))
            ], 64))
          ]),
          createVNode(unref(Pl), {
            id: "comment-dialog",
            modelValue: n.value,
            "onUpdate:modelValue": m[1] || (m[1] = (y) => n.value = y),
            buttons: s
          }, {
            "body.content": withCtx(() => [
              createBaseVNode("div", l6, toDisplayString(e.texts.commentDialogTitle), 1),
              createVNode(unref(ca), {
                modelValue: r.value,
                "onUpdate:modelValue": m[0] || (m[0] = (y) => r.value = y),
                maxLength: 150,
                size: "large",
                placeholder: e.texts.commentPlaceholder
              }, null, 8, ["modelValue", "placeholder"])
            ]),
            _: 1
          }, 8, ["modelValue"]),
          c.value ? (openBlock(), createElementBlock("div", u6, [
            createVNode(v, null, {
              default: withCtx(() => [
                createVNode(unref(ye), {
                  title: e.texts.goodResponse,
                  borderless: "",
                  disabled: g.loading,
                  onClick: m[2] || (m[2] = (y) => f(1))
                }, {
                  default: withCtx(() => [
                    createVNode(unref(de), { icon: "thumb-up" })
                  ]),
                  _: 1
                }, 8, ["title", "disabled"]),
                createVNode(unref(ye), {
                  title: e.texts.badResponse,
                  borderless: "",
                  disabled: g.loading,
                  onClick: m[3] || (m[3] = (y) => f(0))
                }, {
                  default: withCtx(() => [
                    createVNode(unref(de), { icon: "thumb-down" })
                  ]),
                  _: 1
                }, 8, ["title", "disabled"]),
                createVNode(unref(ye), {
                  title: e.texts.copyResponse,
                  borderless: "",
                  disabled: g.loading,
                  onClick: p
                }, {
                  default: withCtx(() => [
                    createVNode(unref(de), { icon: "copy-content" })
                  ]),
                  _: 1
                }, 8, ["title", "disabled"])
              ]),
              _: 1
            })
          ])) : createCommentVNode("", true),
          createBaseVNode("div", f6, toDisplayString(a.value), 1)
        ])
      ]);
    };
  }
});
var te = [];
for (let t7 = 0; t7 < 256; ++t7)
  te.push((t7 + 256).toString(16).slice(1));
function p6(t7, e = 0) {
  return (te[t7[e + 0]] + te[t7[e + 1]] + te[t7[e + 2]] + te[t7[e + 3]] + "-" + te[t7[e + 4]] + te[t7[e + 5]] + "-" + te[t7[e + 6]] + te[t7[e + 7]] + "-" + te[t7[e + 8]] + te[t7[e + 9]] + "-" + te[t7[e + 10]] + te[t7[e + 11]] + te[t7[e + 12]] + te[t7[e + 13]] + te[t7[e + 14]] + te[t7[e + 15]]).toLowerCase();
}
var _n;
var h6 = new Uint8Array(16);
function m6() {
  if (!_n) {
    if (typeof crypto > "u" || !crypto.getRandomValues)
      throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
    _n = crypto.getRandomValues.bind(crypto);
  }
  return _n(h6);
}
var g6 = typeof crypto < "u" && crypto.randomUUID && crypto.randomUUID.bind(crypto);
var co = { randomUUID: g6 };
function b6(t7, e, n) {
  var o;
  if (co.randomUUID && !t7)
    return co.randomUUID();
  t7 = t7 || {};
  const r = t7.random ?? ((o = t7.rng) == null ? void 0 : o.call(t7)) ?? m6();
  if (r.length < 16)
    throw new Error("Random bytes length must be >= 16");
  return r[6] = r[6] & 15 | 64, r[8] = r[8] & 63 | 128, p6(r);
}
var v6 = "data:image/png;base64,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";
var y6 = "data:image/png;base64,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";
var w6 = { class: "q-chatbot" };
var k6 = { class: "q-chatbot__content" };
var x6 = { class: "q-chatbot__tools" };
var _6 = { class: "q-chatbot__footer-container" };
var A6 = { class: "q-chatbot__input-wrapper" };
var E6 = {
  key: 0,
  class: "q-chatbot__image-preview"
};
var C6 = ["src"];
var D6 = { class: "q-chatbot__input" };
var S6 = { class: "q-chatbot__send-container" };
var q6 = ["accept"];
var J6 = defineComponent({
  name: "ChatBot",
  __name: "ChatBot",
  props: {
    apiEndpoint: { default: "http://localhost:3000" },
    controllerEndpoint: {},
    texts: { default: () => ({
      chatbotTitle: "ChatBot",
      sendMessage: "Send message",
      clearChat: "Clear chat",
      inputLabel: "What can I help with?",
      imageUpload: "Upload Image",
      imageUploadQButton: "Upload Image",
      goodResponse: "Good response",
      badResponse: "Bad response",
      initialMessage: "Howdy! I am GenioBot 👋, Quidgest's personal AI assistant! How can I help you?",
      initialAgentMessage: "Just a temporary message while we are working on the agent mode",
      loginError: "Uh oh, I could not authenticate with the Quidgest API endpoint 😓",
      botIsSick: "*cough cough* GenioBot is not feeling alright 🥴️🤒, looks like something failed!"
    }) },
    username: {},
    projectPath: {},
    userImage: { default: y6 },
    chatbotImage: { default: v6 },
    dateFormat: {},
    mode: { default: "chat" }
  },
  setup(t7) {
    const e = ref([]), n = ref(1), r = ref(""), o = ref(false), s = ref(false), c = ref(null), i = ref(true), a = ref(null), l = ref(null), u = computed(() => ".png, .jpeg, .jpg, .svg, .webp"), f = ref(false), d = ref(false), h7 = computed(() => r.value.trim().length === 0 || o.value || s.value), p = t7;
    onMounted(() => {
      y(), nextTick(() => {
        var q;
        (q = c.value) == null || q.addEventListener("scroll", K);
      });
    });
    const g = computed(() => s.value || o.value), m = computed(() => ({
      "q-chatbot__footer-disabled": g.value,
      "drag-over": d.value && !f.value
    }));
    function v(q) {
      s.value = q;
    }
    async function y() {
      try {
        await Y.post(p.apiEndpoint + "/auth/login", {
          username: p.username,
          password: "test"
        }), E();
      } catch (q) {
        v(true), D(p.texts.loginError), console.log("Error logging in: " + q);
      }
    }
    async function E() {
      try {
        const q = await Y.post(p.apiEndpoint + "/prompt/load", {
          username: p.username,
          project: p.projectPath
        });
        if (!q) return console.error("No response from server");
        if (q.status !== 200 || !q.data.success) {
          v(true), D(p.texts.botIsSick), console.log(`Unsuccessful load, endpoint gave status ${q.status}`);
          return;
        }
        _(), q.data.history.forEach((z) => {
          const M = z.imageUrl ? p.controllerEndpoint + z.imageUrl : void 0;
          D(
            z.content,
            z.type === "ai" ? "bot" : "user",
            M,
            z.sessionID
          );
        });
      } catch (q) {
        v(true), D(p.texts.botIsSick), console.log("Error loading: " + q);
      }
    }
    function D(q, z = "bot", M = null, ue, ee) {
      e.value.push({
        id: n.value++,
        message: q,
        date: /* @__PURE__ */ new Date(),
        sender: z,
        imagePreviewUrl: M ?? void 0,
        sessionID: ue || b6(),
        isWelcomeMessage: ee ?? false
      }), nextTick(() => {
        i.value && U();
      });
    }
    function S() {
      return e.value.find(
        (q) => q.id === n.value - 1
      );
    }
    function _() {
      const q = p.mode === "chat" ? p.texts.initialMessage : p.texts.initialAgentMessage;
      D(q, "bot", null, void 0, true);
    }
    function T() {
      e.value = [], r.value = "", o.value = false, v(false), i.value = true;
    }
    function U() {
      nextTick(() => {
        c.value && (c.value.scrollTop = c.value.scrollHeight);
      });
    }
    function K() {
      if (c.value) {
        const { scrollTop: z, clientHeight: M, scrollHeight: ue } = c.value;
        z + M >= ue - 20 ? i.value = true : i.value = false;
      }
    }
    function P() {
      l.value = "", a.value && (a.value.value = ""), f.value = false;
    }
    function w() {
      var q;
      (q = a.value) == null || q.click();
    }
    function x(q) {
      const z = q.target;
      a.value = z, z.files && z.files[0] && (l.value = URL.createObjectURL(z.files[0]), f.value = true);
    }
    function R() {
      var q, z;
      r.value.trim().length === 0 || o.value || s.value || (c.value && c.value.scrollTo({
        top: c.value.scrollHeight,
        behavior: "smooth"
      }), D(r.value, "user", l.value), k(r.value, (z = (q = a.value) == null ? void 0 : q.files) == null ? void 0 : z[0]), P(), r.value = "");
    }
    async function k(q, z) {
      D("", "bot");
      let M = S();
      const ue = (M == null ? void 0 : M.sessionID) || "", ee = new FormData();
      z && ee.append("image", z), ee.append("message", q), ee.append("project", p.projectPath), ee.append("user", p.username), ee.append("sessionID", ue), o.value = true;
      try {
        const he = await Y.post(p.apiEndpoint + "/prompt/submit", ee, {
          headers: {
            "Content-Type": "text/event-stream",
            Accept: "text/event-stream"
          },
          responseType: "stream",
          adapter: "fetch"
        });
        if (!he) return console.error("No response from server");
        const Re = he.data.getReader(), Yt = new TextDecoder("utf-8");
        for (; ; ) {
          const { done: I, value: Ae } = await Re.read();
          if (I) break;
          const mt = Yt.decode(Ae, { stream: true }).match(/data:\s*({.*?})/g);
          if (mt) {
            for (const ks of mt)
              try {
                const Xt = ks.split("data:")[1].trim(), xs = JSON.parse(Xt);
                M && (M.message += xs.value);
              } catch (Xt) {
                console.error("Error parsing match:", Xt);
              }
            i.value && U();
          }
        }
        o.value = false;
      } catch (he) {
        v(true), D(p.texts.botIsSick), console.log("Error setting chat prompt: " + he);
        return;
      }
    }
    function C() {
      Y.post(p.apiEndpoint + "/prompt/clear", {
        username: p.username,
        project: p.projectPath
      }).then((q) => {
        if (q.status !== 200 || !q.data.success) {
          v(true), D(p.texts.loginError), console.log(`Unsuccessful clear, endpoint gave status ${q.status}`);
          return;
        }
        T(), _();
      }).catch((q) => {
        v(true), D(p.texts.loginError), console.log("Error clearing chat: " + q);
      });
    }
    function A(q) {
      const z = ["q-chatbot__messages-wrapper"];
      return q === "user" && z.push("q-chatbot__messages-wrapper_right"), z;
    }
    function N(q) {
      q.preventDefault(), g.value || (d.value = true);
    }
    function J(q) {
      q.preventDefault(), d.value = false;
    }
    function H(q) {
      var M;
      if (q.preventDefault(), d.value = false, g.value || f.value)
        return;
      const z = (M = q.dataTransfer) == null ? void 0 : M.files;
      if (z && z.length > 0) {
        const ue = z[0];
        if (ue.type.startsWith("image/") && a.value) {
          const ee = new DataTransfer();
          ee.items.add(ue), a.value.files = ee.files, l.value = URL.createObjectURL(ue), f.value = true;
        }
      }
    }
    return watch(
      () => p.apiEndpoint,
      () => {
        T(), y();
      }
    ), (q, z) => (openBlock(), createElementBlock("div", w6, [
      createBaseVNode("div", k6, [
        createBaseVNode("div", x6, [
          createVNode(unref(ye), {
            title: p.texts.clearChat,
            disabled: s.value,
            borderless: "",
            onClick: C
          }, {
            default: withCtx(() => [
              createVNode(unref(de), { icon: "bin" })
            ]),
            _: 1
          }, 8, ["title", "disabled"])
        ]),
        createBaseVNode("div", {
          ref_key: "messagesContainer",
          ref: c,
          class: "q-chatbot__messages-container",
          onScroll: K
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(e.value, (M) => (openBlock(), createElementBlock("div", {
            key: M.id,
            class: normalizeClass(A(M.sender))
          }, [
            createVNode(unref(d6), mergeProps({ ref_for: true }, M, {
              "date-format": p.dateFormat,
              "user-image": p.userImage,
              "chatbot-image": p.chatbotImage,
              loading: o.value && !M.message,
              imagePreviewUrl: M.imagePreviewUrl,
              apiEndpoint: p.apiEndpoint,
              sessionID: M.sessionID
            }), null, 16, ["date-format", "user-image", "chatbot-image", "loading", "imagePreviewUrl", "apiEndpoint", "sessionID"])
          ], 2))), 128))
        ], 544)
      ]),
      createBaseVNode("div", _6, [
        createVNode(unref(Go), {
          label: p.texts.inputLabel
        }, null, 8, ["label"]),
        createBaseVNode("div", {
          class: normalizeClass(["q-chatbot__footer", m.value]),
          onDragover: withModifiers(N, ["prevent"]),
          onDragleave: withModifiers(J, ["prevent"]),
          onDrop: withModifiers(H, ["prevent"])
        }, [
          createBaseVNode("div", A6, [
            l.value ? (openBlock(), createElementBlock("div", E6, [
              createBaseVNode("img", {
                src: l.value,
                tabindex: "0",
                alt: "Image preview"
              }, null, 8, C6),
              createVNode(unref(ye), {
                class: "q-chatbot__remove-image",
                tabindex: "0",
                flat: "",
                round: "",
                onClick: P
              }, {
                default: withCtx(() => [
                  createVNode(unref(de), { icon: "bin" })
                ]),
                _: 1
              })
            ])) : createCommentVNode("", true),
            createBaseVNode("div", D6, [
              createVNode(unref(Ul), {
                modelValue: r.value,
                "onUpdate:modelValue": z[0] || (z[0] = (M) => r.value = M),
                size: "block",
                autosize: "",
                resize: "none",
                rows: 2,
                disabled: g.value,
                onKeyup: withKeys(R, ["enter"])
              }, null, 8, ["modelValue", "disabled"])
            ])
          ]),
          createBaseVNode("div", S6, [
            createVNode(unref(ye), {
              title: p.texts.imageUpload,
              class: "q-chatbot__upload",
              disabled: s.value || o.value || f.value,
              onClick: w
            }, {
              default: withCtx(() => [
                createVNode(unref(de), { icon: "upload" })
              ]),
              _: 1
            }, 8, ["title", "disabled"]),
            createBaseVNode("input", {
              id: "image-upload",
              type: "file",
              ref_key: "imageInput",
              ref: a,
              onChange: x,
              accept: u.value,
              class: "hidden-input",
              style: { display: "none" }
            }, null, 40, q6),
            createVNode(unref(ye), {
              title: p.texts.sendMessage,
              variant: "bold",
              class: "q-chatbot__send",
              disabled: h7.value,
              readonly: h7.value,
              loading: o.value,
              onClick: R
            }, {
              default: withCtx(() => [
                createVNode(unref(de), { icon: "send" })
              ]),
              _: 1
            }, 8, ["title", "disabled", "readonly", "loading"])
          ])
        ], 34)
      ])
    ]));
  }
});
export {
  J6 as default
};
/*! Bundled license information:

@quidgest/chatbot/dist/index.mjs:
  (*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)
  (*!
  * focus-trap 7.6.4
  * @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
  *)
*/
//# sourceMappingURL=@quidgest_chatbot.js.map
