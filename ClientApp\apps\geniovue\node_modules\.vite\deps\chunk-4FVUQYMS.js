import {
  isArray_default
} from "./chunk-VO4BPRKV.js";
import {
  isObjectLike_default
} from "./chunk-VB7E2QJD.js";
import {
  baseGetTag_default
} from "./chunk-ZJQW7BA7.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isString.js
var stringTag = "[object String]";
function isString(value) {
  return typeof value == "string" || !isArray_default(value) && isObjectLike_default(value) && baseGetTag_default(value) == stringTag;
}
var isString_default = isString;

export {
  isString_default
};
//# sourceMappingURL=chunk-4FVUQYMS.js.map
