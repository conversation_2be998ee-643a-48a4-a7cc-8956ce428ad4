{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/last.js"], "sourcesContent": ["/**\n * Gets the last element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {*} Returns the last element of `array`.\n * @example\n *\n * _.last([1, 2, 3]);\n * // => 3\n */\nfunction last(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? array[length - 1] : undefined;\n}\n\nexport default last;\n"], "mappings": ";AAcA,SAAS,KAAK,OAAO;AACnB,MAAI,SAAS,SAAS,OAAO,IAAI,MAAM;AACvC,SAAO,SAAS,MAAM,SAAS,CAAC,IAAI;AACtC;AAEA,IAAO,eAAQ;", "names": []}