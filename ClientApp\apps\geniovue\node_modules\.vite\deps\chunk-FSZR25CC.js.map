{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_createAssigner.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function(object, sources) {\n    var index = -1,\n        length = sources.length,\n        customizer = length > 1 ? sources[length - 1] : undefined,\n        guard = length > 2 ? sources[2] : undefined;\n\n    customizer = (assigner.length > 3 && typeof customizer == 'function')\n      ? (length--, customizer)\n      : undefined;\n\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\n\nexport default createAssigner;\n"], "mappings": ";;;;;;;;AAUA,SAAS,eAAe,UAAU;AAChC,SAAO,iBAAS,SAAS,QAAQ,SAAS;AACxC,QAAI,QAAQ,IACR,SAAS,QAAQ,QACjB,aAAa,SAAS,IAAI,QAAQ,SAAS,CAAC,IAAI,QAChD,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI;AAEtC,iBAAc,SAAS,SAAS,KAAK,OAAO,cAAc,cACrD,UAAU,cACX;AAEJ,QAAI,SAAS,uBAAe,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG;AAC1D,mBAAa,SAAS,IAAI,SAAY;AACtC,eAAS;AAAA,IACX;AACA,aAAS,OAAO,MAAM;AACtB,WAAO,EAAE,QAAQ,QAAQ;AACvB,UAAI,SAAS,QAAQ,KAAK;AAC1B,UAAI,QAAQ;AACV,iBAAS,QAAQ,QAAQ,OAAO,UAAU;AAAA,MAC5C;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAO,yBAAQ;", "names": []}