{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseFindIndex.js"], "sourcesContent": ["/**\n * The base implementation of `_.findIndex` and `_.findLastIndex` without\n * support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} predicate The function invoked per iteration.\n * @param {number} fromIndex The index to search from.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseFindIndex(array, predicate, fromIndex, fromRight) {\n  var length = array.length,\n      index = fromIndex + (fromRight ? 1 : -1);\n\n  while ((fromRight ? index-- : ++index < length)) {\n    if (predicate(array[index], index, array)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\nexport default baseFindIndex;\n"], "mappings": ";AAWA,SAAS,cAAc,OAAO,WAAW,WAAW,WAAW;AAC7D,MAAI,SAAS,MAAM,QACf,QAAQ,aAAa,YAAY,IAAI;AAEzC,SAAQ,YAAY,UAAU,EAAE,QAAQ,QAAS;AAC/C,QAAI,UAAU,MAAM,KAAK,GAAG,OAAO,KAAK,GAAG;AACzC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAO,wBAAQ;", "names": []}