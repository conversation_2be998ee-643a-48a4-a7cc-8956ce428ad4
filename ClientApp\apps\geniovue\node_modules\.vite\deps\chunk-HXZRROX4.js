import {
  eq_default
} from "./chunk-532EQRVQ.js";
import {
  isIndex_default
} from "./chunk-S5XSWUFE.js";
import {
  isArrayLike_default
} from "./chunk-JFUT5HMH.js";
import {
  isObject_default
} from "./chunk-X3F52GTU.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_isIterateeCall.js
function isIterateeCall(value, index, object) {
  if (!isObject_default(object)) {
    return false;
  }
  var type = typeof index;
  if (type == "number" ? isArrayLike_default(object) && isIndex_default(index, object.length) : type == "string" && index in object) {
    return eq_default(object[index], value);
  }
  return false;
}
var isIterateeCall_default = isIterateeCall;

export {
  isIterateeCall_default
};
//# sourceMappingURL=chunk-HXZRROX4.js.map
