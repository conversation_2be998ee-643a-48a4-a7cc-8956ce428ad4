{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_isIterateeCall.js"], "sourcesContent": ["import eq from './eq.js';\nimport isArrayLike from './isArrayLike.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\n\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */\nfunction isIterateeCall(value, index, object) {\n  if (!isObject(object)) {\n    return false;\n  }\n  var type = typeof index;\n  if (type == 'number'\n        ? (isArrayLike(object) && isIndex(index, object.length))\n        : (type == 'string' && index in object)\n      ) {\n    return eq(object[index], value);\n  }\n  return false;\n}\n\nexport default isIterateeCall;\n"], "mappings": ";;;;;;;;;;;;;;AAeA,SAAS,eAAe,OAAO,OAAO,QAAQ;AAC5C,MAAI,CAAC,iBAAS,MAAM,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,OAAO;AAClB,MAAI,QAAQ,WACH,oBAAY,MAAM,KAAK,gBAAQ,OAAO,OAAO,MAAM,IACnD,QAAQ,YAAY,SAAS,QAChC;AACJ,WAAO,WAAG,OAAO,KAAK,GAAG,KAAK;AAAA,EAChC;AACA,SAAO;AACT;AAEA,IAAO,yBAAQ;", "names": []}