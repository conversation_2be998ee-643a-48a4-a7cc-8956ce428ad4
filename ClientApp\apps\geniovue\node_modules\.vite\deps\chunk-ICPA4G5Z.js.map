{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_isFlattenable.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseFlatten.js"], "sourcesContent": ["import Symbol from './_Symbol.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\n\n/** Built-in value references. */\nvar spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) ||\n    !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\n\nexport default isFlattenable;\n", "import arrayPush from './_arrayPush.js';\nimport isFlattenable from './_isFlattenable.js';\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n      length = array.length;\n\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\n\nexport default baseFlatten;\n"], "mappings": ";;;;;;;;;;;;;;AAKA,IAAI,mBAAmB,iBAAS,eAAO,qBAAqB;AAS5D,SAAS,cAAc,OAAO;AAC5B,SAAO,gBAAQ,KAAK,KAAK,oBAAY,KAAK,KACxC,CAAC,EAAE,oBAAoB,SAAS,MAAM,gBAAgB;AAC1D;AAEA,IAAO,wBAAQ;;;ACLf,SAAS,YAAY,OAAO,OAAO,WAAW,UAAU,QAAQ;AAC9D,MAAI,QAAQ,IACR,SAAS,MAAM;AAEnB,gBAAc,YAAY;AAC1B,aAAW,SAAS,CAAC;AAErB,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,QAAQ,KAAK,UAAU,KAAK,GAAG;AACjC,UAAI,QAAQ,GAAG;AAEb,oBAAY,OAAO,QAAQ,GAAG,WAAW,UAAU,MAAM;AAAA,MAC3D,OAAO;AACL,0BAAU,QAAQ,KAAK;AAAA,MACzB;AAAA,IACF,WAAW,CAAC,UAAU;AACpB,aAAO,OAAO,MAAM,IAAI;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAO,sBAAQ;", "names": []}