import {
  isLength_default
} from "./chunk-KKDVC4X3.js";
import {
  isFunction_default
} from "./chunk-ZNZP756G.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isArrayLike.js
function isArrayLike(value) {
  return value != null && isLength_default(value.length) && !isFunction_default(value);
}
var isArrayLike_default = isArrayLike;

export {
  isArrayLike_default
};
//# sourceMappingURL=chunk-JFUT5HMH.js.map
