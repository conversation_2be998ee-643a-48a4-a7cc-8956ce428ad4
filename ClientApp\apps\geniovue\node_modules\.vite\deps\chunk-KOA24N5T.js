import {
  baseAssignValue_default
} from "./chunk-3KS2BYTQ.js";
import {
  eq_default
} from "./chunk-532EQRVQ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_assignValue.js
var objectProto = Object.prototype;
var hasOwnProperty = objectProto.hasOwnProperty;
function assignValue(object, key, value) {
  var objValue = object[key];
  if (!(hasOwnProperty.call(object, key) && eq_default(objValue, value)) || value === void 0 && !(key in object)) {
    baseAssignValue_default(object, key, value);
  }
}
var assignValue_default = assignValue;

export {
  assignValue_default
};
//# sourceMappingURL=chunk-KOA24N5T.js.map
