{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_arrayAggregator.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseAggregator.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_createAggregator.js"], "sourcesContent": ["/**\n * A specialized version of `baseAggregator` for arrays.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction arrayAggregator(array, setter, iteratee, accumulator) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    var value = array[index];\n    setter(accumulator, value, iteratee(value), array);\n  }\n  return accumulator;\n}\n\nexport default arrayAggregator;\n", "import baseEach from './_baseEach.js';\n\n/**\n * Aggregates elements of `collection` on `accumulator` with keys transformed\n * by `iteratee` and values set by `setter`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseAggregator(collection, setter, iteratee, accumulator) {\n  baseEach(collection, function(value, key, collection) {\n    setter(accumulator, value, iteratee(value), collection);\n  });\n  return accumulator;\n}\n\nexport default baseAggregator;\n", "import arrayAggregator from './_arrayAggregator.js';\nimport baseAggregator from './_baseAggregator.js';\nimport baseIteratee from './_baseIteratee.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates a function like `_.groupBy`.\n *\n * @private\n * @param {Function} setter The function to set accumulator values.\n * @param {Function} [initializer] The accumulator object initializer.\n * @returns {Function} Returns the new aggregator function.\n */\nfunction createAggregator(setter, initializer) {\n  return function(collection, iteratee) {\n    var func = isArray(collection) ? arrayAggregator : baseAggregator,\n        accumulator = initializer ? initializer() : {};\n\n    return func(collection, setter, baseIteratee(iteratee, 2), accumulator);\n  };\n}\n\nexport default createAggregator;\n"], "mappings": ";;;;;;;;;;;AAUA,SAAS,gBAAgB,OAAO,QAAQ,UAAU,aAAa;AAC7D,MAAI,QAAQ,IACR,SAAS,SAAS,OAAO,IAAI,MAAM;AAEvC,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,QAAQ,MAAM,KAAK;AACvB,WAAO,aAAa,OAAO,SAAS,KAAK,GAAG,KAAK;AAAA,EACnD;AACA,SAAO;AACT;AAEA,IAAO,0BAAQ;;;ACRf,SAAS,eAAe,YAAY,QAAQ,UAAU,aAAa;AACjE,mBAAS,YAAY,SAAS,OAAO,KAAKA,aAAY;AACpD,WAAO,aAAa,OAAO,SAAS,KAAK,GAAGA,WAAU;AAAA,EACxD,CAAC;AACD,SAAO;AACT;AAEA,IAAO,yBAAQ;;;ACPf,SAAS,iBAAiB,QAAQ,aAAa;AAC7C,SAAO,SAAS,YAAY,UAAU;AACpC,QAAI,OAAO,gBAAQ,UAAU,IAAI,0BAAkB,wBAC/C,cAAc,cAAc,YAAY,IAAI,CAAC;AAEjD,WAAO,KAAK,YAAY,QAAQ,qBAAa,UAAU,CAAC,GAAG,WAAW;AAAA,EACxE;AACF;AAEA,IAAO,2BAAQ;", "names": ["collection"]}