{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_copyObject.js"], "sourcesContent": ["import assignValue from './_assignValue.js';\nimport baseAssignValue from './_baseAssignValue.js';\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n  return object;\n}\n\nexport default copyObject;\n"], "mappings": ";;;;;;;;AAaA,SAAS,WAAW,QAAQ,OAAO,QAAQ,YAAY;AACrD,MAAI,QAAQ,CAAC;AACb,aAAW,SAAS,CAAC;AAErB,MAAI,QAAQ,IACR,SAAS,MAAM;AAEnB,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,MAAM,MAAM,KAAK;AAErB,QAAI,WAAW,aACX,WAAW,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,KAAK,QAAQ,MAAM,IACxD;AAEJ,QAAI,aAAa,QAAW;AAC1B,iBAAW,OAAO,GAAG;AAAA,IACvB;AACA,QAAI,OAAO;AACT,8BAAgB,QAAQ,KAAK,QAAQ;AAAA,IACvC,OAAO;AACL,0BAAY,QAAQ,KAAK,QAAQ;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAO,qBAAQ;", "names": []}