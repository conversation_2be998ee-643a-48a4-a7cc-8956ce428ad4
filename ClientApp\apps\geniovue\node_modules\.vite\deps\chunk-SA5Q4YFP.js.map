{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/identity.js"], "sourcesContent": ["/**\n * This method returns the first argument it receives.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {*} value Any value.\n * @returns {*} Returns `value`.\n * @example\n *\n * var object = { 'a': 1 };\n *\n * console.log(_.identity(object) === object);\n * // => true\n */\nfunction identity(value) {\n  return value;\n}\n\nexport default identity;\n"], "mappings": ";AAgBA,SAAS,SAAS,OAAO;AACvB,SAAO;AACT;AAEA,IAAO,mBAAQ;", "names": []}