import {
  baseFindIndex_default
} from "./chunk-GBNDIJHH.js";
import {
  toInteger_default
} from "./chunk-TO6KK5ZK.js";
import {
  baseIteratee_default
} from "./chunk-FWO24P7C.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/findIndex.js
var nativeMax = Math.max;
function findIndex(array, predicate, fromIndex) {
  var length = array == null ? 0 : array.length;
  if (!length) {
    return -1;
  }
  var index = fromIndex == null ? 0 : toInteger_default(fromIndex);
  if (index < 0) {
    index = nativeMax(length + index, 0);
  }
  return baseFindIndex_default(array, baseIteratee_default(predicate, 3), index);
}
var findIndex_default = findIndex;

export {
  findIndex_default
};
//# sourceMappingURL=chunk-T4Q566IY.js.map
