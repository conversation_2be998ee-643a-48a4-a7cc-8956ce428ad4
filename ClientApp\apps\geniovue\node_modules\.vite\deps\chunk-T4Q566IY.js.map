{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/findIndex.js"], "sourcesContent": ["import baseFindIndex from './_baseFindIndex.js';\nimport baseIteratee from './_baseIteratee.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This method is like `_.find` except that it returns the index of the first\n * element `predicate` returns truthy for instead of the element itself.\n *\n * @static\n * @memberOf _\n * @since 1.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=0] The index to search from.\n * @returns {number} Returns the index of the found element, else `-1`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': false },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': true }\n * ];\n *\n * _.findIndex(users, function(o) { return o.user == 'barney'; });\n * // => 0\n *\n * // The `_.matches` iteratee shorthand.\n * _.findIndex(users, { 'user': 'fred', 'active': false });\n * // => 1\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findIndex(users, ['active', false]);\n * // => 0\n *\n * // The `_.property` iteratee shorthand.\n * _.findIndex(users, 'active');\n * // => 2\n */\nfunction findIndex(array, predicate, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = fromIndex == null ? 0 : toInteger(fromIndex);\n  if (index < 0) {\n    index = nativeMax(length + index, 0);\n  }\n  return baseFindIndex(array, baseIteratee(predicate, 3), index);\n}\n\nexport default findIndex;\n"], "mappings": ";;;;;;;;;;;AAKA,IAAI,YAAY,KAAK;AAqCrB,SAAS,UAAU,OAAO,WAAW,WAAW;AAC9C,MAAI,SAAS,SAAS,OAAO,IAAI,MAAM;AACvC,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,aAAa,OAAO,IAAI,kBAAU,SAAS;AACvD,MAAI,QAAQ,GAAG;AACb,YAAQ,UAAU,SAAS,OAAO,CAAC;AAAA,EACrC;AACA,SAAO,sBAAc,OAAO,qBAAa,WAAW,CAAC,GAAG,KAAK;AAC/D;AAEA,IAAO,oBAAQ;", "names": []}