import {
  toFinite_default
} from "./chunk-XJ7DCSNU.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/toInteger.js
function toInteger(value) {
  var result = toFinite_default(value), remainder = result % 1;
  return result === result ? remainder ? result - remainder : result : 0;
}
var toInteger_default = toInteger;

export {
  toInteger_default
};
//# sourceMappingURL=chunk-TO6KK5ZK.js.map
