{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_assignMergeValue.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isPlainObject.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_safeGet.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/toPlainObject.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseMergeDeep.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseMerge.js"], "sourcesContent": ["import baseAssignValue from './_baseAssignValue.js';\nimport eq from './eq.js';\n\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignMergeValue(object, key, value) {\n  if ((value !== undefined && !eq(object[key], value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nexport default assignMergeValue;\n", "import baseGetTag from './_baseGetTag.js';\nimport getPrototype from './_getPrototype.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar objectTag = '[object Object]';\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to infer the `Object` constructor. */\nvar objectCtorString = funcToString.call(Object);\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nfunction isPlainObject(value) {\n  if (!isObjectLike(value) || baseGetTag(value) != objectTag) {\n    return false;\n  }\n  var proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return typeof Ctor == 'function' && Ctor instanceof Ctor &&\n    funcToString.call(Ctor) == objectCtorString;\n}\n\nexport default isPlainObject;\n", "/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction safeGet(object, key) {\n  if (key === 'constructor' && typeof object[key] === 'function') {\n    return;\n  }\n\n  if (key == '__proto__') {\n    return;\n  }\n\n  return object[key];\n}\n\nexport default safeGet;\n", "import copyObject from './_copyObject.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Converts `value` to a plain object flattening inherited enumerable string\n * keyed properties of `value` to own properties of the plain object.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {Object} Returns the converted plain object.\n * @example\n *\n * function Foo() {\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.assign({ 'a': 1 }, new Foo);\n * // => { 'a': 1, 'b': 2 }\n *\n * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));\n * // => { 'a': 1, 'b': 2, 'c': 3 }\n */\nfunction toPlainObject(value) {\n  return copyObject(value, keysIn(value));\n}\n\nexport default toPlainObject;\n", "import assignMergeValue from './_assignMergeValue.js';\nimport cloneBuffer from './_cloneBuffer.js';\nimport cloneTypedArray from './_cloneTypedArray.js';\nimport copyArray from './_copyArray.js';\nimport initCloneObject from './_initCloneObject.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport isBuffer from './isBuffer.js';\nimport isFunction from './isFunction.js';\nimport isObject from './isObject.js';\nimport isPlainObject from './isPlainObject.js';\nimport isTypedArray from './isTypedArray.js';\nimport safeGet from './_safeGet.js';\nimport toPlainObject from './toPlainObject.js';\n\n/**\n * A specialized version of `baseMerge` for arrays and objects which performs\n * deep merges and tracks traversed objects enabling objects with circular\n * references to be merged.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {string} key The key of the value to merge.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} mergeFunc The function to merge values.\n * @param {Function} [customizer] The function to customize assigned values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {\n  var objValue = safeGet(object, key),\n      srcValue = safeGet(source, key),\n      stacked = stack.get(srcValue);\n\n  if (stacked) {\n    assignMergeValue(object, key, stacked);\n    return;\n  }\n  var newValue = customizer\n    ? customizer(objValue, srcValue, (key + ''), object, source, stack)\n    : undefined;\n\n  var isCommon = newValue === undefined;\n\n  if (isCommon) {\n    var isArr = isArray(srcValue),\n        isBuff = !isArr && isBuffer(srcValue),\n        isTyped = !isArr && !isBuff && isTypedArray(srcValue);\n\n    newValue = srcValue;\n    if (isArr || isBuff || isTyped) {\n      if (isArray(objValue)) {\n        newValue = objValue;\n      }\n      else if (isArrayLikeObject(objValue)) {\n        newValue = copyArray(objValue);\n      }\n      else if (isBuff) {\n        isCommon = false;\n        newValue = cloneBuffer(srcValue, true);\n      }\n      else if (isTyped) {\n        isCommon = false;\n        newValue = cloneTypedArray(srcValue, true);\n      }\n      else {\n        newValue = [];\n      }\n    }\n    else if (isPlainObject(srcValue) || isArguments(srcValue)) {\n      newValue = objValue;\n      if (isArguments(objValue)) {\n        newValue = toPlainObject(objValue);\n      }\n      else if (!isObject(objValue) || isFunction(objValue)) {\n        newValue = initCloneObject(srcValue);\n      }\n    }\n    else {\n      isCommon = false;\n    }\n  }\n  if (isCommon) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, newValue);\n    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);\n    stack['delete'](srcValue);\n  }\n  assignMergeValue(object, key, newValue);\n}\n\nexport default baseMergeDeep;\n", "import Stack from './_Stack.js';\nimport assignMergeValue from './_assignMergeValue.js';\nimport baseFor from './_baseFor.js';\nimport baseMergeDeep from './_baseMergeDeep.js';\nimport isObject from './isObject.js';\nimport keysIn from './keysIn.js';\nimport safeGet from './_safeGet.js';\n\n/**\n * The base implementation of `_.merge` without support for multiple sources.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} [customizer] The function to customize merged values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMerge(object, source, srcIndex, customizer, stack) {\n  if (object === source) {\n    return;\n  }\n  baseFor(source, function(srcValue, key) {\n    stack || (stack = new Stack);\n    if (isObject(srcValue)) {\n      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);\n    }\n    else {\n      var newValue = customizer\n        ? customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack)\n        : undefined;\n\n      if (newValue === undefined) {\n        newValue = srcValue;\n      }\n      assignMergeValue(object, key, newValue);\n    }\n  }, keysIn);\n}\n\nexport default baseMerge;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,SAAS,iBAAiB,QAAQ,KAAK,OAAO;AAC5C,MAAK,UAAU,UAAa,CAAC,WAAG,OAAO,GAAG,GAAG,KAAK,KAC7C,UAAU,UAAa,EAAE,OAAO,SAAU;AAC7C,4BAAgB,QAAQ,KAAK,KAAK;AAAA,EACpC;AACF;AAEA,IAAO,2BAAQ;;;ACdf,IAAI,YAAY;AAGhB,IAAI,YAAY,SAAS;AAAzB,IACI,cAAc,OAAO;AAGzB,IAAI,eAAe,UAAU;AAG7B,IAAI,iBAAiB,YAAY;AAGjC,IAAI,mBAAmB,aAAa,KAAK,MAAM;AA8B/C,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,qBAAa,KAAK,KAAK,mBAAW,KAAK,KAAK,WAAW;AAC1D,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,qBAAa,KAAK;AAC9B,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,eAAe,KAAK,OAAO,aAAa,KAAK,MAAM;AAC9D,SAAO,OAAO,QAAQ,cAAc,gBAAgB,QAClD,aAAa,KAAK,IAAI,KAAK;AAC/B;AAEA,IAAO,wBAAQ;;;ACrDf,SAAS,QAAQ,QAAQ,KAAK;AAC5B,MAAI,QAAQ,iBAAiB,OAAO,OAAO,GAAG,MAAM,YAAY;AAC9D;AAAA,EACF;AAEA,MAAI,OAAO,aAAa;AACtB;AAAA,EACF;AAEA,SAAO,OAAO,GAAG;AACnB;AAEA,IAAO,kBAAQ;;;ACOf,SAAS,cAAc,OAAO;AAC5B,SAAO,mBAAW,OAAO,eAAO,KAAK,CAAC;AACxC;AAEA,IAAO,wBAAQ;;;ACAf,SAAS,cAAc,QAAQ,QAAQ,KAAK,UAAU,WAAW,YAAY,OAAO;AAClF,MAAI,WAAW,gBAAQ,QAAQ,GAAG,GAC9B,WAAW,gBAAQ,QAAQ,GAAG,GAC9B,UAAU,MAAM,IAAI,QAAQ;AAEhC,MAAI,SAAS;AACX,6BAAiB,QAAQ,KAAK,OAAO;AACrC;AAAA,EACF;AACA,MAAI,WAAW,aACX,WAAW,UAAU,UAAW,MAAM,IAAK,QAAQ,QAAQ,KAAK,IAChE;AAEJ,MAAI,WAAW,aAAa;AAE5B,MAAI,UAAU;AACZ,QAAI,QAAQ,gBAAQ,QAAQ,GACxB,SAAS,CAAC,SAAS,iBAAS,QAAQ,GACpC,UAAU,CAAC,SAAS,CAAC,UAAU,qBAAa,QAAQ;AAExD,eAAW;AACX,QAAI,SAAS,UAAU,SAAS;AAC9B,UAAI,gBAAQ,QAAQ,GAAG;AACrB,mBAAW;AAAA,MACb,WACS,0BAAkB,QAAQ,GAAG;AACpC,mBAAW,kBAAU,QAAQ;AAAA,MAC/B,WACS,QAAQ;AACf,mBAAW;AACX,mBAAW,oBAAY,UAAU,IAAI;AAAA,MACvC,WACS,SAAS;AAChB,mBAAW;AACX,mBAAW,wBAAgB,UAAU,IAAI;AAAA,MAC3C,OACK;AACH,mBAAW,CAAC;AAAA,MACd;AAAA,IACF,WACS,sBAAc,QAAQ,KAAK,oBAAY,QAAQ,GAAG;AACzD,iBAAW;AACX,UAAI,oBAAY,QAAQ,GAAG;AACzB,mBAAW,sBAAc,QAAQ;AAAA,MACnC,WACS,CAAC,iBAAS,QAAQ,KAAK,mBAAW,QAAQ,GAAG;AACpD,mBAAW,wBAAgB,QAAQ;AAAA,MACrC;AAAA,IACF,OACK;AACH,iBAAW;AAAA,IACb;AAAA,EACF;AACA,MAAI,UAAU;AAEZ,UAAM,IAAI,UAAU,QAAQ;AAC5B,cAAU,UAAU,UAAU,UAAU,YAAY,KAAK;AACzD,UAAM,QAAQ,EAAE,QAAQ;AAAA,EAC1B;AACA,2BAAiB,QAAQ,KAAK,QAAQ;AACxC;AAEA,IAAO,wBAAQ;;;AC1Ef,SAAS,UAAU,QAAQ,QAAQ,UAAU,YAAY,OAAO;AAC9D,MAAI,WAAW,QAAQ;AACrB;AAAA,EACF;AACA,kBAAQ,QAAQ,SAAS,UAAU,KAAK;AACtC,cAAU,QAAQ,IAAI;AACtB,QAAI,iBAAS,QAAQ,GAAG;AACtB,4BAAc,QAAQ,QAAQ,KAAK,UAAU,WAAW,YAAY,KAAK;AAAA,IAC3E,OACK;AACH,UAAI,WAAW,aACX,WAAW,gBAAQ,QAAQ,GAAG,GAAG,UAAW,MAAM,IAAK,QAAQ,QAAQ,KAAK,IAC5E;AAEJ,UAAI,aAAa,QAAW;AAC1B,mBAAW;AAAA,MACb;AACA,+BAAiB,QAAQ,KAAK,QAAQ;AAAA,IACxC;AAAA,EACF,GAAG,cAAM;AACX;AAEA,IAAO,oBAAQ;", "names": []}