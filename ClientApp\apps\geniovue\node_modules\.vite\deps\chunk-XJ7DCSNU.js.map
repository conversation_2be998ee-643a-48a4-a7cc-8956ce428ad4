{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/toFinite.js"], "sourcesContent": ["import toNumber from './toNumber.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_INTEGER = 1.7976931348623157e+308;\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\nexport default toFinite;\n"], "mappings": ";;;;;AAGA,IAAI,WAAW,IAAI;AAAnB,IACI,cAAc;AAyBlB,SAAS,SAAS,OAAO;AACvB,MAAI,CAAC,OAAO;AACV,WAAO,UAAU,IAAI,QAAQ;AAAA,EAC/B;AACA,UAAQ,iBAAS,KAAK;AACtB,MAAI,UAAU,YAAY,UAAU,CAAC,UAAU;AAC7C,QAAI,OAAQ,QAAQ,IAAI,KAAK;AAC7B,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,UAAU,QAAQ,QAAQ;AACnC;AAEA,IAAO,mBAAQ;", "names": []}