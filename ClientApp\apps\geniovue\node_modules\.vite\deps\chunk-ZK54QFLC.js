import {
  isObjectLike_default
} from "./chunk-VB7E2QJD.js";
import {
  baseGetTag_default
} from "./chunk-ZJQW7BA7.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isSymbol.js
var symbolTag = "[object Symbol]";
function isSymbol(value) {
  return typeof value == "symbol" || isObjectLike_default(value) && baseGetTag_default(value) == symbolTag;
}
var isSymbol_default = isSymbol;

export {
  isSymbol_default
};
//# sourceMappingURL=chunk-ZK54QFLC.js.map
