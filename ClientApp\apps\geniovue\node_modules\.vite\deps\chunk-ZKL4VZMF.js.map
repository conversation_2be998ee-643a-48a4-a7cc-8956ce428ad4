{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_createBaseFor.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseFor.js"], "sourcesContent": ["/**\n * Creates a base function for methods like `_.forIn` and `_.forOwn`.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseFor(fromRight) {\n  return function(object, iteratee, keysFunc) {\n    var index = -1,\n        iterable = Object(object),\n        props = keysFunc(object),\n        length = props.length;\n\n    while (length--) {\n      var key = props[fromRight ? length : ++index];\n      if (iteratee(iterable[key], key, iterable) === false) {\n        break;\n      }\n    }\n    return object;\n  };\n}\n\nexport default createBaseFor;\n", "import createBaseFor from './_createBaseFor.js';\n\n/**\n * The base implementation of `baseForOwn` which iterates over `object`\n * properties returned by `keysFunc` and invokes `iteratee` for each property.\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\nvar baseFor = createBaseFor();\n\nexport default baseFor;\n"], "mappings": ";AAOA,SAAS,cAAc,WAAW;AAChC,SAAO,SAAS,QAAQ,UAAU,UAAU;AAC1C,QAAI,QAAQ,IACR,WAAW,OAAO,MAAM,GACxB,QAAQ,SAAS,MAAM,GACvB,SAAS,MAAM;AAEnB,WAAO,UAAU;AACf,UAAI,MAAM,MAAM,YAAY,SAAS,EAAE,KAAK;AAC5C,UAAI,SAAS,SAAS,GAAG,GAAG,KAAK,QAAQ,MAAM,OAAO;AACpD;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,wBAAQ;;;ACXf,IAAI,UAAU,sBAAc;AAE5B,IAAO,kBAAQ;", "names": []}