import {
  isObject_default
} from "./chunk-X3F52GTU.js";
import {
  baseGetTag_default
} from "./chunk-ZJQW7BA7.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isFunction.js
var asyncTag = "[object AsyncFunction]";
var funcTag = "[object Function]";
var genTag = "[object GeneratorFunction]";
var proxyTag = "[object Proxy]";
function isFunction(value) {
  if (!isObject_default(value)) {
    return false;
  }
  var tag = baseGetTag_default(value);
  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;
}
var isFunction_default = isFunction;

export {
  isFunction_default
};
//# sourceMappingURL=chunk-ZNZP756G.js.map
