{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isFunction.js"], "sourcesContent": ["import baseGetTag from './_baseGetTag.js';\nimport isObject from './isObject.js';\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\nexport default isFunction;\n"], "mappings": ";;;;;;;;AAIA,IAAI,WAAW;AAAf,IACI,UAAU;AADd,IAEI,SAAS;AAFb,IAGI,WAAW;AAmBf,SAAS,WAAW,OAAO;AACzB,MAAI,CAAC,iBAAS,KAAK,GAAG;AACpB,WAAO;AAAA,EACT;AAGA,MAAI,MAAM,mBAAW,KAAK;AAC1B,SAAO,OAAO,WAAW,OAAO,UAAU,OAAO,YAAY,OAAO;AACtE;AAEA,IAAO,qBAAQ;", "names": []}