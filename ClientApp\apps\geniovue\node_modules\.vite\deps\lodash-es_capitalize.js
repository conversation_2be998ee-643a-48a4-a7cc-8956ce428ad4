import {
  upperFirst_default
} from "./chunk-QIGDNUJV.js";
import "./chunk-BWB4RQ4S.js";
import {
  toString_default
} from "./chunk-OL6NUAZ6.js";
import "./chunk-M6TBIOXS.js";
import "./chunk-ZK54QFLC.js";
import "./chunk-VO4BPRKV.js";
import "./chunk-VB7E2QJD.js";
import "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/capitalize.js
function capitalize(string) {
  return upperFirst_default(toString_default(string).toLowerCase());
}
var capitalize_default = capitalize;
export {
  capitalize_default as default
};
//# sourceMappingURL=lodash-es_capitalize.js.map
