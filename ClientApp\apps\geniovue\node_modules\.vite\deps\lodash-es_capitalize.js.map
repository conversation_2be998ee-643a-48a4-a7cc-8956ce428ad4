{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/capitalize.js"], "sourcesContent": ["import toString from './toString.js';\nimport upperFirst from './upperFirst.js';\n\n/**\n * Converts the first character of `string` to upper case and the remaining\n * to lower case.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to capitalize.\n * @returns {string} Returns the capitalized string.\n * @example\n *\n * _.capitalize('FRED');\n * // => 'Fred'\n */\nfunction capitalize(string) {\n  return upperFirst(toString(string).toLowerCase());\n}\n\nexport default capitalize;\n"], "mappings": ";;;;;;;;;;;;;;;AAkBA,SAAS,WAAW,QAAQ;AAC1B,SAAO,mBAAW,iBAAS,MAAM,EAAE,YAAY,CAAC;AAClD;AAEA,IAAO,qBAAQ;", "names": []}