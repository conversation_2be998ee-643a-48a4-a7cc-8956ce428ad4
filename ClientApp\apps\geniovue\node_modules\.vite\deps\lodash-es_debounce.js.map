{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/now.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/debounce.js"], "sourcesContent": ["import root from './_root.js';\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nexport default now;\n", "import isObject from './isObject.js';\nimport now from './now.js';\nimport toNumber from './toNumber.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nexport default debounce;\n"], "mappings": ";;;;;;;;;;;;;;AAkBA,IAAI,MAAM,WAAW;AACnB,SAAO,aAAK,KAAK,IAAI;AACvB;AAEA,IAAO,cAAQ;;;ACjBf,IAAI,kBAAkB;AAGtB,IAAI,YAAY,KAAK;AAArB,IACI,YAAY,KAAK;AAwDrB,SAAS,SAAS,MAAM,MAAM,SAAS;AACrC,MAAI,UACA,UACA,SACA,QACA,SACA,cACA,iBAAiB,GACjB,UAAU,OACV,SAAS,OACT,WAAW;AAEf,MAAI,OAAO,QAAQ,YAAY;AAC7B,UAAM,IAAI,UAAU,eAAe;AAAA,EACrC;AACA,SAAO,iBAAS,IAAI,KAAK;AACzB,MAAI,iBAAS,OAAO,GAAG;AACrB,cAAU,CAAC,CAAC,QAAQ;AACpB,aAAS,aAAa;AACtB,cAAU,SAAS,UAAU,iBAAS,QAAQ,OAAO,KAAK,GAAG,IAAI,IAAI;AACrE,eAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,EAC1D;AAEA,WAAS,WAAW,MAAM;AACxB,QAAI,OAAO,UACP,UAAU;AAEd,eAAW,WAAW;AACtB,qBAAiB;AACjB,aAAS,KAAK,MAAM,SAAS,IAAI;AACjC,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,MAAM;AAEzB,qBAAiB;AAEjB,cAAU,WAAW,cAAc,IAAI;AAEvC,WAAO,UAAU,WAAW,IAAI,IAAI;AAAA,EACtC;AAEA,WAAS,cAAc,MAAM;AAC3B,QAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO,gBAC7B,cAAc,OAAO;AAEzB,WAAO,SACH,UAAU,aAAa,UAAU,mBAAmB,IACpD;AAAA,EACN;AAEA,WAAS,aAAa,MAAM;AAC1B,QAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO;AAKjC,WAAQ,iBAAiB,UAAc,qBAAqB,QACzD,oBAAoB,KAAO,UAAU,uBAAuB;AAAA,EACjE;AAEA,WAAS,eAAe;AACtB,QAAI,OAAO,YAAI;AACf,QAAI,aAAa,IAAI,GAAG;AACtB,aAAO,aAAa,IAAI;AAAA,IAC1B;AAEA,cAAU,WAAW,cAAc,cAAc,IAAI,CAAC;AAAA,EACxD;AAEA,WAAS,aAAa,MAAM;AAC1B,cAAU;AAIV,QAAI,YAAY,UAAU;AACxB,aAAO,WAAW,IAAI;AAAA,IACxB;AACA,eAAW,WAAW;AACtB,WAAO;AAAA,EACT;AAEA,WAAS,SAAS;AAChB,QAAI,YAAY,QAAW;AACzB,mBAAa,OAAO;AAAA,IACtB;AACA,qBAAiB;AACjB,eAAW,eAAe,WAAW,UAAU;AAAA,EACjD;AAEA,WAAS,QAAQ;AACf,WAAO,YAAY,SAAY,SAAS,aAAa,YAAI,CAAC;AAAA,EAC5D;AAEA,WAAS,YAAY;AACnB,QAAI,OAAO,YAAI,GACX,aAAa,aAAa,IAAI;AAElC,eAAW;AACX,eAAW;AACX,mBAAe;AAEf,QAAI,YAAY;AACd,UAAI,YAAY,QAAW;AACzB,eAAO,YAAY,YAAY;AAAA,MACjC;AACA,UAAI,QAAQ;AAEV,qBAAa,OAAO;AACpB,kBAAU,WAAW,cAAc,IAAI;AACvC,eAAO,WAAW,YAAY;AAAA,MAChC;AAAA,IACF;AACA,QAAI,YAAY,QAAW;AACzB,gBAAU,WAAW,cAAc,IAAI;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AACA,YAAU,SAAS;AACnB,YAAU,QAAQ;AAClB,SAAO;AACT;AAEA,IAAO,mBAAQ;", "names": []}