{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseFilter.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/filter.js"], "sourcesContent": ["import baseEach from './_baseEach.js';\n\n/**\n * The base implementation of `_.filter` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction baseFilter(collection, predicate) {\n  var result = [];\n  baseEach(collection, function(value, index, collection) {\n    if (predicate(value, index, collection)) {\n      result.push(value);\n    }\n  });\n  return result;\n}\n\nexport default baseFilter;\n", "import arrayFilter from './_arrayFilter.js';\nimport baseFilter from './_baseFilter.js';\nimport baseIteratee from './_baseIteratee.js';\nimport isArray from './isArray.js';\n\n/**\n * Iterates over elements of `collection`, returning an array of all elements\n * `predicate` returns truthy for. The predicate is invoked with three\n * arguments: (value, index|key, collection).\n *\n * **Note:** Unlike `_.remove`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n * @see _.reject\n * @example\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36, 'active': true },\n *   { 'user': 'fred',   'age': 40, 'active': false }\n * ];\n *\n * _.filter(users, function(o) { return !o.active; });\n * // => objects for ['fred']\n *\n * // The `_.matches` iteratee shorthand.\n * _.filter(users, { 'age': 36, 'active': true });\n * // => objects for ['barney']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.filter(users, ['active', false]);\n * // => objects for ['fred']\n *\n * // The `_.property` iteratee shorthand.\n * _.filter(users, 'active');\n * // => objects for ['barney']\n *\n * // Combining several predicates using `_.overEvery` or `_.overSome`.\n * _.filter(users, _.overSome([{ 'age': 36 }, ['age', 40]]));\n * // => objects for ['fred', 'barney']\n */\nfunction filter(collection, predicate) {\n  var func = isArray(collection) ? arrayFilter : baseFilter;\n  return func(collection, baseIteratee(predicate, 3));\n}\n\nexport default filter;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAS,WAAW,YAAY,WAAW;AACzC,MAAI,SAAS,CAAC;AACd,mBAAS,YAAY,SAAS,OAAO,OAAOA,aAAY;AACtD,QAAI,UAAU,OAAO,OAAOA,WAAU,GAAG;AACvC,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAO,qBAAQ;;;AC0Bf,SAAS,OAAO,YAAY,WAAW;AACrC,MAAI,OAAO,gBAAQ,UAAU,IAAI,sBAAc;AAC/C,SAAO,KAAK,YAAY,qBAAa,WAAW,CAAC,CAAC;AACpD;AAEA,IAAO,iBAAQ;", "names": ["collection"]}