{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/flatMap.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\nimport map from './map.js';\n\n/**\n * Creates a flattened array of values by running each element in `collection`\n * thru `iteratee` and flattening the mapped results. The iteratee is invoked\n * with three arguments: (value, index|key, collection).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * function duplicate(n) {\n *   return [n, n];\n * }\n *\n * _.flatMap([1, 2], duplicate);\n * // => [1, 1, 2, 2]\n */\nfunction flatMap(collection, iteratee) {\n  return baseFlatten(map(collection, iteratee), 1);\n}\n\nexport default flatMap;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,SAAS,QAAQ,YAAY,UAAU;AACrC,SAAO,oBAAY,YAAI,YAAY,QAAQ,GAAG,CAAC;AACjD;AAEA,IAAO,kBAAQ;", "names": []}