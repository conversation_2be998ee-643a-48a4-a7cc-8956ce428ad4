import {
  baseEach_default
} from "./chunk-XMKOFDSF.js";
import {
  arrayEach_default
} from "./chunk-3ILRIISS.js";
import "./chunk-ZKL4VZMF.js";
import "./chunk-KS6VPCU7.js";
import {
  identity_default
} from "./chunk-SA5Q4YFP.js";
import "./chunk-7QXBSFWZ.js";
import "./chunk-S5XSWUFE.js";
import "./chunk-6AKQ6PKI.js";
import "./chunk-Q2I7EFQJ.js";
import "./chunk-BPIZ5UIH.js";
import "./chunk-JFUT5HMH.js";
import "./chunk-KBVNP3C6.js";
import "./chunk-KKDVC4X3.js";
import "./chunk-SNQ64GCV.js";
import "./chunk-ZNZP756G.js";
import "./chunk-X3F52GTU.js";
import {
  isArray_default
} from "./chunk-VO4BPRKV.js";
import "./chunk-VB7E2QJD.js";
import "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_castFunction.js
function castFunction(value) {
  return typeof value == "function" ? value : identity_default;
}
var castFunction_default = castFunction;

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/forEach.js
function forEach(collection, iteratee) {
  var func = isArray_default(collection) ? arrayEach_default : baseEach_default;
  return func(collection, castFunction_default(iteratee));
}
var forEach_default = forEach;
export {
  forEach_default as default
};
//# sourceMappingURL=lodash-es_forEach.js.map
