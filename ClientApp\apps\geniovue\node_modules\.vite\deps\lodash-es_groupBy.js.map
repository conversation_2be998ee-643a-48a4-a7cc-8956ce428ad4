{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/groupBy.js"], "sourcesContent": ["import baseAssignValue from './_baseAssignValue.js';\nimport createAggregator from './_createAggregator.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Creates an object composed of keys generated from the results of running\n * each element of `collection` thru `iteratee`. The order of grouped values\n * is determined by the order they occur in `collection`. The corresponding\n * value of each key is an array of elements responsible for generating the\n * key. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee to transform keys.\n * @returns {Object} Returns the composed aggregate object.\n * @example\n *\n * _.groupBy([6.1, 4.2, 6.3], Math.floor);\n * // => { '4': [4.2], '6': [6.1, 6.3] }\n *\n * // The `_.property` iteratee shorthand.\n * _.groupBy(['one', 'two', 'three'], 'length');\n * // => { '3': ['one', 'two'], '5': ['three'] }\n */\nvar groupBy = createAggregator(function(result, value, key) {\n  if (hasOwnProperty.call(result, key)) {\n    result[key].push(value);\n  } else {\n    baseAssignValue(result, key, [value]);\n  }\n});\n\nexport default groupBy;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI,cAAc,OAAO;AAGzB,IAAI,iBAAiB,YAAY;AAyBjC,IAAI,UAAU,yBAAiB,SAAS,QAAQ,OAAO,KAAK;AAC1D,MAAI,eAAe,KAAK,QAAQ,GAAG,GAAG;AACpC,WAAO,GAAG,EAAE,KAAK,KAAK;AAAA,EACxB,OAAO;AACL,4BAAgB,QAAQ,KAAK,CAAC,KAAK,CAAC;AAAA,EACtC;AACF,CAAC;AAED,IAAO,kBAAQ;", "names": []}