import {
  baseUnary_default,
  nodeUtil_default
} from "./chunk-SNQ64GCV.js";
import {
  isObjectLike_default
} from "./chunk-VB7E2QJD.js";
import {
  baseGetTag_default
} from "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseIsDate.js
var dateTag = "[object Date]";
function baseIsDate(value) {
  return isObjectLike_default(value) && baseGetTag_default(value) == dateTag;
}
var baseIsDate_default = baseIsDate;

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isDate.js
var nodeIsDate = nodeUtil_default && nodeUtil_default.isDate;
var isDate = nodeIsDate ? baseUnary_default(nodeIsDate) : baseIsDate_default;
var isDate_default = isDate;
export {
  isDate_default as default
};
//# sourceMappingURL=lodash-es_isDate.js.map
