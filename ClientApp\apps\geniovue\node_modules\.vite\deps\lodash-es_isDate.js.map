{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseIsDate.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isDate.js"], "sourcesContent": ["import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar dateTag = '[object Date]';\n\n/**\n * The base implementation of `_.isDate` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n */\nfunction baseIsDate(value) {\n  return isObjectLike(value) && baseGetTag(value) == dateTag;\n}\n\nexport default baseIsDate;\n", "import baseIsDate from './_baseIsDate.js';\nimport baseUnary from './_baseUnary.js';\nimport nodeUtil from './_nodeUtil.js';\n\n/* Node.js helper references. */\nvar nodeIsDate = nodeUtil && nodeUtil.isDate;\n\n/**\n * Checks if `value` is classified as a `Date` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n * @example\n *\n * _.isDate(new Date);\n * // => true\n *\n * _.isDate('Mon April 23 2012');\n * // => false\n */\nvar isDate = nodeIsDate ? baseUnary(nodeIsDate) : baseIsDate;\n\nexport default isDate;\n"], "mappings": ";;;;;;;;;;;;;AAIA,IAAI,UAAU;AASd,SAAS,WAAW,OAAO;AACzB,SAAO,qBAAa,KAAK,KAAK,mBAAW,KAAK,KAAK;AACrD;AAEA,IAAO,qBAAQ;;;ACZf,IAAI,aAAa,oBAAY,iBAAS;AAmBtC,IAAI,SAAS,aAAa,kBAAU,UAAU,IAAI;AAElD,IAAO,iBAAQ;", "names": []}