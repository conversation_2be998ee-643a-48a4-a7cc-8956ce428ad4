{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isEmpty.js"], "sourcesContent": ["import baseKeys from './_baseKeys.js';\nimport getTag from './_getTag.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isArrayLike from './isArrayLike.js';\nimport isBuffer from './isBuffer.js';\nimport isPrototype from './_isPrototype.js';\nimport isTypedArray from './isTypedArray.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if `value` is an empty object, collection, map, or set.\n *\n * Objects are considered empty if they have no own enumerable string keyed\n * properties.\n *\n * Array-like values such as `arguments` objects, arrays, buffers, strings, or\n * jQuery-like collections are considered empty if they have a `length` of `0`.\n * Similarly, maps and sets are considered empty if they have a `size` of `0`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is empty, else `false`.\n * @example\n *\n * _.isEmpty(null);\n * // => true\n *\n * _.isEmpty(true);\n * // => true\n *\n * _.isEmpty(1);\n * // => true\n *\n * _.isEmpty([1, 2, 3]);\n * // => false\n *\n * _.isEmpty({ 'a': 1 });\n * // => false\n */\nfunction isEmpty(value) {\n  if (value == null) {\n    return true;\n  }\n  if (isArrayLike(value) &&\n      (isArray(value) || typeof value == 'string' || typeof value.splice == 'function' ||\n        isBuffer(value) || isTypedArray(value) || isArguments(value))) {\n    return !value.length;\n  }\n  var tag = getTag(value);\n  if (tag == mapTag || tag == setTag) {\n    return !value.size;\n  }\n  if (isPrototype(value)) {\n    return !baseKeys(value).length;\n  }\n  for (var key in value) {\n    if (hasOwnProperty.call(value, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default isEmpty;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAI,SAAS;AAAb,IACI,SAAS;AAGb,IAAI,cAAc,OAAO;AAGzB,IAAI,iBAAiB,YAAY;AAmCjC,SAAS,QAAQ,OAAO;AACtB,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,MAAI,oBAAY,KAAK,MAChB,gBAAQ,KAAK,KAAK,OAAO,SAAS,YAAY,OAAO,MAAM,UAAU,cACpE,iBAAS,KAAK,KAAK,qBAAa,KAAK,KAAK,oBAAY,KAAK,IAAI;AACnE,WAAO,CAAC,MAAM;AAAA,EAChB;AACA,MAAI,MAAM,eAAO,KAAK;AACtB,MAAI,OAAO,UAAU,OAAO,QAAQ;AAClC,WAAO,CAAC,MAAM;AAAA,EAChB;AACA,MAAI,oBAAY,KAAK,GAAG;AACtB,WAAO,CAAC,iBAAS,KAAK,EAAE;AAAA,EAC1B;AACA,WAAS,OAAO,OAAO;AACrB,QAAI,eAAe,KAAK,OAAO,GAAG,GAAG;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAO,kBAAQ;", "names": []}