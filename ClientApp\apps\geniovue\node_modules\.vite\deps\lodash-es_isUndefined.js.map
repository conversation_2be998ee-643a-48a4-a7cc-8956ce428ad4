{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isUndefined.js"], "sourcesContent": ["/**\n * Checks if `value` is `undefined`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `undefined`, else `false`.\n * @example\n *\n * _.isUndefined(void 0);\n * // => true\n *\n * _.isUndefined(null);\n * // => false\n */\nfunction isUndefined(value) {\n  return value === undefined;\n}\n\nexport default isUndefined;\n"], "mappings": ";;;AAiBA,SAAS,YAAY,OAAO;AAC1B,SAAO,UAAU;AACnB;AAEA,IAAO,sBAAQ;", "names": []}