{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_parent.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseUnset.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_basePullAt.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/remove.js"], "sourcesContent": ["import baseGet from './_baseGet.js';\nimport baseSlice from './_baseSlice.js';\n\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\n\nexport default parent;\n", "import castPath from './_castPath.js';\nimport last from './last.js';\nimport parent from './_parent.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.unset`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The property path to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n */\nfunction baseUnset(object, path) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  return object == null || delete object[toKey(last(path))];\n}\n\nexport default baseUnset;\n", "import baseUnset from './_baseUnset.js';\nimport isIndex from './_isIndex.js';\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * The base implementation of `_.pullAt` without support for individual\n * indexes or capturing the removed elements.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {number[]} indexes The indexes of elements to remove.\n * @returns {Array} Returns `array`.\n */\nfunction basePullAt(array, indexes) {\n  var length = array ? indexes.length : 0,\n      lastIndex = length - 1;\n\n  while (length--) {\n    var index = indexes[length];\n    if (length == lastIndex || index !== previous) {\n      var previous = index;\n      if (isIndex(index)) {\n        splice.call(array, index, 1);\n      } else {\n        baseUnset(array, index);\n      }\n    }\n  }\n  return array;\n}\n\nexport default basePullAt;\n", "import baseIteratee from './_baseIteratee.js';\nimport basePullAt from './_basePullAt.js';\n\n/**\n * Removes all elements from `array` that `predicate` returns truthy for\n * and returns an array of the removed elements. The predicate is invoked\n * with three arguments: (value, index, array).\n *\n * **Note:** Unlike `_.filter`, this method mutates `array`. Use `_.pull`\n * to pull elements from an array by value.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new array of removed elements.\n * @example\n *\n * var array = [1, 2, 3, 4];\n * var evens = _.remove(array, function(n) {\n *   return n % 2 == 0;\n * });\n *\n * console.log(array);\n * // => [1, 3]\n *\n * console.log(evens);\n * // => [2, 4]\n */\nfunction remove(array, predicate) {\n  var result = [];\n  if (!(array && array.length)) {\n    return result;\n  }\n  var index = -1,\n      indexes = [],\n      length = array.length;\n\n  predicate = baseIteratee(predicate, 3);\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result.push(value);\n      indexes.push(index);\n    }\n  }\n  basePullAt(array, indexes);\n  return result;\n}\n\nexport default remove;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,SAAS,OAAO,QAAQ,MAAM;AAC5B,SAAO,KAAK,SAAS,IAAI,SAAS,gBAAQ,QAAQ,kBAAU,MAAM,GAAG,EAAE,CAAC;AAC1E;AAEA,IAAO,iBAAQ;;;ACFf,SAAS,UAAU,QAAQ,MAAM;AAC/B,SAAO,iBAAS,MAAM,MAAM;AAC5B,WAAS,eAAO,QAAQ,IAAI;AAC5B,SAAO,UAAU,QAAQ,OAAO,OAAO,cAAM,aAAK,IAAI,CAAC,CAAC;AAC1D;AAEA,IAAO,oBAAQ;;;ACff,IAAI,aAAa,MAAM;AAGvB,IAAI,SAAS,WAAW;AAWxB,SAAS,WAAW,OAAO,SAAS;AAClC,MAAI,SAAS,QAAQ,QAAQ,SAAS,GAClC,YAAY,SAAS;AAEzB,SAAO,UAAU;AACf,QAAI,QAAQ,QAAQ,MAAM;AAC1B,QAAI,UAAU,aAAa,UAAU,UAAU;AAC7C,UAAI,WAAW;AACf,UAAI,gBAAQ,KAAK,GAAG;AAClB,eAAO,KAAK,OAAO,OAAO,CAAC;AAAA,MAC7B,OAAO;AACL,0BAAU,OAAO,KAAK;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAO,qBAAQ;;;ACLf,SAAS,OAAO,OAAO,WAAW;AAChC,MAAI,SAAS,CAAC;AACd,MAAI,EAAE,SAAS,MAAM,SAAS;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,IACR,UAAU,CAAC,GACX,SAAS,MAAM;AAEnB,cAAY,qBAAa,WAAW,CAAC;AACrC,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,QAAQ,MAAM,KAAK;AACvB,QAAI,UAAU,OAAO,OAAO,KAAK,GAAG;AAClC,aAAO,KAAK,KAAK;AACjB,cAAQ,KAAK,KAAK;AAAA,IACpB;AAAA,EACF;AACA,qBAAW,OAAO,OAAO;AACzB,SAAO;AACT;AAEA,IAAO,iBAAQ;", "names": []}