{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseSet.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/set.js"], "sourcesContent": ["import assignValue from './_assignValue.js';\nimport castPath from './_castPath.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      lastIndex = length - 1,\n      nested = object;\n\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n        newValue = value;\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue)\n          ? objValue\n          : (isIndex(path[index + 1]) ? [] : {});\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\n\nexport default baseSet;\n", "import baseSet from './_baseSet.js';\n\n/**\n * Sets the value at `path` of `object`. If a portion of `path` doesn't exist,\n * it's created. Arrays are created for missing index properties while objects\n * are created for all other missing properties. Use `_.setWith` to customize\n * `path` creation.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Object\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 3 } }] };\n *\n * _.set(object, 'a[0].b.c', 4);\n * console.log(object.a[0].b.c);\n * // => 4\n *\n * _.set(object, ['x', '0', 'y', 'z'], 5);\n * console.log(object.x[0].y.z);\n * // => 5\n */\nfunction set(object, path, value) {\n  return object == null ? object : baseSet(object, path, value);\n}\n\nexport default set;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,SAAS,QAAQ,QAAQ,MAAM,OAAO,YAAY;AAChD,MAAI,CAAC,iBAAS,MAAM,GAAG;AACrB,WAAO;AAAA,EACT;AACA,SAAO,iBAAS,MAAM,MAAM;AAE5B,MAAI,QAAQ,IACR,SAAS,KAAK,QACd,YAAY,SAAS,GACrB,SAAS;AAEb,SAAO,UAAU,QAAQ,EAAE,QAAQ,QAAQ;AACzC,QAAI,MAAM,cAAM,KAAK,KAAK,CAAC,GACvB,WAAW;AAEf,QAAI,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ,aAAa;AACvE,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,WAAW;AACtB,UAAI,WAAW,OAAO,GAAG;AACzB,iBAAW,aAAa,WAAW,UAAU,KAAK,MAAM,IAAI;AAC5D,UAAI,aAAa,QAAW;AAC1B,mBAAW,iBAAS,QAAQ,IACxB,WACC,gBAAQ,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,MACxC;AAAA,IACF;AACA,wBAAY,QAAQ,KAAK,QAAQ;AACjC,aAAS,OAAO,GAAG;AAAA,EACrB;AACA,SAAO;AACT;AAEA,IAAO,kBAAQ;;;ACpBf,SAAS,IAAI,QAAQ,MAAM,OAAO;AAChC,SAAO,UAAU,OAAO,SAAS,gBAAQ,QAAQ,MAAM,KAAK;AAC9D;AAEA,IAAO,cAAQ;", "names": []}