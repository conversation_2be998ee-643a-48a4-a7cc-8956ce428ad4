import {
  copyArray_default
} from "./chunk-D5XRSADC.js";
import {
  stringToPath_default,
  toKey_default
} from "./chunk-K4DRHDXQ.js";
import {
  toString_default
} from "./chunk-OL6NUAZ6.js";
import {
  arrayMap_default
} from "./chunk-M6TBIOXS.js";
import {
  isSymbol_default
} from "./chunk-ZK54QFLC.js";
import "./chunk-GGTI52PJ.js";
import "./chunk-532EQRVQ.js";
import "./chunk-KUIRPFKY.js";
import "./chunk-P5WJJE5X.js";
import "./chunk-ZNZP756G.js";
import "./chunk-X3F52GTU.js";
import {
  isArray_default
} from "./chunk-VO4BPRKV.js";
import "./chunk-VB7E2QJD.js";
import "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/toPath.js
function toPath(value) {
  if (isArray_default(value)) {
    return arrayMap_default(value, toKey_default);
  }
  return isSymbol_default(value) ? [value] : copyArray_default(stringToPath_default(toString_default(value)));
}
var toPath_default = toPath;
export {
  toPath_default as default
};
//# sourceMappingURL=lodash-es_toPath.js.map
