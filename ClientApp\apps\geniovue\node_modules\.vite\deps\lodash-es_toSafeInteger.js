import {
  toInteger_default
} from "./chunk-TO6KK5ZK.js";
import "./chunk-XJ7DCSNU.js";
import "./chunk-5XD4SZID.js";
import "./chunk-ZK54QFLC.js";
import "./chunk-X3F52GTU.js";
import "./chunk-VB7E2QJD.js";
import "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseClamp.js
function baseClamp(number, lower, upper) {
  if (number === number) {
    if (upper !== void 0) {
      number = number <= upper ? number : upper;
    }
    if (lower !== void 0) {
      number = number >= lower ? number : lower;
    }
  }
  return number;
}
var baseClamp_default = baseClamp;

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/toSafeInteger.js
var MAX_SAFE_INTEGER = 9007199254740991;
function toSafeInteger(value) {
  return value ? baseClamp_default(toInteger_default(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER) : value === 0 ? value : 0;
}
var toSafeInteger_default = toSafeInteger;
export {
  toSafeInteger_default as default
};
//# sourceMappingURL=lodash-es_toSafeInteger.js.map
