{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseClamp.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/toSafeInteger.js"], "sourcesContent": ["/**\n * The base implementation of `_.clamp` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n */\nfunction baseClamp(number, lower, upper) {\n  if (number === number) {\n    if (upper !== undefined) {\n      number = number <= upper ? number : upper;\n    }\n    if (lower !== undefined) {\n      number = number >= lower ? number : lower;\n    }\n  }\n  return number;\n}\n\nexport default baseClamp;\n", "import baseClamp from './_baseClamp.js';\nimport toInteger from './toInteger.js';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Converts `value` to a safe integer. A safe integer can be compared and\n * represented correctly.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toSafeInteger(3.2);\n * // => 3\n *\n * _.toSafeInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toSafeInteger(Infinity);\n * // => 9007199254740991\n *\n * _.toSafeInteger('3.2');\n * // => 3\n */\nfunction toSafeInteger(value) {\n  return value\n    ? baseClamp(toInteger(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER)\n    : (value === 0 ? value : 0);\n}\n\nexport default toSafeInteger;\n"], "mappings": ";;;;;;;;;;;;AASA,SAAS,UAAU,QAAQ,OAAO,OAAO;AACvC,MAAI,WAAW,QAAQ;AACrB,QAAI,UAAU,QAAW;AACvB,eAAS,UAAU,QAAQ,SAAS;AAAA,IACtC;AACA,QAAI,UAAU,QAAW;AACvB,eAAS,UAAU,QAAQ,SAAS;AAAA,IACtC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAO,oBAAQ;;;ACjBf,IAAI,mBAAmB;AA0BvB,SAAS,cAAc,OAAO;AAC5B,SAAO,QACH,kBAAU,kBAAU,KAAK,GAAG,CAAC,kBAAkB,gBAAgB,IAC9D,UAAU,IAAI,QAAQ;AAC7B;AAEA,IAAO,wBAAQ;", "names": []}