/* Ephox PowerPaste plugin
 *
 * Copyright 2010-2015 Ephox Corporation.  All rights reserved.
 *
 * Version: 2.1.0.0
 */
(function(){if(this.ephox)var S=this.ephox.bolt;var C={},I=function(a){if(void 0===C[a])throw"required module ["+a+"] is not defined";if(void 0===C[a].instance){for(var h=C[a].dependencies,k=C[a].definition,s=[],d=0;d<h.length;++d)s.push(I(h[d]));C[a].instance=k.apply(null,s);if(void 0===C[a].instance)throw"required module ["+a+"] could not be defined (definition function returned undefined)";}return C[a].instance},a=this.ephox||{};a.bolt={module:{api:{define:function(a,h,k){if("string"!==typeof a)throw"invalid module definition, module id must be defined and be a string";
if(void 0===h)throw"invalid module definition, dependencies must be specified";if(void 0===k)throw"invalid module definition, definition function must be specified";C[a]={dependencies:h,definition:k,instance:void 0}},require:function(a,h){for(var k=[],s=0;s<a.length;++s)k.push(I(a[s]));h.apply(null,h)},demand:I}}};a.bolt.module.api.define("global!document",[],function(){return document});(function(a,h,k){a("ephox.powerpaste.util.NodeUtil",["global!document"],function(a){return{nodeToString:function(d){var c=
a.createElement("div");c.appendChild(d.cloneNode(!0));return c.innerHTML}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!tinymce",[],function(){return tinymce});(function(a,h,k){a("ephox.powerpaste.i18n.I18n",["global!tinymce"],function(a){var d=function(){return"Your browser security settings may be preventing images from being imported."},c={"cement.dialog.paste.title":"Paste Formatting Options","cement.dialog.paste.instructions":"Choose to keep or remove formatting in the pasted content.",
"cement.dialog.paste.merge":"Keep Formatting","cement.dialog.paste.clean":"Remove Formatting","cement.dialog.flash.title":"Local Image Import","cement.dialog.flash.trigger-paste":"Trigger paste again from the keyboard to paste content with images.","cement.dialog.flash.missing":'Adobe Flash is required to import images from Microsoft Office. Install the <a href="http://get.adobe.com/flashplayer/" target="_blank">Adobe Flash Player</a>.',"cement.dialog.flash.press-escape":'Press <span class="ephox-polish-help-kbd">ESC</span> to ignore local images and continue editing.',
"loading.wait":"Please wait...","flash.clipboard.no.rtf":a.Env.mac&&a.Env.webkit?d()+' <a href="https://support.ephox.com/entries/59328357-Safari-6-1-and-7-Flash-Sandboxing" style="text-decoration: underline">More information on paste for Safari</a>':d(),"safari.imagepaste":'Safari does not support direct paste of images. <a href="https://support.ephox.com/entries/88543243-Safari-Direct-paste-of-images-does-not-work" style="text-decoration: underline">More information on image pasting for Safari</a>',
"error.code.images.not.found":"The images service was not found: (","error.imageupload":"Image failed to upload: (","error.full.stop":").","errors.local.images.disallowed":"Local image paste has been disabled. Local images have been removed from pasted content."};return{translate:function(b){return a.translate(c[b])}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.alien.Once",[],function(){return function(a){var d=!1;return function(){d||
(d=!0,a.apply(null,arguments))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!Array",[],function(){return Array});a.bolt.module.api.define("global!String",[],function(){return String});(function(a,h,k){a("ephox.compass.Arr",["global!Array","global!String"],function(a,d){var c=function(b){return function(c){return b===c}},b=c(!0),e=function(b,e){return t(b,c(e))},f=function(b,c){for(var e=[],d=0;d<b.length;d++)e.push(c(b[d],d,b));
return e},g=function(b,c){for(var e=0;e<b.length;e++)c(b[e],e,b)},l=function(b,c){for(var e=[],d=0;d<b.length;d++){var a=b[d];c(a,d,b)&&e.push(a)}return e},m=function(b,c,e){g(b,function(b){e=c(e,b)});return e},n=function(b,c){if(2!==arguments.length)throw"Expected 2 arguments to find";for(var e=0;e<b.length;e++){var d=b[e];if(c(d,e,b))return d}},r=function(c,e){for(var d=e||b,a=0;a<c.length;++a)if(!0===d(c[a]))return a;return-1},p=function(b){for(var c=[],e=0;e<b.length;++e)c=c.concat(b[e]);return c},
w=function(c,e){for(var d=e||b,a=0;a<c.length;++a)if(!0!==d(c[a],a))return!1;return!0},t=function(c,e){for(var d=e||b,a=0;a<c.length;++a)if(!0===d(c[a]))return!0;return!1},u=function(b){b=a.prototype.slice.call(b,0);b.reverse();return b};return{map:f,each:g,partition:function(b,c){for(var e=[],d=[],a=0;a<b.length;a++){var f=b[a];(c(f,a,b)?e:d).push(f)}return{pass:e,fail:d}},filter:l,groupBy:function(b,c){if(0===b.length)return[];var e=c(b[0]),d=[],a=[];g(b,function(b){var f=c(b);f!==e&&(d.push(a),
a=[]);e=f;a.push(b)});0!==a.length&&d.push(a);return d},indexOf:function(b,e){if(2!==arguments.length)throw"Expected 2 arguments to indexOf";return r(b,c(e))},foldr:function(b,c,e){return m(u(b),c,e)},foldl:m,find:n,findIndex:r,findOr:function(b,c,e){b=n(b,c);return void 0!==b?b:e},findOrDie:function(b,c,e){c=n(b,c);if(void 0===c)throw e||"Could not find element in array: "+d(b);return c},flatten:p,bind:function(b,c){var e=f(b,c);return p(e)},forall:w,exists:t,contains:e,equal:function(b,c){return b.length===
c.length&&w(b,function(b,e){return b===c[e]})},reverse:u,chunk:function(b,c){for(var e=[],d=0;d<b.length;d+=c){var a=b.slice(d,d+c);e.push(a)}return e},difference:function(b,c){return l(b,function(b){return!e(c,b)})},mapToObject:function(b,c){var e={};g(b,function(b,a){e[d(b)]=c(b,a)});return e}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.peanut.Fun",["global!Array"],function(a){return{noop:function(){},compose:function(d,c){return function(){return d(c.apply(null,
arguments))}},constant:function(d){return function(){return d}},identity:function(d){return d},tripleEquals:function(d,c){return d===c},curry:function(d){var c=a.prototype.slice,b=c.call(arguments,1);return function(){var e=b.concat(c.call(arguments,0));return d.apply(null,e)}},not:function(d){return function(){return!d.apply(null,arguments)}},die:function(d){return function(){throw d;}},apply:function(d){return d()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.perhaps.Option",["ephox.peanut.Fun"],function(a){var d=function(c){return b(function(b,d){return d(c)})},c=function(){return b(function(b,c){return b()})},b=function(b){var f=function(){return b(a.constant(!1),a.constant(!0))},g=a.not(f),l=function(b){return m(function(c){return d(b(c))})},m=function(d){return b(c,d)};return{is:function(c){return b(a.constant(!1),function(b){return b===c})},isSome:f,isNone:g,getOr:function(c){return b(a.constant(c),a.identity)},getOrThunk:function(c){return b(c,
a.identity)},getOrDie:function(c){return b(a.die(c||"error: getOrDie called on none."),a.identity)},or:function(c){return b(a.constant(c),d)},orThunk:function(c){return b(c,d)},fold:b,map:l,each:l,bind:m,ap:function(a){return b(c,function(b){return a.fold(c,function(c){return d(c(b))})})},flatten:function(){return b(c,a.identity)},exists:function(c){return b(a.constant(!1),c)},forall:function(c){return b(a.constant(!0),c)},equals:function(c){return b(c.isNone,c.is)},equals_:function(c,d){return b(c.isNone,
function(b){return c.fold(a.constant(!1),a.curry(d,b))})},filter:function(a){return b(c,function(b){return a(b)?d(b):c()})},toArray:function(){return b(a.constant([]),function(b){return[b]})},toString:function(){return b(a.constant("none()"),function(b){return"some("+b+")"})}}};return{some:d,none:c,from:function(b){return null===b||void 0===b?c():d(b)},equals:function(b,c){return b.equals(c)},equals_:function(b,c,d){return b.equals_(c,d)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.powerpaste.imageupload.TinyUploader",["ephox.compass.Arr","ephox.peanut.Fun"],function(a,d){return function(c){return{uploadImages:function(){c.uploadImages()},prepareImages:function(b){a.each(b,function(b){b.fold(function(b,d,e,m){a.each(c.dom.select('img[src="'+e+'"]'),function(b){c.dom.setAttrib(b,"src",m.result)})},d.noop)})},getLocalURL:function(b,c,d,a){return a.result}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,
k){a("ephox.powerpaste.tinymce.ErrorDialog",[],function(){return{showDialog:function(a,d){win=a.windowManager.open({title:"Error",spacing:10,padding:10,items:[{type:"container",html:d}],buttons:[{text:"Ok",onclick:function(){win.close()}}]})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.imageupload.UploadError",["ephox.powerpaste.alien.Once","ephox.powerpaste.i18n.I18n","ephox.powerpaste.tinymce.ErrorDialog"],function(a,d,c){return function(b,
e){var f=function(){return d.translate("error.code.images.not.found")+e+d.translate("error.full.stop")},g=function(){return d.translate("error.imageupload")+e+d.translate("error.full.stop")},l=function(a){a=a.status();c.showDialog(b,(0===a||400<=a||500>a?f:g)())};return{instance:function(){return a(l)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.flour.style.Resolver",["ephox.compass.Arr"],function(a){var d=function(c){return c.replace(/\./g,
"-")};return{create:function(c){var b=d(c);return{resolve:function(c){c=c.split(" ");return a.map(c,function(c){return b+"-"+c}).join(" ")}}},cssNamespace:d,cssClass:function(c,b){return c+"-"+b}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.style.Styles",["ephox.flour.style.Resolver"],function(a){return{resolve:a.create("ephox-salmon").resolve}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.classify.Type",["global!Array","global!String"],function(a,d){var c=function(b){return function(c){if(null===c)c="null";else{var f=typeof c;c="object"===f&&a.prototype.isPrototypeOf(c)?"array":"object"===f&&d.prototype.isPrototypeOf(c)?"string":f}return c===b}};return{isString:c("string"),isObject:c("object"),isArray:c("array"),isNull:c("null"),isBoolean:c("boolean"),isUndefined:c("undefined"),isFunction:c("function"),isNumber:c("number")}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.compass.Obj",[],function(){var a=function(b,c){for(var a in b)b.hasOwnProperty(a)&&c(b[a],a,b)},d=function(b,c){var d={};a(b,function(a,s){var n=c(a,s,b);d[n.k]=n.v});return d},c=function(b,c){var d=[];a(b,function(b,a){d.push(c(b,a))});return d},b=function(b){return c(b,function(b,c){return b})};return{bifilter:function(b,c){var d={},l={};a(b,function(b,a){(c(b,a)?d:l)[a]=b});return{t:d,f:l}},each:a,map:function(b,c){return d(b,function(b,a,d){return{k:a,
v:c(b,a,d)}})},mapToArray:c,tupleMap:d,find:function(b,c){for(var a in b)if(b.hasOwnProperty(a)){var d=b[a];if(c(d,a,b))return d}},keys:function(b){return c(b,function(b,c){return c})},values:b,size:function(c){return b(c).length}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!Error",[],function(){return Error});a.bolt.module.api.define("global!console",[],function(){"undefined"===typeof console&&(console={log:function(){}});return console});
(function(a,h,k){a("ephox.sugar.api.Attr",["ephox.classify.Type","ephox.compass.Arr","ephox.compass.Obj","global!Error","global!console"],function(a,d,c,b,e){var f=function(c,d,f){if(a.isString(f)||a.isBoolean(f)||a.isNumber(f))c.setAttribute(d,f+"");else throw e.error("Invalid call to Attr.set. Key ",d,":: Value ",f,":: Element ",c),new b("Attribute value was not simple");};return{clone:function(b){return d.foldl(b.dom().attributes,function(b,c){b[c.name]=c.value;return b},{})},set:function(b,c,
a){f(b.dom(),c,a)},setAll:function(b,a){var d=b.dom();c.each(a,function(b,c){f(d,c,b)})},get:function(b,c){var a=b.dom().getAttribute(c);return null===a?void 0:a},has:function(b,c){var a=b.dom();return a&&a.hasAttribute?a.hasAttribute(c):!1},remove:function(b,c){b.dom().removeAttribute(c)},hasNone:function(b){b=b.dom().attributes;return void 0===b||null===b||0===b.length}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.scullion.Immutable2",
["ephox.compass.Arr","ephox.compass.Obj","ephox.peanut.Fun"],function(a,d,c){return{product:function(b,e){return{nu:function(){var d=Array.prototype.slice.call(arguments);if(b.length!==d.length)throw'Wrong number of arguments to struct. Expected "['+b.length+']", got '+d.length+" arguments";var e={};a.each(b,function(b,a){e[b]=c.constant(d[a])});return e},eq:function(a,d){for(var s=0;s<b.length;s++){var m=b[s];if(!(e&&e[s]||c.tripleEquals)(a[m](),d[m]()))return!1}return!0},evaluate:function(b){return d.map(b,
function(b){return b()})}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.scullion.Immutable",["ephox.scullion.Immutable2"],function(a){return function(){return a.product(arguments).nu}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.scullion.BagUtils",["ephox.classify.Type","ephox.compass.Arr"],function(a,d){var c=function(b){return b.slice(0).sort()};return{sort:c,reqMessage:function(b,
a){throw"All required keys ("+c(b).join(", ")+") were not specified. Specified keys were: "+c(a).join(", ")+".";},unsuppMessage:function(b){throw"Unsupported keys for object: "+c(b).join(", ");},validateStrArr:function(b,c){if(!a.isArray(c))throw"The "+b+" fields must be an array. Was: "+c+".";d.each(c,function(c){if(!a.isString(c))throw"The value "+c+" in the "+b+" fields was not a string.";})},invalidTypeMessage:function(b,a){throw"All values need to be of type: "+a+". Keys ("+c(b).join(", ")+") were not.";
},checkDupes:function(b){var a=c(b);b=d.find(a,function(b,c){return c<a.length-1&&b===a[c+1]});if(void 0!==b&&null!==b)throw"The field: "+b+" occurs more than once in the combined fields: ["+a.join(", ")+"].";}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!Object",[],function(){return Object});(function(a,h,k){a("ephox.scullion.MixedBag","ephox.compass.Arr ephox.compass.Obj ephox.peanut.Fun ephox.perhaps.Option ephox.scullion.BagUtils global!Object".split(" "),
function(a,d,c,b,e,f){return function(g,l){var m=g.concat(l);if(0===m.length)throw"You must specify at least one required or optional field.";e.validateStrArr("required",g);e.validateStrArr("optional",l);e.checkDupes(m);return function(n){var r=d.keys(n);a.forall(g,function(b){return a.contains(r,b)})||e.reqMessage(g,r);var p=a.filter(r,function(b){return!a.contains(m,b)});0<p.length&&e.unsuppMessage(p);var w={};a.each(g,function(b){w[b]=c.constant(n[b])});a.each(l,function(a){w[a]=c.constant(f.prototype.hasOwnProperty.call(n,
a)?b.some(n[a]):b.none())});return w}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.scullion.Struct",["ephox.scullion.Immutable","ephox.scullion.Immutable2","ephox.scullion.MixedBag"],function(a,d,c){return{immutable:a,immutable2:d,immutableBag:c}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.alien.Recurse",[],function(){return{toArray:function(a,d){var c=[],b=function(b){c.push(b);
return d(b)},e=d(a);do e=e.bind(b);while(e.isSome());return c}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.bud.NodeTypes",[],function(){return{ATTRIBUTE:2,CDATA_SECTION:4,COMMENT:8,DOCUMENT:9,DOCUMENT_TYPE:10,DOCUMENT_FRAGMENT:11,ELEMENT:1,TEXT:3,PROCESSING_INSTRUCTION:7,ENTITY_REFERENCE:5,ENTITY:6,NOTATION:12}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Element",
["ephox.peanut.Fun","global!Error","global!console","global!document"],function(a,d,c,b){var e=function(b){if(null===b||void 0===b)throw new d("Node cannot be not null or undefined");return{dom:a.constant(b)}};return{fromHtml:function(a,d){var s=(d||b).createElement("div");s.innerHTML=a;if(!s.hasChildNodes()||1<s.childNodes.length)throw c.error("HTML does not have a single root node",a),"HTML must have a single root node";return e(s.childNodes[0])},fromTag:function(c,a){var d=(a||b).createElement(c);
return e(d)},fromText:function(c,a){var d=(a||b).createTextNode(c);return e(d)},fromDom:e}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Selectors","ephox.bud.NodeTypes ephox.compass.Arr ephox.perhaps.Option ephox.sugar.api.Element global!Error global!document".split(" "),function(a,d,c,b,e,f){var g=function(){var b=f.createElement("span");return void 0!==b.matches?0:void 0!==b.msMatchesSelector?1:void 0!==b.webkitMatchesSelector?
2:void 0!==b.mozMatchesSelector?3:-1}(),l=a.ELEMENT,m=a.DOCUMENT;return{all:function(c,a){var e=void 0===a?f:a.dom();return e.nodeType!==l&&e.nodeType!==m||0===e.childElementCount?[]:d.map(e.querySelectorAll(c),b.fromDom)},is:function(b,c){var a=b.dom();if(a.nodeType!==l)return!1;if(0===g)return a.matches(c);if(1===g)return a.msMatchesSelector(c);if(2===g)return a.webkitMatchesSelector(c);if(3===g)return a.mozMatchesSelector(c);throw new e("Browser lacks native selectors");},one:function(a,d){var e=
void 0===d?f:d.dom();return e.nodeType!==l&&e.nodeType!==m||0===e.childElementCount?c.none():c.from(e.querySelector(a)).map(b.fromDom)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Compare",["ephox.compass.Arr","ephox.peanut.Fun","ephox.sugar.api.Selectors"],function(a,d,c){var b=function(b,a){return b.dom()===a.dom()};return{eq:b,member:function(c,f){return a.exists(f,d.curry(b,c))},is:c.is}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Traverse","ephox.classify.Type ephox.compass.Arr ephox.peanut.Fun ephox.perhaps.Option ephox.scullion.Struct ephox.sugar.alien.Recurse ephox.sugar.api.Compare ephox.sugar.api.Element".split(" "),function(a,d,c,b,e,f,g,l){var m=function(b){return l.fromDom(b.dom().ownerDocument)},n=function(a){a=a.dom();return b.from(a.parentNode).map(l.fromDom)},r=function(b,a){return n(b).fold(function(){return[]},function(b){var c=[b];return a(b)?c:c.concat(r(b,
a))})},p=function(a){a=a.dom();return b.from(a.previousSibling).map(l.fromDom)},w=function(a){a=a.dom();return b.from(a.nextSibling).map(l.fromDom)},t=function(b){b=b.dom();return d.map(b.childNodes,l.fromDom)},u=function(a,c){var d=a.dom().childNodes;return b.from(d[c]).map(l.fromDom)},v=e.immutable("element","offset");return{owner:m,defaultView:function(b){b=b.dom().ownerDocument.defaultView;return l.fromDom(b)},documentElement:function(b){b=m(b);return l.fromDom(b.dom().documentElement)},parent:n,
findIndex:function(a){return n(a).bind(function(c){c=t(c);c=d.findIndex(c,function(b){return g.eq(a,b)});return-1<c?b.some(c):b.none()})},parents:function(b,d){var e=a.isFunction(d)?d:c.constant(!1);return r(b,e)},siblings:function(b){return n(b).map(t).map(function(a){return d.filter(a,function(a){return!g.eq(b,a)})}).getOr([])},prevSibling:p,offsetParent:function(a){a=a.dom();return b.from(a.offsetParent).map(l.fromDom)},prevSiblings:function(b){return d.reverse(f.toArray(b,p))},nextSibling:w,nextSiblings:function(b){return f.toArray(b,
w)},children:t,child:u,firstChild:function(b){return u(b,0)},lastChild:function(b){return u(b,b.dom().childNodes.length-1)},leaf:function(b,a){var c=t(b);return 0<c.length&&a<c.length?v(c[a],0):v(b,a)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Insert",["ephox.sugar.api.Traverse"],function(a){var d=function(b,c){a.parent(b).each(function(a){a.dom().insertBefore(c.dom(),b.dom())})},c=function(b,a){b.dom().appendChild(a.dom())};
return{before:d,after:function(b,e){a.nextSibling(b).fold(function(){a.parent(b).each(function(b){c(b,e)})},function(b){d(b,e)})},prepend:function(b,d){a.firstChild(b).fold(function(){c(b,d)},function(a){b.dom().insertBefore(d.dom(),a.dom())})},append:c,appendAt:function(b,e,f){a.child(b,f).fold(function(){c(b,e)},function(b){d(b,e)})},wrap:function(b,a){d(b,a);c(a,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.InsertAll",
["ephox.compass.Arr","ephox.sugar.api.Insert"],function(a,d){return{before:function(c,b){a.each(b,function(b){d.before(c,b)})},after:function(c,b){a.each(b,function(a,f){d.after(0===f?c:b[f-1],a)})},prepend:function(c,b){a.each(b.slice().reverse(),function(b){d.prepend(c,b)})},append:function(c,b){a.each(b,function(b){d.append(c,b)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Remove",["ephox.sugar.api.InsertAll","ephox.sugar.api.Traverse"],
function(a,d){var c=function(b){b=b.dom();null!==b.parentNode&&b.parentNode.removeChild(b)};return{empty:function(b){b.dom().textContent=""},remove:c,unwrap:function(b){var e=d.children(b);0<e.length&&a.before(b,e);c(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.peanut.Thunk",[],function(){return{cached:function(a){var d=!1,c;return function(){d||(d=!0,c=a.apply(null,arguments));return c}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Node",["ephox.bud.NodeTypes"],function(a){var d=function(b){return b.dom().nodeName.toLowerCase()},c=function(b){return b.dom().nodeType},b=function(b){return function(a){return c(a)===b}},e=b(a.ELEMENT),f=b(a.TEXT),b=b(a.DOCUMENT);return{name:d,type:c,value:function(b){return b.dom().nodeValue},isElement:e,isText:f,isDocument:b,isComment:function(b){return c(b)===a.COMMENT||"#comment"===d(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Body",["ephox.peanut.Thunk","ephox.sugar.api.Element","ephox.sugar.api.Node","global!document"],function(a,d,c,b){return{body:a.cached(function(){var a=b.body;if(null===a||void 0===a)throw"Body is not available yet";return d.fromDom(a)}),inBody:function(b){b=c.isText(b)?b.dom().parentNode:b.dom();return void 0!==b&&null!==b&&b.ownerDocument.body.contains(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.sugar.api.PredicateFilter",["ephox.compass.Arr","ephox.sugar.api.Body","ephox.sugar.api.Traverse"],function(a,d,c){var b=function(d,f){var g=[];a.each(c.children(d),function(a){f(a)&&(g=g.concat([a]));g=g.concat(b(a,f))});return g};return{all:function(a){return b(d.body(),a)},ancestors:function(b,d,g){return a.filter(c.parents(b,g),d)},siblings:function(b,d){return a.filter(c.siblings(b),d)},children:function(b,d){return a.filter(c.children(b),d)},descendants:b}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.SelectorFilter",["ephox.sugar.api.PredicateFilter","ephox.sugar.api.Selectors"],function(a,d){return{all:function(a){return d.all(a)},ancestors:function(c,b,e){return a.ancestors(c,function(a){return d.is(a,b)},e)},siblings:function(c,b){return a.siblings(c,function(a){return d.is(a,b)})},children:function(c,b){return a.children(c,function(a){return d.is(a,b)})},descendants:function(a,b){return d.all(b,a)}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.impl.ClosestOrAncestor",["ephox.classify.Type","ephox.perhaps.Option"],function(a,d){return function(c,b,e,f,g){return c(e,f)?d.some(e):a.isFunction(g)&&g(e)?d.none():b(e,f,g)}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.SelectorFind",["ephox.perhaps.Option","ephox.sugar.api.SelectorFilter","ephox.sugar.api.Selectors","ephox.sugar.impl.ClosestOrAncestor"],
function(a,d,c,b){var e=function(b,c,e){return a.from(d.ancestors(b,c,e)[0])};return{first:function(b){return a.from(d.all(b)[0])},ancestor:e,sibling:function(b,c){return a.from(d.siblings(b,c)[0])},child:function(b,c){return a.from(d.children(b,c)[0])},descendant:function(b,c){return a.from(d.descendants(b,c)[0])},closest:function(a,d,s){return b(c.is,e,a,d,s)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.api.Ephemera",["ephox.peanut.Fun",
"ephox.salmon.style.Styles","ephox.sugar.api.Attr","ephox.sugar.api.Remove","ephox.sugar.api.SelectorFind"],function(a,d,c,b,e){var f=d.resolve("upload-image-container");d="data-"+d.resolve("image-blob");var g=function(b){c.remove(b,"class")};return{uploadContainer:a.constant(f),blobId:a.constant(d),cleanup:function(a){e.child(a,"img").each(g);b.unwrap(a)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Elements",["ephox.compass.Arr",
"ephox.sugar.api.Element","ephox.sugar.api.Traverse","global!document"],function(a,d,c,b){return{fromHtml:function(a,f){var s=(f||b).createElement("div");s.innerHTML=a;return c.children(d.fromDom(s))},fromTags:function(b,c){return a.map(b,function(b){return d.fromTag(b,c)})},fromText:function(b,c){return a.map(b,function(b){return d.fromText(b,c)})},fromDom:function(b){return a.map(b,d.fromDom)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.tinymce.UndoRewriter",
"ephox.compass.Arr ephox.salmon.api.Ephemera ephox.sugar.api.Element ephox.sugar.api.Elements ephox.sugar.api.InsertAll ephox.sugar.api.SelectorFilter".split(" "),function(a,d,c,b,e,f){return{unwrapHistory:function(g){for(var l=0;l<g.undoManager.data.length;l++){var m=g.undoManager.data[l].content,n=c.fromTag("div");e.append(n,b.fromHtml(m));m=f.descendants(n,"."+d.uploadContainer());a.each(m,d.cleanup);g.undoManager.data[l].content=n.dom().innerHTML}},resrcHistory:function(b,a,c){for(var d=0;d<b.undoManager.data.length;d++)b.undoManager.data[d].content=
b.undoManager.data[d].content.split(a.objurl()).join(c.location)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.epithet.Global",[],function(){return Function("return this;")()})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.epithet.Resolve",["ephox.epithet.Global"],function(a){var d=function(c,b){for(var d=b||a,f=0;f<c.length&&void 0!==d&&null!==d;++f)d=d[c[f]];return d};return{path:d,
resolve:function(a,b){var e=a.split(".");return d(e,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.numerosity.core.Global",["ephox.epithet.Resolve"],function(a){return{getOrDie:function(d,c){var b=a.resolve(d,c);if(void 0===b)throw d+" not available on this browser";return b}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.numerosity.api.URL",["ephox.numerosity.core.Global"],
function(a){return{createObjectURL:function(d){return a.getOrDie("URL").createObjectURL(d)},revokeObjectURL:function(d){a.getOrDie("URL").revokeObjectURL(d)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.api.BlobCache",["ephox.compass.Obj","ephox.numerosity.api.URL","ephox.perhaps.Option","ephox.scullion.Struct"],function(a,d,c,b){var e=b.immutable("id","blob","objurl","data");return function(){var b={},g=function(b){d.revokeObjectURL(b.objurl())};
return{add:function(a,c,d,s){c=e(a,c,d,s);return b[a]=c},get:function(a){return c.from(b[a])},remove:function(a){var c=b[a];delete b[a];void 0!==c&&g(c)},lookupByData:function(d){return c.from(a.find(b,function(b){return b.data().result===d}))},destroy:function(){a.each(b,g);b={}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.porkbun.Event",["ephox.compass.Arr","ephox.scullion.Struct"],function(a,d){return function(c){var b=d.immutable.apply(null,
c),e=[];return{bind:function(b){if(void 0===b)throw"Event bind error: undefined handler";e.push(b)},unbind:function(b){b=a.indexOf(e,b);-1!==b&&e.splice(b,1)},trigger:function(){var c=b.apply(null,arguments);a.each(e,function(b){b(c)})}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.porkbun.Events",["ephox.compass.Obj"],function(a){return{create:function(d){var c=a.map(d,function(b){return{bind:b.bind,unbind:b.unbind}});d=a.map(d,function(b){return b.trigger});
return{registry:c,trigger:d}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.api.ImageTracker","ephox.compass.Arr ephox.salmon.style.Styles ephox.salmon.api.Ephemera ephox.porkbun.Event ephox.porkbun.Events ephox.sugar.api.Attr ephox.sugar.api.SelectorFilter".split(" "),function(a,d,c,b,e,f,g){var l="data-"+d.resolve("image-upload"),m=function(b,a){return g.descendants(b,"img["+l+'="'+a+'"]')},n=function(b){return g.descendants(b,
"img:not(["+l+"])["+c.blobId()+"]")};return function(){var c=[],d=[],g=e.create({complete:b(["response"])}),p=function(b){c=a.filter(c,function(a,c){return a!==b});!1===u()&&(g.trigger.complete(d),d=[])},u=function(){return 0<c.length};return{findById:m,findAll:n,register:function(b,a){f.set(b,l,a);c.push(a)},report:function(b,c,e){a.each(c,function(b){f.remove(b,l);d.push({success:e,element:b.dom()})});p(b)},inProgress:u,isActive:function(b){return a.contains(c,b)},events:g.registry}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.highway.Merger",["ephox.classify.Type"],function(a){var d=function(b){return function(){var a=Array.prototype.slice.call(arguments,0);if(0===a.length)throw"Can't merge zero objects";for(var c={},d=0;d<a.length;d++){var s=a[d],m;for(m in s)Object.prototype.hasOwnProperty.call(s,m)&&(c[m]=b(c[m],s[m]))}return c}},c=d(function(b,d){return a.isObject(b)&&a.isObject(d)?c(b,d):d}),d=d(function(b,a){return a});return{deepMerge:c,
merge:d}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.scullion.ADT",["ephox.classify.Type","ephox.compass.Arr","ephox.compass.Obj","ephox.peanut.Fun","global!Array"],function(a,d,c,b,e){return{generate:function(b){if(!a.isArray(b))throw"cases must be an array";if(0===b.length)throw"there must be at least one case";var g={};d.each(b,function(d,m){var n=c.keys(d);if(1!==n.length)throw"one and only one name per case";var r=n[0],q=d[r];if(void 0!==
g[r])throw"duplicate key detected:"+r;if("cata"===r)throw"cannot have a case named cata (sorry)";if(!a.isArray(q))throw"case arguments must be an array";g[r]=function(){var a=e.prototype.slice.call(arguments);if(a.length!==q.length)throw"Wrong number of arguments to case "+r+". Expected "+q.length+" ("+q+"), got "+a.length;return{fold:function(){if(arguments.length!==b.length)throw"Wrong number of arguments to fold. Expected "+b.length+", got "+arguments.length;return arguments[m].apply(null,a)}}}});
return g}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.hermes.api.ImageAsset",["ephox.highway.Merger","ephox.scullion.ADT"],function(a,d){var c=d.generate([{blob:["id","blob","objurl","data"]},{url:["id","url","raw"]}]);return a.merge(c,{cata:function(b,a,c){return b.fold(a,c)}})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.perhaps.Result",["ephox.peanut.Fun","ephox.perhaps.Option"],
function(a,d){var c=function(a){return e(function(b,c){return c(a)})},b=function(a){return e(function(b,c){return b(a)})},e=function(e){var g=function(){return e(a.constant(!1),a.constant(!0))},l=a.not(g),m=function(a){return n(function(b){return c(a(b))})},n=function(a){return e(b,a)};return{is:function(b){return e(a.constant(!1),function(a){return a===b})},isValue:g,isError:l,getOr:function(b){return e(a.constant(b),a.identity)},getOrThunk:function(b){return e(b,a.identity)},getOrDie:function(){return e(function(b){a.die(b)()},
a.identity)},or:function(b){return e(a.constant(b),c)},orThunk:function(a){return e(a,c)},fold:e,map:m,each:m,bind:n,exists:function(b){return e(a.constant(!1),b)},forall:function(b){return e(a.constant(!0),b)},toOption:function(){return e(d.none,d.some)}}};return{value:c,error:b}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.alien.Toggler",[],function(){return function(a,d,c){var b=c||!1,e=function(){d();b=!0},f=function(){a();b=
!1};return{on:e,off:f,toggle:function(){(b?f:e)()},isOn:function(){return b}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Class",["ephox.sugar.alien.Toggler","ephox.sugar.api.Attr"],function(a,d){var c=function(a,c){var d=a.dom().classList;return void 0!==d&&d.contains(c)};return{add:function(a,c){a.dom().classList.add(c)},remove:function(a,c){var f=a.dom().classList;f.remove(c);0===f.length&&d.remove(a,"class")},toggle:function(a,
c){return a.dom().classList.toggle(c)},toggler:function(b,d){var f=b.dom().classList;return a(function(){f.remove(d)},function(){f.add(d)},c(b,d))},has:c}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.ui.UploadUi","ephox.salmon.api.Ephemera ephox.sugar.api.Class ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.InsertAll ephox.sugar.api.Remove ephox.sugar.api.SelectorFind ephox.sugar.api.Traverse".split(" "),function(a,
d,c,b,e,f,g,l){return{removeUi:function(b){g.ancestor(b,"."+a.uploadContainer()).each(function(a){var b=l.children(a);e.before(a,b);f.remove(a)})},addUi:function(e){var f=c.fromTag("div");d.add(f,a.uploadContainer());b.before(e,f);b.append(f,e)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.api.UploadUtils","ephox.compass.Arr ephox.hermes.api.ImageAsset ephox.peanut.Fun ephox.perhaps.Option ephox.perhaps.Result ephox.salmon.api.Ephemera ephox.salmon.ui.UploadUi ephox.scullion.ADT ephox.scullion.Struct ephox.sugar.api.Attr ephox.sugar.api.SelectorFind global!console".split(" "),
function(a,d,c,b,e,f,g,l,m,n,r,q){var p=m.immutable("image","blobInfo"),t=l.generate([{failure:["error"]},{success:["result","images","blob"]}]),u=function(b,c,d,e){a.each(c,function(a){n.set(a,"src",e.location);n.remove(a,f.blobId())});return k(b,d,c)},v=function(a,b,c,d,s,g){var l=a.lookupByData(s.result).getOrThunk(function(){return a.add(b,c,d,s)});n.set(g,f.blobId(),l.id());return e.value(p(g,l))},h=function(a,b){var c=n.get(b,f.blobId());return a.get(c).fold(function(){return e.error(c)},function(a){return e.value(p(b,
a))})},k=function(a,b,c){return a.get(b).fold(function(){return e.error("Internal error with blob cache")},function(c){a.remove(b);return e.value(c)})};return{prepareForUpload:function(a,c,d){var e=a.isActive(c);a.register(d,c);g.addUi(d);return e?b.none():b.some(c)},handleUpload:function(b,d,e,f,l,m,n){var r=function(){q.error("Internal error with blob cache",l);n(t.failure({status:c.constant(666)}))};b.upload(m,l,function(b){var c=d.findById(f,l);a.each(c,g.removeUi);b.fold(function(a){n(t.failure(a))},
function(a){u(e,c,l,a).fold(r,function(b){n(t.success(a,c,b))})});d.report(l,c,b.isValue())})},registerAssets:function(b,f,g){return a.bind(g,function(a){return d.cata(a,function(a,c,d,s){return r.descendant(f,'img[src="'+d+'"]').fold(function(){return[e.error("Image that was just inserted could not be found: "+d)]},function(e){return[v(b,a,c,d,s,e)]})},c.constant([]))})},findBlobs:function(b,c,d){d=b.findAll(d);return b.inProgress()?[]:a.map(d,function(a){return h(c,a)})}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.numerosity.api.FormData",["ephox.numerosity.core.Global"],function(a){return function(){return new (a.getOrDie("FormData"))}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!Math",[],function(){return Math});a.bolt.module.api.define("global!isFinite",[],function(){return isFinite});a.bolt.module.api.define("global!isNaN",[],function(){return isNaN});a.bolt.module.api.define("global!parseFloat",
[],function(){return parseFloat});(function(a,h,k){a("ephox.violin.util.Validate",["global!Math","global!isFinite","global!isNaN","global!parseFloat"],function(a,d,c,b){var e=function(a){return function(b,c){var d=typeof c;if(d!==a)throw b+" was not a "+a+". Was: "+c+" ("+d+")";}},f=e("string"),g=e("number"),l=function(b,c){g(b,c);if(c!==a.abs(c))throw b+" was not an integer. Was: "+c;};return{vString:f,vChar:function(a,b){f(a,b);if(1!==b.length)throw a+" was not a single char. Was: "+b;},vInt:l,
vNat:function(a,b){l(a,b);if(0>b)throw a+" was not a natural number. Was: "+b;},pNum:function(a){return!c(b(a))&&d(a)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.violin.Strings",["ephox.violin.util.Validate"],function(a){var d=function(a,b,c){return""===b?!0:a.length<b.length?!1:a.substr(c,c+b.length)===b},c=function(a){var b=function(a,b){for(var c=[],d=0;d<a.length;d++)c.push(b(a[d]));return c};return function(){var c=b(arguments,
function(a){return"string"===typeof a?a.toLowerCase():a});return a.apply(this,c)}},b=function(a,b){return d(a,b,0)},e=c(b),f=function(a,b){return d(a,b,a.length-b.length)},g=c(f),l=function(a,b){return a.substr(0,b)},m=function(a,b){return a.substr(a.length-b,a.length)},n=function(a,b){return function(c,d){return a(c,d)?b(c,c.length-d.length):c}},r=n(b,m),n=n(f,l),q=function(a,b){return function(c,d){return a(c,d)?c:b(c,d)}},p=q(b,function(a,b){return b+a}),q=q(f,function(a,b){return a+b}),t=function(a,
b){return-1!=a.indexOf(b)},u=c(t),v=function(a,b){return a===b},c=c(v),h=function(a){if(""===a)throw"head on empty string";return a.substr(0,1)},k=function(a){if(""===a)throw"tail on empty string";return a.substr(1,a.length-1)},A=function(b,c){a.vString("str",b);a.vNat("num",c);for(var d="",e=0;e<c;e++)d+=b;return d},z=function(b){return function(c,d,e){a.vString("str",c);a.vChar("c",d);a.vNat("width",e);var f=c.length;return f>=e?c:b(c,A(d,e-f))}},B=z(function(a,b){return b+a}),z=z(function(a,b){return a+
b});return{supplant:function(a,b){return a.replace(/\${([^{}]*)}/g,function(a,c){var d=b[c],e=typeof d;return"string"===e||"number"===e?d:a})},startsWith:b,startsWithIgnoringCase:e,endsWith:f,endsWithIgnoringCase:g,first:l,last:m,removeLeading:r,removeTrailing:n,ensureLeading:p,ensureTrailing:q,trim:function(a){return a.replace(/^\s+|\s+$/g,"")},lTrim:function(a){return a.replace(/^\s+/g,"")},rTrim:function(a){return a.replace(/\s+$/g,"")},contains:t,containsIgnoringCase:u,htmlEncodeDoubleQuotes:function(a){return a.replace(/\"/gm,
"&quot;")},equals:v,equalsIgnoringCase:c,head:h,repead:A,padLeft:B,padRight:z,toe:function(a){if(""===a)throw"toe on empty string";return a.substr(a.length-1,a.length)},tail:k,torso:function(a){if(""===a)throw"torso on empty string";return a.substr(0,a.length-1)},capitalize:function(a){return""===a?a:h(a).toUpperCase()+k(a)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.services.UploadCommon",["ephox.classify.Type","ephox.compass.Arr",
"ephox.numerosity.api.FormData","ephox.scullion.Struct","ephox.violin.Strings"],function(a,d,c,b,e){var f=["jpg","png","gif","jpeg"];return{failureObject:b.immutable("message","status","contents"),getFilename:function(b,c){var m;a.isString(b.name)&&!e.endsWith(b.name,".tmp")?m=b.name:a.isString(b.type)&&e.startsWith(b.type,"image/")?(m=b.type.substr(6),m=d.contains(f,m)?c+"."+m:c):m=c;return m},buildExtra:function(a,b,d){var e=c();e.append(a,b,d);return{data:e,contentType:!1,processData:!1}}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.numerosity.api.XMLHttpRequest",["ephox.numerosity.core.Global"],function(a){return function(){return new (a.getOrDie("XMLHttpRequest"))}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.jax.base.Ajax","ephox.classify.Type ephox.compass.Obj ephox.highway.Merger ephox.numerosity.api.XMLHttpRequest ephox.perhaps.Result ephox.violin.Strings global!console".split(" "),
function(a,d,c,b,e,f,g){var l={"*":"*/*",text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"};return{ajax:function(m,n,r,q){var p=c.merge({url:m,contentType:"application/json",processData:!1,type:"GET"},q),t=b();t.open(p.type.toUpperCase(),p.url,!0);a.isString(p.contentType)&&t.setRequestHeader("Content-Type",p.contentType);q=p.dataType;q=a.isString(q)&&"*"!==q?l[q]+", "+l["*"]+"; q=0.01":l["*"];t.setRequestHeader("Accept",q);void 0!==p.xhrFields&&
!0===p.xhrFields.withCredentials&&(t.withCredentials=!0);a.isObject(p.headers)&&d.each(p.headers,function(b,c){a.isString(c)||a.isString(b)?t.setRequestHeader(c,b):g.error("Request header data was not a string: ",c," -> ",b)});var u=function(a){r('Could not load url "'+m+'": '+a.status+" "+a.statusText,a.status,a.responseText)},v=function(a){try{return e.value(JSON.parse(a.response))}catch(b){return e.error({status:a.status,statusText:"Response was not JSON",responseText:a.responseText})}},h=function(a){("json"===
p.dataType?v(a):e.value(a.response)).fold(u,function(a){n(a)})};t.onerror=u;t.onload=function(){0===t.status?f.startsWith(p.url,"file:")?h(t):r("Unknown HTTP error (possible cross-domain request)",t.status,t.responseText):100>t.status||400<=t.status?u(t):h(t)};void 0===p.data?t.send():t.send(p.data)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.numerosity.api.JSON",["ephox.numerosity.core.Global"],function(a){return{parse:function(d){return a.getOrDie("JSON").parse(d)},
stringify:function(d){return a.getOrDie("JSON").stringify(d)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.jax.plain.Ajax",["ephox.highway.Merger","ephox.jax.base.Ajax","ephox.numerosity.api.JSON"],function(a,d,c){return{get:function(b,c,f,g){d.ajax(b,c,f,a.merge({dataType:"text",type:"GET"},g))},post:function(b,e,f,g,l){d.ajax(b,f,g,a.merge({dataType:"text",data:c.stringify(e),type:"POST"},l))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.yuri.resolve.Recompose",[],function(){return{recompose:function(a){var d="";""!==a.protocol&&(d+=a.protocol,d+=":");""!==a.authority&&(d=d+"//"+a.authority);d+=a.path;""!==a.query&&(d+="?",d+=a.query);""!==a.anchor&&(d+="#",d+=a.anchor);return d}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.yuri.api.Parser",["ephox.highway.Merger"],function(a){var d={strictMode:!1,key:"source protocol authority userInfo user password host port relative path directory file query anchor".split(" "),
q:{name:"queryKey",parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}},c=function(a,c){for(var d=c.parser[c.strictMode?"strict":"loose"].exec(a),g={},l=14;l--;)g[c.key[l]]=
d[l]||"";g[c.q.name]={};g[c.key[12]].replace(c.q.parser,function(a,b,d){b&&(g[c.q.name][b]=d)});return g};return{parse:function(b,e){var f=a.merge(d,e);return c(b,f)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.yuri.normalize.Dots",["ephox.violin.Strings"],function(a){return{remove:function(d){for(var c="";""!==d;)if(a.startsWith(d,"../"))d=a.removeLeading(d,"../");else if(a.startsWith(d,"./"))d=a.removeLeading(d,"./");else if(a.startsWith(d,
"/./"))d="/"+a.removeLeading(d,"/./");else if("/."===d)d="/";else if(a.startsWith(d,"/../"))d="/"+a.removeLeading(d,"/../"),c=a.removeTrailing(c,c.substring(c.lastIndexOf("/")));else if("/.."===d)d="/",c=a.removeTrailing(c,c.substring(c.lastIndexOf("/")));else if("."===d||".."===d)d="";else{var b=d.match(/(^\/?.*?)(\/|$)/)[1];d=a.removeLeading(d,b);c+=b}return c}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.yuri.resolve.Merge",["ephox.violin.Strings"],
function(a){return{merge:function(d,c,b){if(""!==b&&""===d)return"/"+c;b=d.substring(d.lastIndexOf("/")+1);return a.removeTrailing(d,b)+c}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.yuri.resolve.Transform",["ephox.violin.Strings","ephox.yuri.api.Parser","ephox.yuri.normalize.Dots","ephox.yuri.resolve.Merge"],function(a,d,c,b){return{transform:function(e,f){var g={strictMode:!0},l=d.parse(e,g),g=d.parse(f,g),m={};""!==g.protocol?(m.protocol=
g.protocol,m.authority=g.authority,m.path=c.remove(g.path),m.query=g.query):(""!==g.authority?(m.authority=g.authority,m.path=c.remove(g.path),m.query=g.query):(""===g.path?(m.path=l.path,m.query=""!==g.query?g.query:l.query):(a.startsWith(g.path,"/")?m.path=c.remove(g.path):(m.path=b.merge(l.path,g.path,e.authority),m.path=c.remove(m.path)),m.query=g.query),m.authority=l.authority),m.protocol=l.protocol);m.anchor=g.anchor;return m}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.yuri.api.Resolver",["ephox.yuri.resolve.Recompose","ephox.yuri.resolve.Transform"],function(a,d){return{resolve:function(c,b){var e=d.transform(c,b);return a.recompose(e)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.services.UploadDirect","ephox.classify.Type ephox.highway.Merger ephox.jax.plain.Ajax ephox.numerosity.api.JSON ephox.perhaps.Result ephox.salmon.services.UploadCommon ephox.violin.Strings ephox.yuri.api.Resolver".split(" "),
function(a,d,c,b,e,f,g,l){return function(m){var n=function(){var a=m.url,b=a.lastIndexOf("/"),a=0<b?a.substr(0,b):a,a=void 0===m.basePath?a:m.basePath;return g.endsWith(a,"/")?a:a+"/"}();return{upload:function(g,q,p){g=g.blob();var t=function(a,b,c){p(e.error(f.failureObject(a,b,c)))},u=f.getFilename(g,q);q=d.merge(!0!==m.credentials?{}:{xhrFields:{withCredentials:!0}},f.buildExtra("image",g,u));c.post(m.url,{},function(c){var d;try{var f=b.parse(c);if(a.isString(f.location))d=f.location;else{t("JSON response did not contain a string location",
500,c);return}}catch(g){d=c}c=d.split(/\s+/);c=l.resolve(n,1===c.length&&""!==c[0]?c[0]:u);p(e.value({location:c}))},t,q)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!setTimeout",[],function(){return setTimeout});(function(a,h,k){a("ephox.salmon.services.UploadFunction","ephox.classify.Type ephox.perhaps.Result ephox.salmon.services.UploadCommon ephox.scullion.Struct global!console global!setTimeout".split(" "),function(a,d,c,
b,e,f){var g=b.immutable("id","filename","blob","base64");return function(b){return{upload:function(m,n,r){var q=function(a){r(d.error(a))},p=function(b){a.isString(b)?r(d.value({location:b})):(e.error("Image upload result was not a string"),q(""))},t=c.getFilename(m.blob(),n),u=g(n,t,m.blob(),m.data().result);f(function(){b(u,p,q)},0)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.salmon.api.Uploaders",["ephox.salmon.services.UploadCommon",
"ephox.salmon.services.UploadDirect","ephox.salmon.services.UploadFunction"],function(a,d,c){return{direct:function(a){return d(a)},custom:function(a){return c(a)},failureObject:function(b,c,d){return a.failureObject(b,c,d)},getFilename:function(b,c){return a.getFilename(b,c)},buildExtra:function(b,c,d){return a.buildExtra(b,c,d)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.imageupload.EphoxUploader","ephox.compass.Arr ephox.peanut.Fun ephox.perhaps.Option ephox.powerpaste.imageupload.TinyUploader ephox.powerpaste.imageupload.UploadError ephox.powerpaste.tinymce.UndoRewriter ephox.salmon.api.BlobCache ephox.salmon.api.ImageTracker ephox.salmon.api.UploadUtils ephox.salmon.api.Uploaders ephox.sugar.api.Attr ephox.sugar.api.Element".split(" "),
function(a,d,c,b,e,f,g,l,m,n,r,q){var p=function(b,p){var w=g(),h=l();e();var k=e(b,p.url),A=n.direct(p),z=function(c,d,e){a.each(d,function(a){r.set(a,"data-mce-src",c.location)});f.resrcHistory(b,e,c)};h.events.complete.bind(function(a){f.unwrapHistory(b)});var B=function(a,c,d){m.handleUpload(A,h,w,q.fromDom(b.getBody()),a,c,function(a){a.fold(function(a){d(a)},z)})},D=function(a,b){m.prepareForUpload(h,a.blobInfo().id(),a.image()).each(function(c){B(c,a.blobInfo(),b)})},G=function(c){var d=k.instance();
c=m.registerAssets(w,q.fromDom(b.getBody()),c);a.each(c,function(a){a.fold(function(a){console.error(a)},function(a){D(a,d)})})},M=function(){var d=k.instance(),e=m.findBlobs(h,w,q.fromDom(b.getBody()));a.each(e,function(a){a.fold(function(a){h.report(a,c.none(),!1)},function(a){D(a,d)})})};return{uploadImages:function(a){M();G(a)},prepareImages:d.noop,getLocalURL:function(a,b,c,d){return c}}};return function(a,c){var e;c?e=p(a,c):(e=b(a),e={uploadImages:d.noop,prepareImages:e.prepareImages,getLocalURL:e.getLocalURL});
return e}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.imageupload.UploaderFactory",["ephox.powerpaste.imageupload.EphoxUploader","ephox.powerpaste.imageupload.TinyUploader"],function(a,d){return function(c){var b=!c.uploadImages&&c.settings.images_upload_url?{url:c.settings.images_upload_url,basePath:c.settings.images_upload_base_path,credentials:c.settings.images_upload_credentials}:null;return c.uploadImages?d(c):a(c,b)}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.tinymce.Util",[],function(){return{each:tinymce.each,trim:tinymce.trim,bind:function(a,d){return function(){return a.apply(d,arguments)}},extend:function(a){tinymce.each(Array.prototype.slice.call(arguments,1),function(d){for(var c in d)a[c]=d[c]});return a},ephoxGetComputedStyle:function(a){return a.ownerDocument.defaultView?a.ownerDocument.defaultView.getComputedStyle(a,null):a.currentStyle||{}},log:function(a){"undefined"!==
typeof console&&console.log&&console.log(a)},compose:function(a){var d=Array.prototype.slice.call(a).reverse();return function(a){for(var b=0;b<d.length;b++)a=(0,d[b])(a);return a}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.tinymce.Clipboard",["ephox.powerpaste.legacy.tinymce.Util"],function(a){var d=function(c,b,d){var f,g,l,m=c.selection,n=c.dom;l=c.getBody();if(d.clipboardData&&d.clipboardData.getData("text/html"))return d.preventDefault(),
d=d.clipboardData.getData("text/html"),f=d.match(/<html[\s\S]+<\/html>/i),b(null===f?d:f[0]);if(!n.get("_mcePaste"))if(f=n.add(l,"div",{id:"_mcePaste","class":"mcePaste"},'\ufeff<br _mce_bogus="1">'),l=l!=c.getDoc().body?n.getPos(c.selection.getStart(),l).y:l.scrollTop,n.setStyles(f,{position:"absolute",left:-1E4,top:l,width:1,height:1,overflow:"hidden"}),tinymce.isIE)if(l=n.doc.body.createTextRange(),l.moveToElementText(f),l.execCommand("Paste"),n.remove(f),"\ufeff"===f.innerHTML)c.execCommand("mcePasteWord"),
d.preventDefault();else return b(f.innerHTML),tinymce.dom.Event.cancel(d);else{var r=function(a){a.preventDefault()};n.bind(c.getDoc(),"mousedown",r);n.bind(c.getDoc(),"keydown",r);tinymce.isGecko&&(l=c.selection.getRng(!0),l.startContainer==l.endContainer&&3==l.startContainer.nodeType&&(nodes=n.select("p,h1,h2,h3,h4,h5,h6,pre",f),1==nodes.length&&n.remove(nodes.reverse(),!0)));g=c.selection.getRng();f=f.firstChild;l=c.getDoc().createRange();l.setStart(f,0);l.setEnd(f,1);m.setRng(l);window.setTimeout(function(){var d=
"",e=n.select("div.mcePaste");a.each(e,function(b){var c=b.firstChild;c&&"DIV"==c.nodeName&&c.style.marginTop&&c.style.backgroundColor&&n.remove(c,1);a.each(n.select("div.mcePaste",b),function(a){n.remove(a,1)});a.each(n.select("span.Apple-style-span",b),function(a){n.remove(a,1)});a.each(n.select("br[_mce_bogus]",b),function(a){n.remove(a)});d+=b.innerHTML});a.each(e,function(a){n.remove(a)});g&&m.setRng(g);b(d);n.unbind(c.getDoc(),"mousedown",r);n.unbind(c.getDoc(),"keydown",r)},0)}};return{getOnPasteFunction:function(a,
b){return function(e){d(a,b,e)}},getOnKeyDownFunction:function(a,b){return function(e){(tinymce.isOpera||0<navigator.userAgent.indexOf("Firefox/2"))&&((tinymce.isMac?e.metaKey:e.ctrlKey)&&86==e.keyCode||e.shiftKey&&45==e.keyCode)&&d(a,b,e)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.data.Insert",[],function(){return{insert:function(a,d){var c=d.getDoc(),b,e=d.dom;d.selection.setContent('<span id="ephoxInsertMarker">&nbsp;</span>');
b=e.get("ephoxInsertMarker");for(var f=c.createDocumentFragment();a.firstChild&&!e.isBlock(a.firstChild);)f.appendChild(a.firstChild);for(c=c.createDocumentFragment();a.lastChild&&!e.isBlock(a.lastChild);)c.appendChild(a.lastChild);b.parentNode.insertBefore(f,b);e.insertAfter(c,b);if(a.firstChild){if(e.isBlock(a.firstChild)){for(;!e.isBlock(b.parentNode)&&b.parentNode!==e.getRoot();)b=e.split(b.parentNode,b);e.is(b.parentNode,"td,th")||b.parentNode===e.getRoot()||(b=e.split(b.parentNode,b))}e.replace(a,
b)}else e.remove(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.tinymce.Settings",["ephox.powerpaste.legacy.tinymce.Util"],function(a){var d={strip_class_attributes:"all",retain_style_properties:"none"},c={strip_class_attributes:"none",retain_style_properties:"valid"},b=function(a,b){if(a&&"string"!=typeof a)return a;switch(a){case "clean":return d;case "merge":return c;default:return b}},e=function(c,d,e){c=b(c,d);
return c=a.extend(c,{base_64_images:e})};return{create:function(a,b,l){var s=e(a,d,l),n=e(b,c,l),r=n;return{setWordContent:function(a){r=a?s:n},get:function(a){return r[a]}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.data.tokens.Attributes",["ephox.powerpaste.legacy.tinymce.Util"],function(a){var d=function(a,b){return a&&b?function(d,f){return b(d,a(d,f))}:a||b};return{manager:function(c){var b=0,e,f=function(){return e},
g;g=function(){e={};b=0;a.each(c.attributes,function(a){var c=a.nodeName,d=a.value;(!1!==a.specified||"name"===a.nodeName&&""!==a.value)&&null!==d&&void 0!==d&&(e[c]=d,b++)});void 0===e.style&&c.style.cssText&&(e.style=c.style.cssText,b++);g=f;return e};var l,m,n=function(b){a.each(g(),function(a,c){b(c,a)})};return{get:function(a){return g()[a]},each:n,filter:function(a){l||(m=g);l=d(l,a);g=function(){g=m;n(function(a,d){var f=l(a,d);null===f?(c.removeAttribute(a),delete e[a],b--):f!==d&&("class"===
a?c.className=f:c.setAttribute(a,f),e[a]=f)});g=f;return e}},getAttributes:function(){return g()},getAttributeCount:function(){g();return b}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.data.tokens.Token",["ephox.powerpaste.legacy.data.tokens.Attributes","ephox.powerpaste.legacy.tinymce.Util"],function(a,d,c){var b=a.manager,e=function(a){return a.replace(/-(.)/g,function(a,b){return b.toUpperCase()})},f=function(a){return a.replace(/([A-Z])/g,
function(a,b){return"-"+b.toLowerCase()})},g=!1,l=function(a,b,c){var e,l;b=b||a.getAttribute("style");void 0!==b&&null!==b&&b.split||(b=a.style.cssText);d.each(b.split(";"),function(a){var b=a.indexOf(":");0<b&&(e=d.trim(a.substring(0,b)),e.toUpperCase()===e&&(e=e.toLowerCase()),e=f(e),l=d.trim(a.substring(b+1)),g||(g=0===e.indexOf("mso-")),c(e,l))});g||(l=a.style["mso-list"])&&c("mso-list",l)},m=function(a,c,f){var g,s,m,p,h;switch(a.nodeType){case 1:c?g="endElement":(g="startElement",p=b(a),h=
{},l(a,f,function(a,b){h[a]=b}));s="HTML"!==a.scopeName&&a.scopeName&&a.tagName&&0>=a.tagName.indexOf(":")?(a.scopeName+":"+a.tagName).toUpperCase():a.tagName;break;case 3:g="text";m=a.nodeValue;break;case 8:g="comment";m=a.nodeValue;break;default:d.log("WARNING: Unsupported node type encountered: "+a.nodeType)}var k=function(a){"startElement"===g&&p.filter(a)};return{getNode:function(){p&&p.getAttributes();return a},tag:function(){return s},type:function(){return g},text:function(){return m},toString:function(){return"Type: "+
g+", Tag: "+s+" Text: "+m},getAttribute:function(a){return p.get(a.toLowerCase())},filterAttributes:k,filterStyles:function(b){if("startElement"===g){var c="";d.each(h,function(d,f){var g=b(f,d);null===g?(a.style.removeProperty?a.style.removeProperty(e(f)):a.style.removeAttribute(e(f)),delete h[f]):(c+=f+": "+g+"; ",h[f]=g)});c=c?c:null;k(function(a,b){return"style"===a?c:b});a.style.cssText=c}},getAttributeCount:function(){return p.getAttributeCount()},attributes:function(a){p.each(a)},getStyle:function(a){return h[a]},
styles:function(a){d.each(h,function(b,c){a(c,b)})},getComputedStyle:function(){return d.ephoxGetComputedStyle(a)},isWhitespace:function(){return"text"===g&&/^[\s\u00A0]*$/.test(m)}}};a=function(a,b){return m(b.createElement(a),!0)};return{START_ELEMENT_TYPE:"startElement",END_ELEMENT_TYPE:"endElement",TEXT_TYPE:"text",COMMENT_TYPE:"comment",FINISHED:a("HTML",window.document),token:m,createStartElement:function(a,b,c,f){var g=f.createElement(a),l="";d.each(b,function(a,b){g.setAttribute(b,a)});d.each(c,
function(a,b){l+=b+":"+a+";";g.style[e(b)]=a});return m(g,!1,""!==l?l:null)},createEndElement:a,createComment:function(a,b){return m(b.createComment(a),!1)},createText:function(a,b){return m(b.createTextNode(a))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.data.tokens.Serializer",["ephox.powerpaste.legacy.data.tokens.Token"],function(a){return{create:function(d){var c=d.createDocumentFragment();return{dom:c,receive:function(b){var e=
function(a){a=a.getNode().cloneNode(!1);c.appendChild(a);c=a},f=function(a,b){var e=d.createTextNode(a.text());c.appendChild(e)};switch(b.type()){case a.START_ELEMENT_TYPE:e(b);break;case a.TEXT_TYPE:f(b);break;case a.END_ELEMENT_TYPE:c=c.parentNode;break;case a.COMMENT_TYPE:break;default:throw{message:"Unsupported token type: "+b.type()};}}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.data.tokens.Tokenizer",["ephox.powerpaste.legacy.data.tokens.Token"],
function(a){return{tokenize:function(d,c){var b;c=c||window.document;b=c.createElement("div");c.body.appendChild(b);b.style.position="absolute";b.style.left="-10000px";b.innerHTML=d;nextNode=b.firstChild||a.FINISHED;var e=[];endNode=!1;return{hasNext:function(){return void 0!==nextNode},next:function(){var d=nextNode,g=endNode;!endNode&&nextNode.firstChild?(e.push(nextNode),nextNode=nextNode.firstChild):endNode||1!==nextNode.nodeType?nextNode.nextSibling?(nextNode=nextNode.nextSibling,endNode=!1):
(nextNode=e.pop(),endNode=!0):endNode=!0;d===a.FINISHED||nextNode||(c.body.removeChild(b),nextNode=a.FINISHED);d=d===a.FINISHED?d:d?a.token(d,g):void 0;return d}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.data.tokens.Filter",["ephox.powerpaste.legacy.data.tokens.Token","ephox.powerpaste.legacy.tinymce.Util"],function(a,d){var c=function(b,c){return function(f,g,l){var m,n,r,q=!1,p=function(){c&&c(A);q=!1;n=[];r=[]},
t=function(a){d.each(a,function(a){f.receive(a)})},u=function(a){q?r.push(a):f.receive(a)},h=function(){k();t(r);p()},k=function(){d.each(m,function(a){u(a)});y()},y=function(){m=[]},A={document:l||window.document,settings:g||{},emit:u,receive:function(d){c&&n.push(d);b(A,d);d===a.FINISHED&&h()},startTransaction:function(){q=!0},rollback:function(){t(n);p()},commit:h,defer:function(a){m=m||[];m.push(a)},hasDeferred:function(){return m&&0<m.length},emitDeferred:k,dropDeferred:y};p();return A}};return{createFilter:c,
createAttributeFilter:function(a){return c(function(c,f){f.filterAttributes(d.bind(a,c));c.emit(f)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.Text",["ephox.powerpaste.legacy.data.tokens.Filter","ephox.powerpaste.legacy.data.tokens.Token"],function(a,d){var c=/^(P|H[1-6]|T[DH]|LI|DIV|BLOCKQUOTE|PRE|ADDRESS|FIELDSET|DD|DT|CENTER)$/,b=function(){return null},e=!1;return a.createFilter(function(a,g){var l=function(){e||
(a.emit(d.createStartElement("P",{},{},a.document)),e=!0)};switch(g.type()){case d.TEXT_TYPE:l();a.emit(g);break;case d.END_ELEMENT_TYPE:e&&(c.test(g.tag())||g===d.FINISHED)?(a.emit(d.createEndElement("P",a.document)),e=!1):"BR"===g.tag()&&a.emit(g);break;case d.START_ELEMENT_TYPE:"BR"===g.tag()?(g.filterAttributes(b),g.filterStyles(b),a.emit(g)):"IMG"===g.tag()&&g.getAttribute("alt")&&(l(),a.emit(d.createText(g.getAttribute("alt"),a.document)))}g===d.FINISHED&&a.emit(g)})})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.data.tokens.Helper",["ephox.powerpaste.legacy.data.tokens.Token"],function(a){return{hasNoAttributes:function(d,c){return d.type()===a.START_ELEMENT_TYPE?0===d.getAttributeCount()||c&&1===d.getAttributeCount()&&null!==d.getAttribute("style")&&void 0!==d.getAttribute("style"):d.type()===a.END_ELEMENT_TYPE},supportsCustomStyles:function(){if(0<navigator.userAgent.indexOf("Gecko")&&0>navigator.userAgent.indexOf("WebKit"))return!1;
var d=document.createElement("div");try{d.innerHTML='<p style="mso-list: Ignore;">&nbsp;</p>'}catch(c){return!1}return"Ignore"===a.token(d.firstChild).getStyle("mso-list")}(),spanOrA:function(a){return"A"===a.tag()||"SPAN"===a.tag()},hasMsoListStyle:function(a){return(a=a.getStyle("mso-list"))&&"skip"!==a}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.list.ListTypes",["ephox.powerpaste.legacy.data.tokens.Token",
"ephox.powerpaste.legacy.tinymce.Util"],function(a,d){var c=[{regex:/^\(?[dc][\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[DC][\.\)]$/,type:{tag:"OL",type:"upper-alpha"}},{regex:/^\(?M*(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})[\.\)]$/,type:{tag:"OL",type:"upper-roman"}},{regex:/^\(?m*(cm|cd|d?c{0,3})(xc|xl|l?x{0,3})(ix|iv|v?i{0,3})[\.\)]$/,type:{tag:"OL",type:"lower-roman"}},{regex:/^\(?[0-9]+[\.\)]$/,type:{tag:"OL"}},{regex:/^([0-9]+\.)*[0-9]+\.?$/,type:{tag:"OL",variant:"outline"}},
{regex:/^\(?[a-z]+[\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[A-Z]+[\.\)]$/,type:{tag:"OL",type:"upper-alpha"}}],b={"\u2022":{tag:"UL",type:"disc"},"\u00b7":{tag:"UL",type:"disc"},"\u00a7":{tag:"UL",type:"square"}},e={o:{tag:"UL",type:"circle"},"-":{tag:"UL",type:"disc"},"\u25cf":{tag:"UL",type:"disc"}},f=function(a,b){var c={tag:a.tag,type:a.type,variant:b};a.start&&(c.start=a.start);a.type||delete c.type;return c},g=function(a,b,c){return a===b||a&&b&&a.tag===b.tag&&a.type===b.type&&
(c||a.variant===b.variant)};return{guessListType:function(a,m,s){var r=null,q,p;a&&(q=a.text,p=a.symbolFont);q=d.trim(q);(r=e[q])?r=f(r,q):p?r=(r=b[q])?f(r,q):{tag:"UL",variant:q}:(d.each(c,function(a){if(a.regex.test(q)){if(m&&g(a.type,m,!0))return r=a.type,r.start=parseInt(q),!1;r||(r=a.type);r.start=parseInt(q)}}),r&&!r.variant&&(a="("===q.charAt(0)?"()":")"===q.charAt(q.length-1)?")":".",r=f(r,a)));r&&"OL"===r.tag&&s&&("P"!==s.tag()||/^MsoHeading/.test(s.getAttribute("class")))&&(r=null);return r},
eqListType:g,checkFont:function(b,c){b.type()==a.START_ELEMENT_TYPE&&((font=b.getStyle("font-family"))?c="Wingdings"===font||"Symbol"===font:/^(P|H[1-6]|DIV)$/.test(b.tag())&&(c=!1));return c}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.list.CommentHeuristics",["ephox.powerpaste.legacy.data.tokens.Token","ephox.powerpaste.legacy.filters.list.ListTypes","ephox.powerpaste.legacy.tinymce.Util"],function(a,d,c){var b=
function(a){var b=a.indexOf(".");if(0<=b&&c.trim(a.substring(b+1))===className)return match=results[2],!1},e=function(a){var b={};return function(c,d){var e,s=c+","+d;if(b.hasOwnProperty(s))return b[s];e=a.call(null,c,d);return b[s]=e}}(function(a,d){var e,s=/([^{]+){([^}]+)}/g;for(s.lastIndex=0;null!==(e=s.exec(a));)c.each(e[1].split(","),b(selector));return!1});return{isListWithoutCommentsOrStyles:function(b,e){var l,m=!1,n;n=function(a){(a=a.style.fontFamily)&&(m="Wingdings"===a||"Symbol"===a)};
if(b.type()===a.START_ELEMENT_TYPE&&e.openedTag&&"SPAN"===b.tag()){l=e.openedTag.getNode();n(l);for(1<l.childNodes.length&&"A"===l.firstChild.tagName&&""===l.firstChild.textContent&&(l=l.childNodes[1]);l.firstChild&&("SPAN"===l.firstChild.tagName||"A"===l.firstChild.tagName);)l=l.firstChild,n(l);if((l=l.firstChild)&&3===l.nodeType){n=l.value;c.trim(n)||(n=(l=l.parentNode.nextSibling)?l.value:"");if(!l||c.trim(l.parentNode.textContent)!=n)return!1;if(n=d.guessListType({text:n,symbolFont:m},null,e.originalToken))return l.nextSibling&&
"SPAN"===l.nextSibling.tagName&&/^[\u00A0\s]/.test(l.nextSibling.firstChild.value)&&("P"===e.openedTag.tag()||"UL"===n.tag)}else return l&&"IMG"===l.tagName}return!1},indentGuesser:function(){var a,b;return{guessIndentLevel:function(c,d,s,r){var q=1;if(r&&/^([0-9]+\.)+[0-9]+\.?$/.test(r.text))return r.text.replace(/([0-9]+|\.$)/g,"").length+1;s=b||parseInt(e(s,d.getAttribute("class")));c=c.getNode();d=d.getNode();r=0;for(c=c.parentNode;null!==c&&void 0!==c&&c!==d.parentNode;)r+=c.offsetLeft,c=c.offsetParent;
d=r;s?a?d+=a:0===d&&(a=s,d+=s):s=48;b=s=Math.min(d,s);return q=Math.max(1,Math.floor(d/s))||1}}},styles:function(){var b=!1,c="";return{check:function(d){return b&&d.type()===a.TEXT_TYPE?(c+=d.text(),!0):d.type()===a.START_ELEMENT_TYPE&&"STYLE"===d.tag()?b=!0:d.type()===a.END_ELEMENT_TYPE&&"STYLE"===d.tag()?(b=!1,!0):!1}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.list.Emitter",["ephox.powerpaste.legacy.data.tokens.Token",
"ephox.powerpaste.legacy.filters.list.ListTypes"],function(a,d){var c=["disc","circle","square"];return function(b,e){var f=[],g=[],l=0,m,n=function(c,d){var g={},n={};l++;d&&c.type&&(g={"list-style-type":c.type});c.start&&1<c.start&&(n={start:c.start});f.push(c);b.emit(a.createStartElement(c.tag,n,g,e));m=c},r=function(){b.emit(a.createEndElement(f.pop().tag,e));l--;m=f[f.length-1]},q=function(){var c=g?g.pop():"P";"P"!=c&&b.emit(a.createEndElement(c,e));b.emit(a.createEndElement("LI",e))},p=function(c,
f,l){var q={};if(c){var p=c.getStyle("margin-left");void 0!==p&&(q["margin-left"]=p)}else q["list-style-type"]="none";m&&!d.eqListType(m,f)&&(r(),l&&(b.emit(a.createStartElement("P",{},{},e)),b.emit(a.createText("\u00a0",e)),b.emit(a.createEndElement("P",e))),n(f,!0));b.emit(a.createStartElement("LI",{},q,e));c&&"P"!=c.tag()?(g.push(c.tag()),c.filterStyles(function(){return null}),b.emit(c)):g.push("P")};return{openList:n,closelist:r,closeAllLists:function(){for(;0<l;)q(),r();b.commit()},closeItem:q,
openLI:p,openItem:function(d,f,m,h){if(m){for(l||(l=0);l>d;)q(),r();"UL"===m.tag&&c[d-1]===m.type&&(m={tag:"UL"});if(l==d)q(),p(f,m,h);else for(1<d&&0<g.length&&"P"!==g[g.length-1]&&(b.emit(a.createEndElement(g[g.length-1],e)),g[g.length-1]="P");l<d;)n(m,l==d-1),p(l==d?f:void 0,m)}},getCurrentListType:function(){return m},getCurrentLevel:function(){return l}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.list.ListStates",
["ephox.powerpaste.legacy.data.tokens.Helper","ephox.powerpaste.legacy.data.tokens.Token","ephox.powerpaste.legacy.filters.list.CommentHeuristics","ephox.powerpaste.legacy.filters.list.ListTypes","ephox.powerpaste.legacy.tinymce.Util"],function(a,d,c,b,e){var f=function(a,b){e.log("Unexpected token in list conversion: "+b.toString());a.rollback()},g=function(a,b,c){return b==c?a:null},l=function(b,c,f){f.type()===d.TEXT_TYPE&&""===e.trim(f.text())?b.defer(f):c.skippedPara||f.type()!==d.START_ELEMENT_TYPE||
"P"!==f.tag()||a.hasMsoListStyle(f)?n(b,c,f):(c.openedTag=f,b.defer(f),c.nextFilter=m)},m=function(b,e,f){f.type()!==d.START_ELEMENT_TYPE||"SPAN"!==f.tag()||0!==e.spanCount.length||!a.supportsCustomStyles&&c.isListWithoutCommentsOrStyles(f,e)||a.hasMsoListStyle(f)?f.type()===d.END_ELEMENT_TYPE?"SPAN"===f.tag()?(b.defer(f),e.spanCount.pop()):"P"===f.tag()?(b.defer(f),e.skippedPara=!0,e.openedTag=null,e.nextFilter=l):(e.nextFilter=n,e.nextFilter(b,e,f)):f.isWhitespace()?b.defer(f):(e.nextFilter=n,e.nextFilter(b,
e,f)):(b.defer(f),e.spanCount.push(f))},n=function(b,e,f){var g=function(){e.emitter.closeAllLists();b.emitDeferred();e.openedTag=null;b.emit(f);e.nextFilter=n};if(f.type()===d.START_ELEMENT_TYPE&&a.hasMsoListStyle(f)&&"LI"!==f.tag()){f.getStyle("mso-list");var l=/ level([0-9]+)/.exec(f.getStyle("mso-list"));l&&l[1]?(e.itemLevel=parseInt(l[1],10)+e.styleLevelAdjust,e.nextFilter===n?b.emitDeferred():b.dropDeferred(),e.nextFilter=q,b.startTransaction(),e.originalToken=f,e.commentMode=!1):g()}else!a.supportsCustomStyles&&
(f.type()===d.COMMENT_TYPE&&"[if !supportLists]"===f.text()||c.isListWithoutCommentsOrStyles(f,b))?(f.type()===d.START_ELEMENT_TYPE&&"SPAN"===f.tag()&&e.spanCount.push(f),e.nextFilter=q,b.startTransaction(),e.originalToken=e.openedTag,e.commentMode=!0,e.openedTag=null,b.dropDeferred()):f.type()===d.END_ELEMENT_TYPE&&a.spanOrA(f)?(b.defer(f),e.spanCount.pop()):f.type()===d.START_ELEMENT_TYPE?a.spanOrA(f)?(b.defer(f),e.spanCount.push(f)):(e.openedTag&&(e.emitter.closeAllLists(),b.emitDeferred()),e.openedTag=
f,b.defer(f)):g()},r=function(a,b,c){c.type()===d.END_ELEMENT_TYPE&&b.originalToken.tag()===c.tag()&&(b.nextFilter=l,b.styleLevelAdjust=-1);a.emit(c)},q=function(a,b,c){c.type()==d.START_ELEMENT_TYPE&&"Ignore"===c.getStyle("mso-list")&&(b.nextFilter=p);if(c.type()===d.START_ELEMENT_TYPE&&"SPAN"===c.tag()){if(b.spanCount.push(c),b.commentMode&&""===c.getAttribute("style")||null===c.getAttribute("style"))b.nextFilter=p}else if("A"===c.tag())c.type()===d.START_ELEMENT_TYPE?b.spanCount.push(c):b.spanCount.pop();
else if(c.type()===d.TEXT_TYPE)if(b.commentMode)b.nextFilter=p,b.nextFilter(a,b,c);else{var g=b.originalToken,l=b.spanCount;b.emitter.closeAllLists();a.emit(g);e.each(l,e.bind(a.emit,a));a.emit(c);a.commit();b.originalToken=g;b.nextFilter=r}else(b.commentMode||c.type()!==d.COMMENT_TYPE)&&f(a,c)},p=function(b,c,e){e.type()===d.TEXT_TYPE?e.isWhitespace()||(c.nextFilter=t,c.bulletInfo={text:e.text(),symbolFont:c.symbolFont}):a.spanOrA(e)?e.type()===d.START_ELEMENT_TYPE?c.spanCount.push(e):c.spanCount.pop():
e.type()===d.START_ELEMENT_TYPE&&"IMG"===e.tag()?(c.nextFilter=t,c.bulletInfo={text:"\u2202",symbolFont:!0}):f(b,e)},t=function(b,c,e){e.type()===d.START_ELEMENT_TYPE&&a.spanOrA(e)?(c.spanCount.push(e),c.nextFilter=h):e.type()===d.END_ELEMENT_TYPE&&a.spanOrA(e)?(c.spanCount.pop(),c.nextFilter=k):e.type()===d.END_ELEMENT_TYPE&&"IMG"===e.tag()||f(b,e)},h=function(b,c,e){e.type()===d.END_ELEMENT_TYPE&&(a.spanOrA(e)&&c.spanCount.pop(),c.nextFilter=k)},k=function(c,l,m){var n=function(a){l.nextFilter=
x;l.commentMode&&(l.itemLevel=l.indentGuesser.guessIndentLevel(m,l.originalToken,l.styles.styles,l.bulletInfo));l.listType=b.guessListType(l.bulletInfo,g(l.emitter.getCurrentListType(),l.emitter.getCurrentLevel(),l.itemLevel),l.originalToken);if(l.listType){l.emitter.openItem(l.itemLevel,l.originalToken,l.listType,l.skippedPara);for(c.emitDeferred();0<l.spanCount.length;)c.emit(l.spanCount.shift());a&&c.emit(m)}else e.log("Unknown list type: "+l.bulletInfo.text+" Symbol font? "+l.bulletInfo.symbolFont),
c.rollback()};m.type()===d.TEXT_TYPE||m.type()===d.START_ELEMENT_TYPE?n(!0):m.type()===d.COMMENT_TYPE?n("[endif]"!==m.text()):m.type()===d.END_ELEMENT_TYPE?a.spanOrA(m)&&l.spanCount.pop():f(c,m)},x=function(a,b,c){c.type()===d.END_ELEMENT_TYPE&&c.tag()===b.originalToken.tag()?(b.nextFilter=l,b.skippedPara=!1):a.emit(c)};return{initial:n}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.list.Lists","ephox.powerpaste.legacy.data.tokens.Filter ephox.powerpaste.legacy.data.tokens.Helper ephox.powerpaste.legacy.data.tokens.Token ephox.powerpaste.legacy.filters.list.CommentHeuristics ephox.powerpaste.legacy.filters.list.Emitter ephox.powerpaste.legacy.filters.list.ListStates ephox.powerpaste.legacy.filters.list.ListTypes ephox.powerpaste.legacy.tinymce.Util".split(" "),
function(a,d,c,b,e,f,g,l){var m={},n=function(a){m.nextFilter=f.initial;m.itemLevel=0;m.originalToken=null;m.commentMode=!1;m.openedTag=null;m.symbolFont=!1;m.listType=null;m.indentGuesser=b.indentGuesser();m.emitter=e(a,a.document);m.styles=b.styles();m.spanCount=[];m.skippedPara=!1;m.styleLevelAdjust=0;m.bulletInfo=void 0};n({});return a.createFilter(function(a,b){m.styles.check(b)||(m.symbolFont=g.checkFont(b,m.symbolFont),m.nextFilter(a,m,b))},function(a){n(a)})})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.tinymce.BrowserFilters",["ephox.powerpaste.legacy.tinymce.Util"],function(a){var d=function(a){return 65279===a.charCodeAt(a.length-1)?a.substring(0,a.length-1):a},c=function(a){return/<(h[1-6r]|p|div|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|caption|blockquote|center|dl|dt|dd|dir|fieldset)/.test(a)?a.replace(/(?:<br>&nbsp;[\s\r\n]+|<br>)*(<\/?(h[1-6r]|p|div|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|caption|blockquote|center|dl|dt|dd|dir|fieldset)[^>]*>)(?:<br>&nbsp;[\s\r\n]+|<br>)*/g,
"$1"):a},b=function(a){return a.replace(/<br><br>/g,"<BR><BR>")},e=function(a){return a.replace(/<br>/g," ")},f=function(a){return a.replace(/<BR><BR>/g,"<br>")},g=[d],c=tinymce.isIE&&9<=document.documentMode?[f,e,b,c].concat(g):g;return{all:a.compose(c),textOnly:d}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.FilterInlineStyles",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){var d=/^(mso-.*|tab-stops|tab-interval|language|text-underline|text-effect|text-line-through|font-color|horiz-align|list-image-[0-9]+|separator-image|table-border-color-(dark|light)|vert-align|vnd\..*)$/,
c=function(a){return function(c,f){var g=!1;switch(a){case "all":case "*":g=!0;break;case "valid":g=!d.test(c);break;case void 0:case "none":g="list-style-type"===c;break;default:g=0<=(","+a+",").indexOf(","+c+",")}return g?f:null}};return a.createFilter(function(a,d){var f=a.settings.get("retain_style_properties");d.filterStyles(c(f));a.emit(d)})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.InferListTags",["ephox.powerpaste.legacy.data.tokens.Filter",
"ephox.powerpaste.legacy.data.tokens.Token"],function(a,d){return a.createFilter(function(a,b){a.seenList||(a.inferring?"LI"===b.tag()&&(b.type()===d.START_ELEMENT_TYPE?a.inferring++:(a.inferring--,a.inferring||(a.needsClosing=!0))):("OL"===b.tag()||"UL"===b.tag()?a.seenList=!0:"LI"===b.tag()&&(a.inferring=1,a.needsClosing||a.emit(d.createStartElement("UL",{},{},a.document))),!a.needsClosing||a.inferring||b.isWhitespace()||(a.needsClosing=!1,a.emit(d.createEndElement("UL",a.document)))));a.emit(b)})})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripBookmarks",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createAttributeFilter(function(a,c){return"name"===a||"id"===a?null:c})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripClassAttributes",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createAttributeFilter(function(a,
c){var b;if("class"===a)switch(b=this.settings.get("strip_class_attributes"),b){case "mso":return 0===c.indexOf("Mso")?null:c;case "none":break;default:return null}return c})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripEmptyInlineElements",["ephox.powerpaste.legacy.data.tokens.Filter","ephox.powerpaste.legacy.data.tokens.Helper","ephox.powerpaste.legacy.data.tokens.Token"],function(a,d,c){var b=[],e=[],f=
!1,g=function(a,g){b.push(g);e=e||[];if(g.type()===c.START_ELEMENT_TYPE)e.push(g);else if(g.type()===c.END_ELEMENT_TYPE&&(e.pop(),0===e.length)){if(f){var s,r=b.length,q;for(q=0;q<r;q++)if(s=b[q])if(s.type()===c.START_ELEMENT_TYPE&&"SPAN"===s.tag()&&d.hasNoAttributes(s)){s=r;for(var p=void 0,t=void 0,h=1,p=q+1;p<s;p++)if((t=b[p])&&"SPAN"===t.tag())if(t.type()===c.START_ELEMENT_TYPE)h++;else if(t.type()===c.END_ELEMENT_TYPE&&(h--,0===h)){b[p]=null;break}}else a.emit(s)}b=[];e=[];f=!1}};return a.createFilter(function(a,
e){b=b||[];var s=function(a){return!(0<=",FONT,EM,STRONG,SAMP,ACRONYM,CITE,CODE,DFN,KBD,TT,B,I,U,S,SUB,SUP,INS,DEL,VAR,SPAN,".indexOf(","+a.tag()+",")&&d.hasNoAttributes(a,!0))};0===b.length?e.type()===c.START_ELEMENT_TYPE?s(e)?a.emit(e):g(a,e):a.emit(e):(f||(f=s(e)),g(a,e))})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripEmptyStyleAttributes",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createAttributeFilter(function(a,
c){return"style"===a&&""===c?null:c})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripLangAttribute",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createAttributeFilter(function(a,c){return"lang"===a?null:c})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripImages",["ephox.powerpaste.legacy.data.tokens.Filter",
"ephox.powerpaste.legacy.data.tokens.Token"],function(a,d){return a.createFilter(function(a,b){if("IMG"===b.tag()){if(b.type()===d.END_ELEMENT_TYPE&&a.skipEnd){a.skipEnd=!1;return}if(b.type()===d.START_ELEMENT_TYPE){if(/^file:/.test(b.getAttribute("src"))){a.skipEnd=!0;return}if(a.settings.get("base_64_images")&&/^data:image\/.*;base64/.test(b.getAttribute("src"))){a.skipEnd=!0;return}}}a.emit(b)})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripMetaAndLinkElements",
["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createFilter(function(a,c){"META"!==c.tag()&&"LINK"!==c.tag()&&a.emit(c)})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripNoAttributeA",["ephox.powerpaste.legacy.data.tokens.Filter","ephox.powerpaste.legacy.data.tokens.Helper","ephox.powerpaste.legacy.data.tokens.Token"],function(a,d,c){var b=function(a){return!d.hasNoAttributes(a)&&!/^OLE_LINK/.test(a.getAttribute("name"))},
e=[];return a.createFilter(function(a,d){var l;d.type()===c.START_ELEMENT_TYPE&&"A"===d.tag()?(e.push(d),b(d)&&a.defer(d)):d.type()===c.END_ELEMENT_TYPE&&"A"===d.tag()?(l=e.pop(),b(l)&&a.defer(d),0===e.length&&a.emitDeferred()):a.hasDeferred()?a.defer(d):a.emit(d)})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripScripts",["ephox.powerpaste.legacy.data.tokens.Filter","ephox.powerpaste.legacy.data.tokens.Token"],
function(a,d){var c=!1;return a.createFilter(function(a,e){"SCRIPT"===e.tag()?c=e.type()===d.START_ELEMENT_TYPE:c||(e.filterAttributes(function(a,b){return/^on/.test(a)||"language"===a?null:b}),a.emit(e))})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.wordimport.CommonFilters","ephox.powerpaste.legacy.filters.FilterInlineStyles ephox.powerpaste.legacy.filters.InferListTags ephox.powerpaste.legacy.filters.StripBookmarks ephox.powerpaste.legacy.filters.StripClassAttributes ephox.powerpaste.legacy.filters.StripEmptyInlineElements ephox.powerpaste.legacy.filters.StripEmptyStyleAttributes ephox.powerpaste.legacy.filters.StripLangAttribute ephox.powerpaste.legacy.filters.StripImages ephox.powerpaste.legacy.filters.StripMetaAndLinkElements ephox.powerpaste.legacy.filters.StripNoAttributeA ephox.powerpaste.legacy.filters.StripScripts".split(" "),
function(a,d,c,b,e,f,g,l,m,n,r){return[r,c,l,a,g,f,b,n,e,m,d]})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripFormattingAttributes",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createFilter(function(a,c){c.filterAttributes(function(a,d){return"align"===a||("UL"===c.tag()||"OL"===c.tag())&&"type"===a?null:d});a.emit(c)})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripNamespaceDeclarations",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createAttributeFilter(function(a,c){return/^xmlns(:|$)/.test(a)?null:c})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripOPTags",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createFilter(function(a,c){c.tag&&/^([OVWXP]|U[0-9]+|ST[0-9]+):/.test(c.tag())||
a.emit(c)})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripTocLinks",["ephox.powerpaste.legacy.data.tokens.Filter"],function(a){return a.createAttributeFilter(function(a,c){return"href"===a&&(0<=c.indexOf("#_Toc")||0<=c.indexOf("#_mso"))?null:c})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.filters.StripVMLAttributes",["ephox.powerpaste.legacy.data.tokens.Filter"],
function(a){return a.createAttributeFilter(function(a,c){return/^v:/.test(a)?null:c})})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.wordimport.WordOnlyFilters","ephox.powerpaste.legacy.filters.StripFormattingAttributes ephox.powerpaste.legacy.filters.StripNamespaceDeclarations ephox.powerpaste.legacy.filters.StripOPTags ephox.powerpaste.legacy.filters.StripTocLinks ephox.powerpaste.legacy.filters.StripVMLAttributes ephox.powerpaste.legacy.filters.list.Lists".split(" "),
function(a,d,c,b,e,f){return[c,f,b,e,d,a]})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.legacy.wordimport.WordImport","ephox.powerpaste.legacy.data.tokens.Serializer ephox.powerpaste.legacy.data.tokens.Tokenizer ephox.powerpaste.legacy.filters.Text ephox.powerpaste.legacy.filters.list.Lists ephox.powerpaste.legacy.tinymce.BrowserFilters ephox.powerpaste.legacy.wordimport.CommonFilters ephox.powerpaste.legacy.wordimport.WordOnlyFilters".split(" "),
function(a,d,c,b,e,f,g){var l=function(b,c,e,f){var g=a.create(e);b=d.tokenize(b,e);var l,m=g;for(l=f.length-1;0<=l;l--)m=f[l](m,c,e);for(pipeline=m;b.hasNext();)pipeline.receive(b.next());return g.dom},m=function(a){return 0<=a.indexOf("<o:p>")||0<=a.indexOf("p.MsoNormal, li.MsoNormal, div.MsoNormal")||0<=a.indexOf("MsoListParagraphCxSpFirst")||0<=a.indexOf("<w:WordDocument>")};return{filter:function(a,b,c){a=e.all(a);var d=m(a);b.setWordContent(d);var s=f;d&&(s=g.concat(f));return l(a,b,c,s)},filterPlainText:function(a,
b,d){a=e.textOnly(a);return l(a,b,d,[c])},isWordContent:m}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.tinymce.LegacyTinyDialog",["ephox.powerpaste.legacy.data.Insert","ephox.powerpaste.legacy.tinymce.Settings","ephox.powerpaste.legacy.wordimport.WordImport","global!setTimeout"],function(a,d,c,b){return function(e,f){return{showDialog:function(g){var l=function(b){var f={content:g};e.fire("PastePreProcess",f);b=d.create(b||
e.settings.powerpaste_word_import,b||e.settings.powerpaste_html_import,!0);var l=c.filter(f.content,b,e.getDoc());e.fire("PastePostProcess",l);e.undoManager.transact(function(){a.insert(l,e)})},m=function(a){return"clean"===a||"merge"===a},n=function(){var a,c=[{text:f("cement.dialog.paste.clean"),onclick:function(){a.close();l("clean")}},{text:f("cement.dialog.paste.merge"),onclick:function(){a.close();l("merge")}}],c={title:f("cement.dialog.paste.title"),spacing:10,padding:10,items:[{type:"container",
html:f("cement.dialog.paste.instructions")}],buttons:c};a=e.windowManager.open(c);b(function(){a&&a.getEl().focus()},1)};c.isWordContent(g)&&!m(e.settings.powerpaste_word_import)?n():m(e.settings.powerpaste_html_import)?l():n()}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.tinymce.LegacyPowerPaste",["ephox.powerpaste.i18n.I18n","ephox.powerpaste.legacy.tinymce.Clipboard","ephox.powerpaste.tinymce.LegacyTinyDialog"],function(a,
d,c){return function(b,e){var f=this,g,l=c(b,a.translate),m=function(a){return function(b){a(b)}};g=d.getOnPasteFunction(b,l.showDialog);b.on("paste",m(g));g=d.getOnKeyDownFunction(b,l.showDialog);b.on("keydown",m(g));b.addCommand("mceInsertClipboardContent",function(a,b){l.showDialog(b.content||b)});if(b.settings.paste_preprocess)b.on("PastePreProcess",function(a){b.settings.paste_preprocess.call(f,f,a)})}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.epithet.Id",[],function(){var a=0;return{generate:function(d){var c=(new Date).getTime(),b=Math.floor(1E9*Math.random());a++;return d+"_"+b+a+String(c)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.core.DeviceType",["ephox.peanut.Fun"],function(a){return function(d,c,b){c=d.isiOS()&&-1!==b.search(/iPad/i);var e=d.isiOS()&&!c,f=d.isAndroid()&&3===d.version.major,g=d.isAndroid()&&4===d.version.major;b=c||f||g&&-1===b.search(/mobile/i);
g=(f=d.isiOS()||d.isAndroid())&&!b;return{isiPad:a.constant(c),isiPhone:a.constant(e),isTablet:a.constant(b),isPhone:a.constant(g),isTouch:a.constant(f),isAndroid:d.isAndroid,isiOS:d.isiOS}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.core.Platform",[],function(){return{create:function(a,d,c){return{browser:{current:a,version:d},os:{current:c}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.fred.core.GetterHelper",[],function(){var a=function(a){return function(){return a}};return{getter:a,attachGetters:function(d,c,b){for(var e=0;e<b.length;e++)d["is"+b[e].name]=a(b[e].name===c)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.core.Result",["ephox.fred.core.GetterHelper"],function(a){return{create:function(d,c,b){var e=a.attachGetters,f={};f.current=c;f.version=b;e(f,f.current,d);return f}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.core.SearchInfo",["ephox.violin.Strings"],function(a){var d=a.contains,c=function(a){return function(b){return d(b,a)}},b=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/;return{create:function(a){var f=[{name:"Spartan",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(a){return d(a,"edge/")&&d(a,"chrome")&&d(a,"safari")&&d(a,"applewebkit")}},{name:"ChromeFrame",versionRegexes:[/.*?chromeframe\/([0-9]+)\.([0-9]+).*/,
b],search:function(b){return d(b,"chromeframe")?a():!1}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,b],search:function(a){return d(a,"chrome")&&!d(a,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(b){var c=d(b,"msie")||d(b,"trident");return d(b,"chromeframe")?c&&!a():c}},{name:"Opera",versionRegexes:[b,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:c("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],
search:c("firefox")},{name:"Safari",versionRegexes:[b],search:c("safari")},{name:"Envjs",versionRegexes:[/.*?envjs\/\ ?([0-9]+)\.([0-9]+).*/],search:c("envjs")}],g=[{name:"Windows",search:c("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(a){return d(a,"iphone")||d(a,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/]},{name:"Android",search:c("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:c("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},
{name:"Linux",search:c("linux")},{name:"Solaris",search:c("sunos")},{name:"FreeBSD",search:c("freebsd")}];return{browsers:f,oses:g}},chromeFrameChecker:function(){try{return!!new ActiveXObject("ChromeTab.ChromeFrame")}catch(a){return!1}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.core.SpecTester",[],function(){return{meetsSpec:function(a,d){var c=typeof a;if("boolean"===c)return!!a;if("object"===c)return c=a.minimum,d.major>c.major||
d.major===c.major&&d.minor>=c.minor;throw"invalid spec";}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.core.Fn",[],function(){return{findOneInArrayOr:function(a,d,c){for(var b=0;b<a.length;b++){var e=a[b];if(c(e,b,a))return e}return d}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.core.UaStringDetector",["ephox.fred.core.Fn"],function(a){return{detect:function(d,c){var b=
a.findOneInArrayOr,e=String(c).toLowerCase();return b(d,{name:void 0},function(a){return a.search(e)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.core.VersionDetector",[],function(){return{detectVersion:function(a,d){var c=String(d).toLowerCase(),b=a.versionRegexes;if(b){a:{for(var e=0;e<b.length;e++){var f=b[e];if(f.test(c)){b=f;break a}}b=void 0}c=b?{major:Number(c.replace(b,"$1")),minor:Number(c.replace(b,"$2"))}:{major:0,minor:0}}else c=
{major:0,minor:0};return c}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fred.PlatformDetection","ephox.fred.core.DeviceType ephox.fred.core.Platform ephox.fred.core.Result ephox.fred.core.SearchInfo ephox.fred.core.SpecTester ephox.fred.core.UaStringDetector ephox.fred.core.VersionDetector".split(" "),function(a,d,c,b,e,f,g){var l=g.detectVersion,m=c.create,n=e.meetsSpec,r=f.detect,q=function(a,b,c,d){return a[b]?a[b][c]?n(a[b][c],d):
!!a[b].All:!1},p=function(c,d){var e=b.create(d),f=e.browsers,e=e.oses,g=r(e,c),n=g.name,g=l(g,c),p=r(f,c),w=p.name,h=l(p,c),e=m(e,n,g),f=m(f,w,h),g=a(e,f,c);return{browser:f,os:e,deviceType:g,isSupported:function(a){return q(a,n,w,h)}}};return{Platform:d,detect:function(){return p(navigator.userAgent,b.chromeFrameChecker)},doDetect:p,isSupported:q,isSupportedPlatform:function(a,b){var c=b.browser;return q(a,b.os.current,c.current,c.version)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.numerosity.api.FileReader",["ephox.numerosity.core.Global"],function(a){return function(){return new (a.getOrDie("FileReader"))}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.hermes.utils.ImageExtract","ephox.compass.Arr ephox.epithet.Id ephox.fred.PlatformDetection ephox.hermes.api.ImageAsset ephox.numerosity.api.FileReader ephox.numerosity.api.URL ephox.scullion.Struct".split(" "),function(a,
d,c,b,e,f,g){var l=g.immutable("id","obj","objurl"),m=function(a){return l(d.generate("image"),a,f.createObjectURL(a))};c=c.detect();var n=function(c,d){if(0===c.length)d([]);else{var f=[],g=function(a,b){var c=e();c.onload=function(c){b(a,c.target)};c.readAsDataURL(a)};a.each(c,function(a){var e=a.id(),l=a.obj(),m=a.objurl();g(l,function(a,g){var l=b.blob(e,a,m,g);f.push(l);f.length===c.length&&d(f)})})}};g=function(b){return 1===b.length&&a.contains(b,"Files")};var r=function(b){return!a.contains(b,
"text/_moz_htmlcontext")},q=function(b){return a.contains(b,"Files")},p=function(a){return!0};c=c.browser.isChrome()||c.browser.isSafari()||c.browser.isOpera()?q:c.browser.isFirefox()?r:c.browser.isIE()?g:p;return{blob:m,toAssets:function(b,c){var d=a.map(b,function(a){return m(a)});n(d,c)},toFiles:function(a){return a.raw().target.files||a.raw().dataTransfer.files},isFiles:c,fromImages:function(c){return a.map(c,function(a){var c=d.generate("image");return b.url(c,a.src,a)})},fromBlobs:n}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.hermes.api.ImageExtract",["ephox.hermes.utils.ImageExtract"],function(a){return{toAssets:function(d,c){return a.toAssets(d,c)},fromBlobs:function(d,c){return a.fromBlobs(d,c)},blob:a.blob}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.tinymce.ModernPowerDrop","ephox.compass.Arr ephox.hermes.api.ImageAsset ephox.hermes.api.ImageExtract ephox.sugar.api.Attr ephox.sugar.api.Element global!tinymce".split(" "),
function(a,d,c,b,e,f){return function(g,l,m,n){var r,q=/^image\/(jpe?g|png|gif|bmp)$/i;g.on("dragstart dragend",function(a){r="dragstart"===a.type});g.on("dragover dragend dragleave",function(a){a.preventDefault()});var p=function(b){return a.filter(b.target.files||b.dataTransfer.files,function(a){return q.test(a.type)})},t=function(c){return a.map(c,function(a){var c=e.fromTag("img");a=d.cata(a,n.getLocalURL,function(a,b,c){return b});b.set(c,"src",a);return c.dom().outerHTML}).join("")},h=function(a){c.toAssets(a,
function(a){var b=t(a);g.insertContent(b,{merge:!1!==g.settings.paste_merge_formats});n.uploadImages(a)})};g.on("drop",function(a){if(!r){if(f.dom.RangeUtils&&f.dom.RangeUtils.getCaretRangeFromPoint){var b=f.dom.RangeUtils.getCaretRangeFromPoint(a.clientX,a.clientY,g.getDoc());b&&g.selection.setRng(b)}b=p(a);0<b.length&&h(b);a.preventDefault()}})}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.numerosity.api.Blob",["ephox.numerosity.core.Global"],
function(a){return function(d,c){return new (a.getOrDie("Blob"))(d,c)}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.numerosity.api.Uint8Array",["ephox.numerosity.core.Global"],function(a){return function(d){return new (a.getOrDie("Uint8Array"))(d)}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!parseInt",[],function(){return parseInt});(function(a,h,k){a("ephox.bowerbird.codes.HexToBlob",
"ephox.numerosity.api.Blob ephox.numerosity.api.Uint8Array global!Array global!Math global!String global!parseInt".split(" "),function(a,d,c,b,e,f){return{convert:function(e,l){if(0===e.length)throw"Zero length content passed to Hex conversion";for(var m=new c(e.length/2),n=0;n<e.length;n+=2){var r=e.substr(n,2),q=b.floor(n/2);m[q]=f(r,16)}m=d(m);return a([m],{type:l})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.bowerbird.core.Species",
["ephox.peanut.Fun","ephox.perhaps.Option"],function(a,d){var c=function(b,c,e,f,g){return-1===b||-1===c?d.none():d.some({start:a.constant(b),end:a.constant(c),bower:e,regex:a.constant(f),idRef:a.constant(g)})},b=function(a,b,c){return function(){return a.substring(b,c)}},e=function(a,b){var c=0,d=a.length,e,f;do if(e=a.indexOf("{",b),f=a.indexOf("}",b),f>e&&-1!==e?(b=e+1,++c):(e>f||0>e)&&-1!==f&&(b=f+1,--c),b>d||-1===f)return-1;while(0<c);return b},f=function(a,d,f){d=-1===f?f:e(a,f);a=b(a,f,d);
return c(f,d,a,/([a-hA-H0-9]+)\}$/,"i")},g=function(a,d,f){d=-1===f?f:e(a,f);a=b(a,f,d);return c(f,d,a,/([a-hA-H0-9]{64,})(?:\}.*)/,"s")};return{identify:function(a,b){var c=a.indexOf("{\\pict{",b),e=a.indexOf("{\\shp{",b);return-1===c&&-1===e?d.none():-1===c?g(a,b,e):-1===e?f(a,b,c):c<e?f(a,b,c):e<c?g(a,b,e):d.none()},endBracket:e}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.bowerbird.core.Rtf",["ephox.bowerbird.codes.HexToBlob","ephox.bowerbird.core.Species",
"ephox.perhaps.Option"],function(a,d,c){var b=function(a,b){return d.identify(a,b)},e=function(a){return 0<=a.indexOf("\\pngblip")?c.some("image/png"):0<=a.indexOf("\\jpegblip")?c.some("image/jpeg"):c.none()},f=function(a){return 0===a.length%2},g=function(a,b){var d=a.match(b);return null!==d?c.some(d[1]):c.none()},l=function(a,b){return g(a,b).filter(f)},m=function(a){return g(a,/\\shplid(\d+)/)},n=function(b){var c=b.bower(),d=b.regex();return m(c).bind(function(f){return e(c).bind(function(e){return l(c,
d).map(function(c){return{id:b.idRef()+f,contentType:e,blob:a.convert(c,e)}})})})},r=function(a){for(var c=[],d=function(){return a.length},e=function(a){n(a).each(function(a){c.push(a)});return a.end()+1},f=0;f<a.length;)f=b(a,f).fold(d,e);return c};return{nextBower:b,extractId:m,extractContentType:e,extractHex:l,images:function(a){return r(a.replace(/\r/g,"").replace(/\n/g,""))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.bowerbird.api.Rtf",
["ephox.bowerbird.core.Rtf"],function(a){return{images:function(d){return a.images(d)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.flash.Correlation","ephox.compass.Arr ephox.hermes.api.ImageExtract ephox.perhaps.Option ephox.scullion.Struct ephox.sugar.api.Attr ephox.sugar.api.Class".split(" "),function(a,d,c,b,e,f){var g=b.immutable("id","url"),l={local:function(a,b,d){return c.from(b[d])},code:function(b,d,f){d=a.find(d,function(a){return e.get(b,
"data-image-id")===a.id()});return c.from(d)}};return{convert:function(b,n,r){var q=[];n=a.bind(n,function(b){if(a.exists(q,function(a){return a.id()===b.id}))return[];var c=d.blob(b.blob);q.push(g(b.id,c.objurl()));return[c]});d.fromBlobs(n,function(d){a.each(b,function(a,b){var d=e.get(a,"data-image-type");(void 0!==l[d]?l[d]:c.none)(a,q,b).each(function(b){e.set(a,"src",b.url())});f.remove(a,"rtf-data-image");e.remove(a,"data-image-type");e.remove(a,"data-image-id")});r(d)})}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.style.Styles",["ephox.flour.style.Resolver"],function(a){return{resolve:a.create("ephox-cement").resolve}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.flash.HelpCopy",["ephox.cement.style.Styles","ephox.fred.PlatformDetection","ephox.sugar.api.Class","ephox.sugar.api.Element","ephox.sugar.api.InsertAll"],function(a,d,c,b,e){var f=function(a){return b.fromHtml("<p>"+
a("cement.dialog.flash.press-escape")+"</p>")};return{paste:function(g){var l=b.fromTag("div");c.add(l,a.resolve("flashbin-helpcopy"));var m;m=d.detect().os.isOSX()?["\u2318"]:["Ctrl"];var n=b.fromHtml("<p>"+g("cement.dialog.flash.trigger-paste")+"</p>");m=b.fromHtml('<div><span class="ephox-polish-help-kbd">'+m+'</span> + <span class="ephox-polish-help-kbd">V</span></div>');c.add(m,a.resolve("flashbin-helpcopy-kbd"));e.append(l,[n,m,f(g)]);return l},noflash:function(d){var l=b.fromTag("div");c.add(l,
a.resolve("flashbin-helpcopy"));var m=b.fromHtml("<p>"+d("cement.dialog.flash.missing")+"</p>");e.append(l,[m,f(d)]);return l},indicator:function(d){var f=b.fromTag("div");c.add(f,a.resolve("flashbin-loading"));var m=b.fromTag("div");c.add(m,a.resolve("flashbin-loading-spinner"));var n=b.fromTag("p");n.dom().innerHTML=d("loading.wait");e.append(f,[m,n]);return f}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!window",[],function(){return window});
(function(a,h,k){a("ephox.sugar.api.Css","ephox.classify.Type ephox.compass.Obj ephox.perhaps.Option ephox.sugar.api.Attr ephox.sugar.api.Body ephox.sugar.api.Element ephox.violin.Strings global!Error global!console global!window".split(" "),function(a,d,c,b,e,f,g,l,m,n){var r=function(b,c,d){if(!a.isString(d))throw m.error("Invalid call to CSS.set. Property ",c,":: Value ",d,":: Element ",b),new l("CSS value must be a string: "+d);b.style.setProperty(c,d)},q=function(a,b,c){a=a.dom();r(a,b,c)},p=
function(a,b){var d=a.dom().style.getPropertyValue(b);return c.from(d).filter(function(a){return 0<a.length})};return{copy:function(a,b){b.dom().style.cssText=a.dom().style.cssText},set:q,preserve:function(a,c){var d=b.get(a,"style"),e=c(a);(void 0===d?b.remove:b.set)(a,"style",d);return e},setAll:function(a,b){var c=a.dom();d.each(b,function(a,b){r(c,b,a)})},remove:function(a,c){a.dom().style.removeProperty(c);b.has(a,"style")&&""===g.trim(b.get(a,"style"))&&b.remove(a,"style")},get:function(a,b){var c=
a.dom(),d=n.getComputedStyle(c).getPropertyValue(b),c=""!==d||e.inBody(a)?d:c.style.getPropertyValue(b);return null===c?void 0:c},getRaw:p,isValidValue:function(a,b,c){a=f.fromTag(a);q(a,b,c);return p(a,b).isSome()},reflow:function(a){return a.dom().offsetWidth}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!navigator",[],function(){return navigator});(function(a,h,k){a("ephox.cement.flash.FlashInfo","ephox.cement.flash.HelpCopy ephox.cement.style.Styles ephox.fred.PlatformDetection ephox.peanut.Fun ephox.sugar.api.Class ephox.sugar.api.Css ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.InsertAll global!navigator".split(" "),
function(a,d,c,b,e,f,g,l,m,n){var r=c.detect(),q=function(c,d,e,f){d=a.noflash(f);l.append(c,d);return{reset:b.noop}},p=function(b,c,d,e){var g=a.paste(e),l=a.indicator(e);m.append(b,[l,g,c.element()]);b=function(){f.set(g,"display","block");f.set(l,"display","none");d()};c.events.spin.bind(function(){f.set(g,"display","none");f.set(l,"display","block");f.remove(l,"height");f.remove(l,"padding");d()});c.events.reset.bind(b);c.events.hide.bind(function(){f.setAll(l,{height:"0",padding:"0"})});return{reset:b}};
return function(a,c,f){var l=g.fromTag("div"),m="flashbin-wrapper-"+(r.os.isOSX()?"cmd":"ctrl");e.add(l,d.resolve(m));var s;try{s=void 0!==(r.browser.isIE()?new ActiveXObject("ShockwaveFlash.ShockwaveFlash"):n.plugins["Shockwave Flash"])}catch(h){s=!1}a=(s?p:q)(l,a,c,f.translations);return{element:b.constant(l),reset:a.reset}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!clearInterval",[],function(){return clearInterval});a.bolt.module.api.define("global!setInterval",
[],function(){return setInterval});(function(a,h,k){a("ephox.cement.alien.WaitForFlash",["ephox.classify.Type","ephox.compass.Arr","global!clearInterval","global!setInterval"],function(a,d,c,b){return function(e,f,g){var l=function(b){return d.forall(f,function(c){return a.isFunction(b[c])})},m=!0,n=b(function(){var b=e.dom();a.isFunction(b.PercentLoaded)&&100===b.PercentLoaded()&&l(b)&&(r(),g())},500),r=function(){m&&(c(n),m=!1)};return{stop:r}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.epithet.Namespace",["ephox.epithet.Global"],function(a){return{namespace:function(d,c){for(var b=c||a,e=d.split("."),f=0;f<e.length;++f){var g=e[f];if(void 0===b[g]||null===b[g])b[g]={};b=b[g]}return b}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.oilspill.callback.Globaliser",["ephox.epithet.Namespace"],function(a){return{install:function(d){var c=a.namespace(d);c.callbacks={};var b=
0,e=function(){var a="callback_"+b;b++;return a},f=function(a,b){var f=e();c.callbacks[f]=function(){b||g(f);a.apply(null,arguments)};return d+".callbacks."+f},g=function(a){a=a.substring(a.lastIndexOf(".")+1);void 0!==c.callbacks[a]&&delete c.callbacks[a]};c.ephemeral=function(a){return f(a,!1)};c.permanent=function(a){return f(a,!0)};c.unregister=g;return c}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.PredicateFind","ephox.classify.Type ephox.compass.Arr ephox.peanut.Fun ephox.perhaps.Option ephox.sugar.api.Body ephox.sugar.api.Compare ephox.sugar.api.Element ephox.sugar.impl.ClosestOrAncestor".split(" "),
function(a,d,c,b,e,f,g,l){var m=function(d,e,f){d=d.dom();for(f=a.isFunction(f)?f:c.constant(!1);d.parentNode;){d=d.parentNode;var l=g.fromDom(d);if(e(l))return b.some(l);if(f(l))break}return b.none()},n=function(a,e){var f=d.find(a.dom().childNodes,c.compose(e,g.fromDom));return b.from(f).map(g.fromDom)},r=function(a,c){var d=function(a){for(var e=0;e<a.childNodes.length;e++){if(c(g.fromDom(a.childNodes[e])))return b.some(g.fromDom(a.childNodes[e]));var f=d(a.childNodes[e]);if(f.isSome())return f}return b.none()};
return d(a.dom())};return{first:function(a){return r(e.body(),a)},ancestor:m,closest:function(a,b,c){return l(function(a){return b(a)},m,a,b,c)},sibling:function(a,c){var d=a.dom();return d.parentNode?n(g.fromDom(d.parentNode),function(b){return!f.eq(a,b)&&c(b)}):b.none()},child:n,descendant:r}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.PredicateExists",["ephox.sugar.api.PredicateFind"],function(a){return{any:function(d){return a.first(d).isSome()},
ancestor:function(d,c,b){return a.ancestor(d,c,b).isSome()},closest:function(d,c,b){return a.closest(d,c,b).isSome()},sibling:function(d,c){return a.sibling(d,c).isSome()},child:function(d,c){return a.child(d,c).isSome()},descendant:function(d,c){return a.descendant(d,c).isSome()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Focus","ephox.peanut.Fun ephox.perhaps.Option ephox.sugar.api.Compare ephox.sugar.api.Element ephox.sugar.api.PredicateExists ephox.sugar.api.Traverse global!document".split(" "),
function(a,d,c,b,e,f,g){var l=function(a){a.dom().focus()},m=function(a){a=void 0!==a?a.dom():g;return d.from(a.activeElement).map(b.fromDom)};return{hasFocus:function(a){var b=f.owner(a).dom();return a.dom()===b.activeElement},focus:l,blur:function(a){a.dom().blur()},active:m,search:function(a){return m(f.owner(a)).filter(function(b){return a.dom().contains(b.dom())})},focusInside:function(b){var d=f.owner(b);m(d).filter(function(d){return e.closest(d,a.curry(c.eq,b))}).fold(function(){l(b)},a.noop)}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!clearTimeout",[],function(){return clearTimeout});a.bolt.module.api.define("global!unescape",[],function(){return unescape});(function(a,h,k){a("ephox.cement.flash.Flashbin","ephox.cement.alien.WaitForFlash ephox.cement.style.Styles ephox.compass.Arr ephox.compass.Obj ephox.epithet.Id ephox.fred.PlatformDetection ephox.oilspill.callback.Globaliser ephox.perhaps.Option ephox.porkbun.Event ephox.porkbun.Events ephox.sugar.api.Class ephox.sugar.api.Css ephox.sugar.api.Element ephox.sugar.api.Focus ephox.sugar.api.Insert global!clearTimeout global!console global!setTimeout global!unescape global!window".split(" "),
function(a,d,c,b,e,f,g,l,m,n,r,q,p,h,k,v,x,y,A,z){var B=g.install("ephox.flash"),D=f.detect(),G=l.none();return function(f){var g=n.create({response:m(["rtf"]),spin:m([]),cancel:m([]),error:m(["message"]),reset:m([]),hide:m([])}),l=!1,E=p.fromTag("div");r.add(E,d.resolve("flashbin-target"));var J=function(){O.stop();if(!l){l=!0;try{var a=F.dom();b.each(H,function(b,c){a[c].call(a,b)});g.trigger.reset();v(P);r.remove(E,d.resolve("flash-activate"));Q();N()}catch(c){x.log("Flash dialog - Error during onLoad ",
c)}}},L=B.permanent(J),H={setSpinCallback:B.permanent(g.trigger.spin),setPasteCallback:B.permanent(function(a){y(function(){g.trigger.response(A(a))},0)}),setEscapeCallback:B.permanent(g.trigger.cancel),setErrorCallback:B.permanent(g.trigger.error)},F=function(){var a=f.replace(/^https?:\/\//,"//"),b='    <param name="allowscriptaccess" value="always">    <param name="wmode" value="opaque">    <param name="FlashVars" value="onLoad='+L+'">';if(D.browser.isIE()&&10===D.browser.version.major){var c=
e.generate("flash-bin");return p.fromHtml('<object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" id="'+c+'"><param name="movie" value="'+a+'">'+b+"</object>")}return p.fromHtml('<object type="application/x-shockwave-flash" data="'+a+'">'+b+"</object>")}(),Q=function(){q.setAll(F,{width:"2px",height:"2px"})};Q();var O=a(F,b.keys(H),J);k.append(E,F);var N=function(){D.browser.isFirefox()&&z.getSelection().removeAllRanges();h.focus(F)},P=null,T=function(){r.add(E,d.resolve("flash-activate"));
q.remove(F,"height");q.remove(F,"width");g.trigger.hide()},R=function(){q.set(E,"display","none");G.each(function(a){c.each(a,function(a){a.unbind()})})};return{focus:N,element:function(){return E},activate:function(){P=y(T,3E3);g.trigger.spin();q.set(E,"display","block");N()},deactivate:R,destroy:function(){R();c.each(b.values(H),function(a){B.unregister(a)});B.unregister(L);O.stop()},events:g.registry}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.sugar.impl.FilteredEvent",["ephox.peanut.Fun","ephox.sugar.api.Element"],function(a,d){var c=function(b,c,d,e,f,q,p){return{target:a.constant(b),x:a.constant(c),y:a.constant(d),stop:e,prevent:f,kill:q,raw:a.constant(p)}},b=function(b,e){return function(f){if(b(f)){var n=d.fromDom(f.target),r=function(){f.stopPropagation()},q=function(){f.preventDefault()},p=a.compose(q,r),n=c(n,f.clientX,f.clientY,r,q,p,f);e(n)}}},e=function(c,d,e,n,r){e=b(e,n);c.dom().addEventListener(d,e,r);return{unbind:a.curry(f,
c,d,e,r)}},f=function(a,b,c,d){a.dom().removeEventListener(b,c,d)};return{bind:function(a,b,c,d){return e(a,b,c,d,!1)},capture:function(a,b,c,d){return e(a,b,c,d,!0)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.DomEvent",["ephox.peanut.Fun","ephox.sugar.impl.FilteredEvent"],function(a,d){var c=a.constant(!0);return{bind:function(a,e,f){return d.bind(a,e,c,f)},capture:function(a,e,f){return d.capture(a,e,c,f)}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.flash.FlashDialog","ephox.cement.flash.FlashInfo ephox.cement.flash.Flashbin ephox.porkbun.Event ephox.porkbun.Events ephox.sugar.api.DomEvent ephox.sugar.api.Element global!window".split(" "),function(a,d,c,b,e,f,g){return function(l,m){var n=m.translations,r=b.create({response:c(["rtf","hide"]),cancel:c([]),error:c(["message"])});return{open:function(){var b=d(m.swf);b.deactivate();var c=f.fromDom(g),p=e.bind(c,
"mouseup",b.focus),h=function(){y()},c=function(){y();r.trigger.cancel()};b.events.cancel.bind(c);b.events.response.bind(function(a){r.trigger.response(a.rtf(),h)});b.events.error.bind(function(a){y();r.trigger.error(a.message())});var k=l();k.setTitle(n("cement.dialog.flash.title"));var x=a(b,k.reflow,m);x.reset();k.setContent(x.element());k.events.close.bind(c);k.show();b.activate();var y=function(){p.unbind();k.destroy();b.destroy()}},events:r.registry}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Replication","ephox.sugar.api.Attr ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.InsertAll ephox.sugar.api.Remove ephox.sugar.api.Traverse".split(" "),function(a,d,c,b,e,f){var g=function(a){return d.fromDom(a.dom().cloneNode(!0))},l=function(b,c){var e=d.fromTag(c),f=a.clone(b);a.setAll(e,f);return e};return{shallow:function(a){return d.fromDom(a.dom().cloneNode(!1))},shallowAs:l,deep:g,copy:function(a,c){var d=l(a,c),
e=f.children(g(a));b.append(d,e);return d},mutate:function(a,d){var g=l(a,d);c.before(a,g);var s=f.children(a);b.append(g,s);e.remove(a);return g}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.limbo.api.RtfImage","ephox.perhaps.Option ephox.sugar.api.Attr ephox.sugar.api.Class ephox.sugar.api.Css ephox.sugar.api.Element ephox.sugar.api.Node ephox.sugar.api.PredicateFind ephox.sugar.api.Replication ephox.sugar.api.SelectorFilter ephox.violin.Strings".split(" "),
function(a,d,c,b,e,f,g,l,m,n){var r=function(a,b){var c=f.value(a),d=e.fromTag("div"),l=c.indexOf("]>");d.dom().innerHTML=c.substr(l+2);return g.descendant(d,function(a){return f.name(a)===b})},q=function(b){return f.isComment(b)?r(b,"v:shape"):a.none()},p=function(a){return m.descendants(a,".rtf-data-image")};return{local:function(b){if("img"===f.name(b)){var e=d.get(b,"src");if(void 0!==e&&null!==e&&n.startsWith(e,"file://"))return b=l.shallow(b),e=e.split(/[\/\\]/),d.set(b,"data-image-id",e[e.length-
1]),d.remove(b,"src"),d.set(b,"data-image-type","local"),c.add(b,"rtf-data-image"),a.some(b)}return a.none()},vshape:function(a){return q(a).map(function(a){var f=d.get(a,"o:spid"),f=void 0===f?d.get(a,"id"):f,g=e.fromTag("img");c.add(g,"rtf-data-image");d.set(g,"data-image-id",f.substr(7));d.set(g,"data-image-type","code");b.setAll(g,{width:b.get(a,"width"),height:b.get(a,"height")});return g})},find:p,exists:function(a){return 0<p(a).length},scour:q}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.flash.Flash","ephox.bowerbird.api.Rtf ephox.cement.flash.Correlation ephox.cement.flash.FlashDialog ephox.compass.Arr ephox.limbo.api.RtfImage ephox.porkbun.Event ephox.porkbun.Events ephox.sugar.api.Remove ephox.sugar.api.Traverse".split(" "),function(a,d,c,b,e,f,g,l,m){return function(n,r){var q=g.create({error:f(["message"]),insert:f(["elements","assets"])});return{events:q.registry,gordon:function(f,g){var p=function(b){var c=b.hide();
b=a.images(b.rtf());var l=e.find(f);d.convert(l,b,function(a){q.trigger.insert(m.children(f),a.concat(g));c()})},h=function(){var a=e.find(f);b.each(a,l.remove);q.trigger.insert(m.children(f),g)};if(r.allowLocalImages){var k=c(n,r);k.events.response.bind(p);k.events.cancel.bind(h);k.events.error.bind(function(a){q.trigger.error(a.message())});k.open()}else h(),q.trigger.error("errors.local.images.disallowed")}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.cement.smartpaste.MergeSettings","ephox.cement.style.Styles ephox.highway.Merger ephox.peanut.Fun ephox.porkbun.Event ephox.porkbun.Events ephox.sugar.api.Class ephox.sugar.api.Element ephox.sugar.api.Insert".split(" "),function(a,d,c,b,e,f,g,l){return function(m,n){var r=n.translations,q=function(a,b,c){c(d.merge(b,{mergeOfficeStyles:a,mergeHtmlStyles:a}))},p=e.create({open:b([]),cancel:b([]),close:b([])}),h=function(b,c){var d=g.fromTag("div");f.add(d,a.resolve("styles-dialog-content"));
var e=g.fromTag("p"),n=g.fromText(r("cement.dialog.paste.instructions"));l.append(e,n);l.append(d,e);var e={text:r("cement.dialog.paste.clean"),tabindex:0,className:a.resolve("clean-styles"),click:function(){k();q(!1,b,c)}},n={text:r("cement.dialog.paste.merge"),tabindex:1,className:a.resolve("merge-styles"),click:function(){k();q(!0,b,c)}},h=m(!0);h.setTitle(r("cement.dialog.paste.title"));h.setContent(d);h.setButtons([e,n]);h.show();var k=function(){p.trigger.close();h.destroy()};h.events.close.bind(function(){p.trigger.cancel();
k()});p.trigger.open()};return{events:p.registry,get:function(a,b){var c=n[a?"officeStyles":"htmlStyles"];"clean"===c?q(!1,n,b):"merge"===c?q(!0,n,b):h(n,b)},destroy:c.noop}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.smartpaste.Inspection",[],function(){return{isValidData:function(a){return void 0!==a&&void 0!==a.types&&null!==a.types}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.cement.html.Transform",["ephox.classify.Type","ephox.sugar.api.Attr"],function(a,d){return{rotateImage:function(c,b){var e=b.style;d.has(c,"width")&&d.has(c,"height")&&a.isString(e)&&(e=e.match(/rotation:([^;]*)/),null===e||"90"!==e[1]&&"-90"!==e[1]||d.setAll(c,{width:d.get(c,"height"),height:d.get(c,"width")}))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Comments",["ephox.fred.PlatformDetection","ephox.peanut.Fun",
"ephox.sugar.api.Element","global!document"],function(a,d,c,b){var e=function(a){for(var b=[];null!==a.nextNode();)b.push(c.fromDom(a.currentNode));return b},f=function(a){try{return e(a)}catch(b){return[]}};a=a.detect().browser;var g=a.isIE()||a.isSpartan()?f:e,l=d.constant(d.constant(!0));return{find:function(a,c){var d=c.fold(l,function(a){return function(b){return a(b.nodeValue)}});d.acceptNode=d;d=b.createTreeWalker(a.dom(),NodeFilter.SHOW_COMMENT,d,!1);return g(d)}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.alien.Comments",["ephox.perhaps.Option","ephox.sugar.api.Comments","ephox.violin.Strings","global!document"],function(a,d,c,b){return{find:function(b){return d.find(b,a.some(function(a){return c.startsWith(a,"[if gte vml 1]")}))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.perhaps.Options",["ephox.perhaps.Option"],function(a){return{cat:function(a){for(var c=
[],b=function(a){c.push(a)},e=0;e<a.length;e++)a[e].each(b);return c},findMap:function(d,c){for(var b=0;b<d.length;b++){var e=c(d[b],b);if(e.isSome())return e}return a.none()},liftN:function(d,c){for(var b=[],e=0;e<d.length;e++){var f=d[e];if(f.isSome())b.push(f.getOrDie());else return a.none()}return a.some(c.apply(null,b))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.images.ImageReference","ephox.cement.alien.Comments ephox.compass.Arr ephox.limbo.api.RtfImage ephox.perhaps.Option ephox.perhaps.Options ephox.scullion.Struct ephox.sugar.api.Attr ephox.sugar.api.Elements ephox.sugar.api.SelectorFilter global!console".split(" "),
function(a,d,c,b,e,f,g,l,m,n){var r=f.immutable("img","vshape"),q=function(a,c){var e=g.get(a,"v:shapes"),f=b.from(d.find(c,function(a){return g.get(a,"id")===e}));f.isNone()&&n.log("WARNING: unable to find data for image",a.dom());return f.map(function(b){var c=g.clone(a);c._rawElement=a.dom();var d=g.clone(b);d._rawElement=b.dom();return r(c,d)})};return{extract:function(b){var f=l.fromHtml(b);b=d.bind(f,function(a){return m.descendants(a,"img")});var f=d.bind(f,a.find),g=e.cat(d.map(f,c.scour));
b=d.map(b,function(a){return q(a,g)});return e.cat(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.photon.Reader",["ephox.perhaps.Option","ephox.sugar.api.Element"],function(a,d){var c=function(b){b=b.dom();try{var c=b.contentWindow?b.contentWindow.document:b.contentDocument;return void 0!==c&&null!==c?a.some(d.fromDom(c)):a.none()}catch(f){return console.log("Error reading iframe: ",b),console.log("Error was: "+f),a.none()}};return{doc:function(a){return c(a).fold(function(){return a},
function(a){return a})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.photon.Writer",["ephox.photon.Reader","ephox.sugar.api.Body"],function(a,d){return{write:function(c,b){if(!d.inBody(c))throw"Internal error: attempted to write to an iframe that is not in the DOM";var e=a.doc(c).dom();e.open();e.writeln(b);e.close()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Ready",
["ephox.sugar.api.DomEvent","ephox.sugar.api.Element","global!document"],function(a,d,c){return{execute:function(b){if("complete"===c.readyState||"interactive"===c.readyState)b();else var e=a.bind(d.fromDom(c),"DOMContentLoaded",function(){b();e.unbind()})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.keurig.loader.GWTLoader","ephox.fred.PlatformDetection ephox.oilspill.callback.Globaliser ephox.peanut.Fun ephox.peanut.Thunk ephox.perhaps.Option ephox.photon.Writer ephox.sugar.api.Body ephox.sugar.api.Css ephox.sugar.api.DomEvent ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.Ready ephox.sugar.api.Remove".split(" "),
function(a,d,c,b,e,f,g,l,m,n,r,q,p){var h=d.install("ephox.keurig.init"),k=e.none();return{load:a.detect().browser.isIE()?c.noop:b.cached(function(b){var c=n.fromTag("div");if(void 0===b)throw"baseUrl was undefined";var d=n.fromTag("iframe");l.setAll(c,{display:"none"});var A=m.bind(d,"load",function(){var g=h.ephemeral(function(b){k=e.some(b);a.detect().browser.isSafari()||p.remove(c)});f.write(d,'<script type="text/javascript" src="'+(b+"/wordimport.js")+'">\x3c/script><script type="text/javascript">function gwtInited () {parent.window.'+
g+"(com.ephox.keurig.WordCleaner.cleanDocument);};\x3c/script>");A.unbind()});q.execute(function(){r.append(g.body(),c);r.append(c,d)})}),cleanDocument:function(a,b){return k.map(function(c){return c(a,b)})},ready:function(){return k.isSome()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.keurig.api.WordCleaner",["ephox.keurig.loader.GWTLoader"],function(a){return function(d){a.ready()||a.load(d);return{cleanDocument:a.cleanDocument}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.photon.Sandbox","ephox.peanut.Fun ephox.photon.Writer ephox.sugar.api.Css ephox.sugar.api.DomEvent ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.Remove global!setTimeout".split(" "),function(a,d,c,b,e,f,g,l){return function(m){return{play:function(n,r,q){var p=e.fromTag("div"),h=e.fromTag("iframe");c.setAll(p,{display:"none"});var k=b.bind(h,"load",function(){d.write(h,n);var b=h.dom().contentWindow.document;
if(void 0===b)throw"sandbox iframe load event did not fire correctly";var c=e.fromDom(b),b=b.body;if(void 0===b)throw"sandbox iframe does not have a body";b=e.fromDom(b);c=r(c,b);k.unbind();g.remove(p);l(a.curry(q,c),0)});f.append(p,h);f.append(m,p)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.impl.NodeValue",["ephox.perhaps.Option","global!Error"],function(a,d){return function(c,b){var e=function(b){try{return c(b)?a.some(b.dom().nodeValue):
a.none()}catch(d){return a.none()}};return{get:function(a){if(!c(a))throw new d("Can only get "+b+" value of a "+b+" node");return e(a).getOr("")},getOption:e,set:function(a,e){if(!c(a))throw new d("Can only set raw "+b+" value of a "+b+" node");a.dom().nodeValue=e}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Comment",["ephox.sugar.api.Node","ephox.sugar.impl.NodeValue"],function(a,d){var c=d(a.isComment,"comment");return{get:function(a){return c.get(a)},
getOption:function(a){return c.getOption(a)},set:function(a,d){c.set(a,d)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Html",["ephox.sugar.api.Element","ephox.sugar.api.Insert"],function(a,d){var c=function(a){return a.dom().innerHTML};return{get:c,set:function(a,c){a.dom().innerHTML=c},getOuter:function(b){var e=a.fromTag("div");b=a.fromDom(b.dom().cloneNode(!0));d.append(e,b);return c(e)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.vogue.css.Set",["ephox.sugar.api.Insert"],function(a){return{setCss:function(d,c,b){d.dom().styleSheet?d.dom().styleSheet.cssText=c:a.append(d,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.vogue.util.Regex",[],function(){return{escape:function(a){return a.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);a.bolt.module.api.define("global!RegExp",
[],function(){return RegExp});(function(a,h,k){a("ephox.vogue.css.Url",["ephox.compass.Obj","ephox.vogue.util.Regex","global!RegExp"],function(a,d,c){var b=function(a,b,g){b=new c("url\\(\\s*['\"]?"+d.escape(b)+"(.*?)['\"]?\\s*\\)","g");return a.replace(b,'url("'+g+'$1")')};return{replace:b,replaceMany:function(c,d){var g=c;a.each(d,function(a,c){g=b(g,c,a)});return g}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.vogue.api.DocStyle","ephox.sugar.api.Attr ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.SelectorFind ephox.vogue.css.Set ephox.vogue.css.Url global!Array".split(" "),
function(a,d,c,b,e,f,g){return{stylesheets:function(a){a=a.dom().styleSheets;return g.prototype.slice.call(a)},inject:function(g,m,n){var r=d.fromTag("style",n.dom());a.set(r,"type","text/css");g=void 0===m?g:f.replaceMany(g,m);e.setCss(r,g,d.fromText(g,n.dom()));n=b.descendant(n,"head").getOrDie();c.append(n,r)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.vogue.css.Rules",["ephox.compass.Arr","ephox.scullion.Struct"],function(a,d){var c=
d.immutable("selector","style"),b=function(b){return a.map(b.cssRules,function(a){var b=a.selectorText;a=a.style.cssText;if(void 0===a)throw"WARNING: Browser does not support cssText property";return c(b,a)})};return{extract:b,extractAll:function(c){return a.bind(c,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.vogue.api.Rules",["ephox.vogue.css.Rules"],function(a){return{extract:function(d){return a.extract(d)},extractAll:function(d){return a.extractAll(d)}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.html.HtmlPaste","ephox.cement.html.Transform ephox.cement.images.ImageReference ephox.classify.Type ephox.compass.Arr ephox.keurig.api.WordCleaner ephox.peanut.Fun ephox.photon.Sandbox ephox.porkbun.Event ephox.porkbun.Events ephox.sugar.api.Attr ephox.sugar.api.Class ephox.sugar.api.Comment ephox.sugar.api.Element ephox.sugar.api.Elements ephox.sugar.api.Html ephox.sugar.api.Remove ephox.sugar.api.SelectorFilter ephox.sugar.api.Traverse ephox.vogue.api.DocStyle ephox.vogue.api.Rules global!document".split(" "),
function(a,d,c,b,e,f,g,l,m,n,r,q,p,h,k,v,x,y,A,z,B){var D=function(a){var b=1;return a.replace(/(<img[^>]*)src=".*?"/g,function(a,c,d){return c+'data-textbox-image="'+b++ +'"'})},G=function(a){a=y.children(a);b.each(a,function(a){q.getOption(a).each(function(b){"StartFragment"!==b&&"EndFragment"!==b||v.remove(a)})})},M=function(c,d){b.each(d,function(d){var e=n.get(d,"data-textbox-image");b.each(c,function(b){var c=b.img();b=b.vshape();c["data-textbox-image"]==e&&(c=b["o:spid"],c=void 0===c?b.id:
c,a.rotateImage(d,b),r.add(d,"rtf-data-image"),n.set(d,"data-image-id",c.substr(7)),n.set(d,"data-image-type","code"),n.remove(d,"data-textbox-image"))})})},C=function(a,c){var d=z.extractAll(c);b.each(d,function(c){var d=x.descendants(a,c.selector());b.each(d,function(a){n.remove(a,"class");n.set(a,"style",c.style())})})},K=function(a,b){var c=g(p.fromDom(B.body));return function(d,e){c.play(d,function(c,d){var e=x.descendants(d,"img");G(d);M(a,e);b&&C(d,A.stylesheets(c));return k.get(d)},e)}},E=
function(a){var b=a.indexOf("</html>");return-1<b?a.substr(0,b+7):a};return function(a,b){var c=e(b.baseUrl),g=m.create({paste:l(["elements","assets"]),error:l(["message"])});return{handler:function(b){var e=E(b);a.get(!0,function(a){a=a.mergeOfficeStyles;var b=D(e),f=d.extract(b),l=K(f,a);c.cleanDocument(b,a).fold(function(){g.trigger.error("errors.paste.word.notready")},function(a){void 0===a||null===a||0===a.length?g.trigger.paste([],[]):l(a,function(a){a=h.fromHtml(a);g.trigger.paste(a,[])})})});
return!0},isSupported:f.constant(!0),events:g.registry}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.images.ImagePaste","ephox.compass.Arr ephox.fred.PlatformDetection ephox.hermes.api.ImageAsset ephox.hermes.api.ImageExtract ephox.peanut.Fun ephox.porkbun.Event ephox.porkbun.Events ephox.sugar.api.Attr ephox.sugar.api.Element global!console".split(" "),function(a,d,c,b,e,f,g,l,m,n){return function(r){var q=d.detect(),p=!q.browser.isIE()&&
!q.browser.isFirefox(),h=g.create({paste:f(["elements","assets"]),error:f(["message"])}),k=function(b){return a.bind(b,function(a){return c.cata(a,function(a,b,c,d){a=m.fromTag("img");l.set(a,"src",c);return a},function(a,b,c){n.log("Internal error: Paste operation produced an image URL instead of a Data URI: ",b)})})},v=function(c){c=a.filter(c,function(a){return"file"===a.kind&&/image/.test(a.type)});c=a.map(c,function(a){return a.getAsFile()});b.toAssets(c,function(a){var b=k(a);h.trigger.paste(b,
a)});return!0},x=function(){h.trigger.error("safari.imagepaste");return!0},y=function(){h.trigger.error("errors.local.images.disallowed");return!0},q=q.browser.isSafari()?x:v;return{handler:r.allowLocalImages?q:y,isSupported:e.constant(p),events:h.registry}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.api.CementConstants",["ephox.cement.style.Styles","ephox.peanut.Fun"],function(a,d){var c=a.resolve("smartpaste-eph-bin");return{binStyle:d.constant(c)}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.knoch.core.Bounce",["global!Array"],function(a){return{bounce:function(d){return function(){var c=a.prototype.slice.call(arguments),b=this;setTimeout(function(){d.apply(b,c)},0)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.knoch.core.FutureOps",[],function(){return function(a,d){return{get:d,map:function(c){return a(function(a){d(function(d){a(c(d))})})},bind:function(c){return a(function(a){d(function(d){c(d).get(a)})})},
anonBind:function(c){return a(function(a){d(function(d){c.get(a)})})}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.knoch.future.Future",["ephox.compass.Arr","ephox.knoch.core.Bounce","ephox.knoch.core.FutureOps"],function(a,d,c){var b=function(a){return c(b,function(b){a(d.bounce(b))})},e=function(c){return b(function(b){var d=[],e=0,n=function(a){return function(n){d[a]=n;e++;e>=c.length&&b(d)}};0===c.length?b([]):a.each(c,function(a,
b){a.get(n(b))})})};return{nu:b,par:e,mapM:function(b,c){return e(a.map(b,c))},lift2:function(a,c,d){return b(function(b){var e=!1,s=!1,q=void 0,p=void 0,h=function(){if(e&&s){var a=d(q,p);b(a)}};a.get(function(a){q=a;e=!0;h()});c.get(function(a){p=a;s=!0;h()})})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.knoch.future.CachedFuture","ephox.compass.Arr ephox.highway.Merger ephox.knoch.core.Bounce ephox.knoch.core.FutureOps ephox.knoch.future.Future ephox.peanut.Fun ephox.perhaps.Option".split(" "),
function(a,d,c,b,e,f,g){var l=function(f){var n=g.none(),r=[],q=function(){return n.isSome()},p=function(a){n.each(function(b){c.bounce(a)(b)})};e.nu(f).get(function(b){n=g.some(b);a.each(r,p);r=[]});f=b(l,function(a){q()?p(a):r.push(a)});return d.merge(f,{isSet:q})};return{nu:l}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.pastiche.IeBlob","ephox.compass.Arr ephox.epithet.Resolve ephox.hermes.api.ImageExtract ephox.knoch.future.CachedFuture ephox.peanut.Fun ephox.perhaps.Option".split(" "),
function(a,d,c,b,e,f){return{convert:function(g){var l=d.resolve("window.clipboardData.files"),m=void 0!==g.convertURL?g.convertURL:void 0!==g.msConvertURL?g.msConvertURL:void 0;if(void 0!==l&&void 0!==m&&0<l.length){var n=a.map(l,function(a){var b=c.blob(a);m.apply(g,[a,"specified",b.objurl()]);return b}),l=b.nu(function(a){c.fromBlobs(n,a)});l.get(e.noop);return f.some(l)}return f.none()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fussy.api.Situ",
[],function(){var a=function(a){return{fold:a}};return{on:function(d,c){return a(function(a,e,f){return e(d,c)})},before:function(d){return a(function(a,b,e){return a(d)})},after:function(d){return a(function(a,b,e){return e(d)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fussy.api.SelectionRange",["ephox.fussy.api.Situ","ephox.scullion.Struct","ephox.sugar.api.Element"],function(a,d,c){var b=d.immutable("start","soffset","finish",
"foffset"),e=d.immutable("start","soffset","finish","foffset"),f=d.immutable("start","finish");return{read:b,general:e,write:f,writeFromNative:function(b){var d=c.fromDom(b.startContainer),e=c.fromDom(b.endContainer);return f(a.on(d,b.startOffset),a.on(e,b.endOffset))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fussy.api.Supported",[],function(){return{run:function(a,d){if(a.getSelection)return d(a,a.getSelection());throw"No selection model supported.";
}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.DocumentPosition",["ephox.sugar.api.Compare","ephox.sugar.api.Traverse"],function(a,d){return{after:function(c,b,e,f){var g=d.owner(c).dom().createRange();g.setStart(c.dom(),b);g.setEnd(e.dom(),f);c=a.eq(c,e)&&b===f;return g.collapsed&&!c}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fussy.wwwc.Directions",["ephox.fussy.api.SelectionRange",
"ephox.sugar.api.DocumentPosition","ephox.sugar.api.Element","ephox.sugar.api.Traverse"],function(a,d,c,b){var e=function(a){return d.after(c.fromDom(a.anchorNode),a.anchorOffset,c.fromDom(a.focusNode),a.focusOffset)},f=function(b,d){var f=c.fromDom(d.startContainer),g=c.fromDom(d.endContainer);return e(b)?a.read(g,d.endOffset,f,d.startOffset):a.read(f,d.startOffset,g,d.endOffset)},g=function(a){return e(a)},l=function(a,c,d,e){return function(f){if(f.extend)f.collapse(a.dom(),c),f.extend(d.dom(),
e);else{var g=b.owner(a).dom().createRange();g.setStart(d.dom(),e);g.setEnd(a.dom(),c);f.removeAllRanges();f.addRange(g)}}},m=function(a,b,c,e){return d.after(a,b,c,e)};return{read:function(){return{flip:f,isRtl:g}},write:function(){return{flip:l,isRtl:m}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fussy.wwwc.DomRange",["ephox.fussy.api.SelectionRange","ephox.fussy.wwwc.Directions","ephox.perhaps.Option","ephox.sugar.api.DocumentPosition",
"ephox.sugar.api.Element"],function(a,d,c,b,e){var f=function(b,d){if(!0===g(b,d.start(),d.finish()).collapsed){var f=g(b,d.finish(),d.start());return!0===f.collapsed?c.none():c.some(a.general(e.fromDom(f.endContainer),f.endOffset,e.fromDom(f.startContainer),f.startOffset))}return c.none()},g=function(a,b,c){var d=n(a);b.fold(function(a){d.setStartBefore(a.dom())},function(a,b){d.setStart(a.dom(),b)},function(a){d.setStartAfter(a.dom())});c.fold(function(a){d.setEndBefore(a.dom())},function(a,b){d.setEnd(a.dom(),
b)},function(a){d.setEndAfter(a.dom())});return d},l=function(a,b){return g(a,b.start(),b.finish())},m=function(a,b){var c=l(a,b);return function(a){a.addRange(c)}},n=function(a){return a.document.createRange()};return{create:n,build:function(a,b){return f(a,b).fold(function(){return m(a,b)},function(a){return d.write().flip(a.start(),a.soffset(),a.finish(),a.foffset())})},toNative:l,forceRange:function(a,b){var c=g(a,b.start(),b.finish());return!0===c.collapsed?g(a,b.finish(),b.start()):c},toExactNative:function(a,
c,d,e,f){var g=b.after(c,d,e,f);a=a.document.createRange();g?(a.setStart(e.dom(),f),a.setEnd(c.dom(),d)):(a.setStart(c.dom(),d),a.setEnd(e.dom(),f));return a}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fussy.search.Within","ephox.compass.Arr ephox.fussy.wwwc.DomRange ephox.sugar.api.Element ephox.sugar.api.Node ephox.sugar.api.SelectorFilter ephox.sugar.api.Selectors".split(" "),function(a,d,c,b,e,f){var g=function(b,c,d,g){var q=b.document.createRange();
b=(f.is(c,g)?[c]:[]).concat(e.descendants(c,g));return a.filter(b,function(a){q.selectNodeContents(a.dom());return 1>q.compareBoundaryPoints(d.END_TO_START,d)&&-1<q.compareBoundaryPoints(d.START_TO_END,d)})};return{find:function(a,e,f){e=d.forceRange(a,e);var s=c.fromDom(e.commonAncestorContainer);return b.isElement(s)?g(a,s,e,f):[]}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fussy.wwwc.Prefilter",["ephox.fussy.api.SelectionRange","ephox.fussy.api.Situ",
"ephox.sugar.api.Node"],function(a,d,c){var b=function(a,b){return"br"===c.name(a)?d.before(a):d.on(a,b)};return{preprocess:function(c){var f=c.start().fold(d.before,b,d.after);c=c.finish().fold(d.before,b,d.after);return a.write(f,c)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Fragment",["ephox.compass.Arr","ephox.sugar.api.Element","global!document"],function(a,d,c){return{fromElements:function(b,e){var f=(e||c).createDocumentFragment();
a.each(b,function(a){f.appendChild(a.dom())});return d.fromDom(f)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.fussy.wwwc.WwwcModel","ephox.fussy.api.SelectionRange ephox.fussy.wwwc.Directions ephox.fussy.wwwc.DomRange ephox.fussy.wwwc.Prefilter ephox.perhaps.Option ephox.sugar.api.Element ephox.sugar.api.Fragment".split(" "),function(a,d,c,b,e,f,g){var l=function(a,b){var c=b.getRangeAt(0),d=b.getRangeAt(b.rangeCount-1),e=a.document.createRange();
e.setStart(c.startContainer,c.startOffset);e.setEnd(d.endContainer,d.endOffset);return e},m=function(a,b){return void 0!==b&&null!==b&&0<b.rangeCount?e.from(l(a,b)):e.none()};return{get:function(b,c){return m(b,c).map(function(b){var e=f.fromDom(b.startContainer),g=f.fromDom(b.endContainer);return d.read().isRtl(c)?d.read().flip(c,b):a.read(e,b.startOffset,g,b.endOffset)})},set:function(a){return function(d,e){var f=b.preprocess(a),f=c.build(d,f);void 0!==e&&null!==e&&(e.removeAllRanges(),f(e))}},
selectElementContents:function(a){return function(b,d){var e=c.create(b);e.selectNodeContents(a.dom());d.removeAllRanges();d.addRange(e)}},replace:function(a){return function(b,c){m(b,c).each(function(c){var d=g.fromElements(a,b.document);c.deleteContents();c.insertNode(d.dom())})}},replaceRange:function(a,d){return function(e,f){var l=b.preprocess(a),l=c.toNative(e,l),m=g.fromElements(d,e.document);l.deleteContents();l.insertNode(m.dom())}},deleteRange:function(a,b,d,e){return function(f,g){c.toExactNative(f,
a,b,d,e).deleteContents()}},cloneFragment:function(a,b,d,e){return function(g,l){var m=c.toExactNative(g,a,b,d,e).cloneContents();return f.fromDom(m)}},rectangleAt:function(a,b,d,f){return function(g,l){var m=c.toExactNative(g,a,b,d,f),s=m.getClientRects(),m=0<s.length?s[0]:m.getBoundingClientRect();return 0<m.width||0<m.height?e.some(m):e.none()}},clearSelection:function(a,b){a.getSelection().removeAllRanges()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.fussy.api.WindowSelection","ephox.fussy.api.SelectionRange ephox.fussy.api.Situ ephox.fussy.api.Supported ephox.fussy.search.Within ephox.fussy.wwwc.DomRange ephox.fussy.wwwc.WwwcModel ephox.sugar.api.Compare ephox.sugar.api.Element".split(" "),function(a,d,c,b,e,f,g,l){var m=function(a,b){c.run(a,f.set(b))},n=function(a,c,d){return b.find(a,c,d)};return{get:function(a){return c.run(a,f.get)},set:m,setExact:function(b,c,e,f,g){c=a.write(d.on(c,e),d.on(f,g));m(b,c)},selectElementContents:function(a,
b){c.run(a,f.selectElementContents(b))},replace:function(a,b){c.run(a,f.replace(b))},replaceRange:function(a,b,d){c.run(a,f.replaceRange(b,d))},deleteRange:function(a,b,d,e,g){c.run(a,f.deleteRange(b,d,e,g))},isCollapsed:function(a,b,c,d){return g.eq(a,c)&&b===d},cloneFragment:function(a,b,d,e,g){return c.run(a,f.cloneFragment(b,d,e,g))},rectangleAt:function(a,b,d,e,g){return c.run(a,f.rectangleAt(b,d,e,g))},findWithin:n,findWithinExact:function(b,c,e,f,g,l){c=a.write(d.on(c,e),d.on(f,g));return n(b,
c,l)},deriveExact:function(b,c){var d=e.forceRange(b,c);return a.general(l.fromDom(d.startContainer),d.startOffset,l.fromDom(d.endContainer),d.endOffset)},clearAll:function(a){c.run(a,f.clearSelection)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.api.HtmlPatterns",[],function(){return{validStyles:function(){return/^(mso-.*|tab-stops|tab-interval|language|text-underline|text-effect|text-line-through|font-color|horiz-align|list-image-[0-9]+|separator-image|table-border-color-(dark|light)|vert-align|vnd\..*)$/},
specialInline:function(){return/^(font|em|strong|samp|acronym|cite|code|dfn|kbd|tt|b|i|u|s|sub|sup|ins|del|var|span)$/}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.violin.StringMatch",[],function(){var a=function(a){return{fold:a,matches:function(c){return a(function(a){return 0===c.toLowerCase().indexOf(a.toLowerCase())},function(a){return a.test(c.toLowerCase())},function(a){return 0<=c.toLowerCase().indexOf(a.toLowerCase())},function(a){return c.toLowerCase()===
a.toLowerCase()},function(){return!0},function(a){return!a.matches(c)})}}};return{starts:function(d){return a(function(a,b,e,f,g,l){return a(d)})},pattern:function(d){return a(function(a,b,e,f,g,l){return b(d)})},contains:function(d){return a(function(a,b,e,f,g,l){return e(d)})},exact:function(d){return a(function(a,b,e,f,g,l){return f(d)})},all:function(){return a(function(a,c,b,e,f,g){return f()})},not:function(d){return a(function(a,b,e,f,g,l){return l(d)})},cata:function(a,c,b,e,f,g,l){return a.fold(c,
b,e,f,g,l)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.api.RuleMatch",["ephox.peanut.Fun","ephox.sugar.api.Node","ephox.violin.StringMatch"],function(a,d,c){return{keyval:function(b,d,f,g){var l=g.name,m=void 0!==g.condition?g.condition:a.constant(!0);g=void 0!==g.value?g.value:c.all();return l.matches(f)&&g.matches(d)&&m(b)},name:function(b,c){var f=d.name(b),g=c.name,l=void 0!==c.condition?c.condition:a.constant(!0);return g.matches(f)&&
l(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.cleanup.AttributeAccess",["ephox.compass.Arr","ephox.compass.Obj","ephox.peanut.Fun","ephox.sugar.api.Attr"],function(a,d,c,b){var e=function(c,e,l){a.each(e,function(a){b.remove(c,a)});d.each(l,function(a,d){b.set(c,d,a)})};return{filter:function(b,c){var d={};a.each(b.dom().attributes,function(a){c(a.value,a.name)||(d[a.name]=a.value)});return d},clobber:function(b,c,l){l=a.map(b.dom().attributes,
function(a){return a.name});d.size(c)!==l.length&&e(b,l,c)},scan:c.constant({})}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.cleanup.StyleAccess",["ephox.compass.Arr","ephox.compass.Obj","ephox.sugar.api.Attr","ephox.sugar.api.Css","ephox.violin.Strings"],function(a,d,c,b,e){var f=function(b){var c={};b=void 0!==b&&null!==b?b.split(";"):[];a.each(b,function(a){a=a.split(":");2===a.length&&(c[e.trim(a[0])]=e.trim(a[1]))});return c},
g=function(b){var c=d.keys(b);return a.map(c,function(a){return a+": "+b[a]}).join("; ")};return{filter:function(b,c){var d={};a.each(b.dom().style,function(a){var e;e=b.dom().style.getPropertyValue(a);c(e,a)||(d[a]=e)});return d},clobber:function(a,e,f){c.set(a,"style","");var s=d.size(e),h=d.size(f);0===s&&0===h?c.remove(a,"style"):0===s?c.set(a,"style",g(f)):(d.each(e,function(c,d){b.set(a,d,c)}),e=c.get(a,"style"),f=0<h?g(f)+"; ":"",c.set(a,"style",f+e))},scan:function(b,c,d){b=b.dom().getAttribute("style");
var e=f(b),g={};a.each(c,function(a){var b=e[a];void 0===b||d(b,a)||(g[a]=b)});return g}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.cleanup.Cleaners",["ephox.pastiche.cleanup.AttributeAccess","ephox.pastiche.cleanup.StyleAccess","ephox.peanut.Fun","ephox.sugar.api.Element"],function(a,d,c,b){var e=["mso-list"],f=function(a,b){var c=d.scan(a,e,b),f=d.filter(a,b);d.clobber(a,f,c)},g=function(b,c){var d=a.filter(b,c);a.clobber(b,
d,{})};return{style:f,attribute:g,styleDom:function(a,c){f(b.fromDom(a),c)},attributeDom:function(a,c){g(b.fromDom(a),c)},validateStyles:function(a){var b=d.filter(a,c.constant(!1));d.clobber(a,b,{})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Classes",["ephox.compass.Arr","ephox.sugar.api.Class","global!Array"],function(a,d,c){return{add:function(b,c){a.each(c,function(a){d.add(b,a)})},remove:function(b,c){a.each(c,function(a){d.remove(b,
a)})},toggle:function(b,c){a.each(c,function(a){d.toggle(b,a)})},hasAll:function(b,c){return a.forall(c,function(a){return d.has(b,a)})},hasAny:function(b,c){return a.exists(c,function(a){return d.has(b,a)})},get:function(a){a=a.dom().classList;for(var d=new c(a.length),f=0;f<a.length;f++)d[f]=a.item(f);return d}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.engine.Pipeless","ephox.compass.Arr ephox.highway.Merger ephox.pastiche.api.RuleMatch ephox.pastiche.cleanup.Cleaners ephox.peanut.Fun ephox.sugar.api.Attr ephox.sugar.api.Class ephox.sugar.api.Classes ephox.sugar.api.Remove ephox.sugar.api.SelectorFilter".split(" "),
function(a,d,c,b,e,f,g,l,m,n){var h=function(b,d,e){b(e,function(b,f){return a.exists(d,function(a){return c.keyval(e,b,f,a)})})};return{remover:function(k,p){var t=d.merge({styles:[],attributes:[],classes:[],tags:[]},p),u=n.descendants(k,"*");a.each(u,function(c){h(b.style,t.styles,c);h(b.attribute,t.attributes,c);a.each(t.classes,function(b){var d=f.has(c,"class")?l.get(c):[];a.each(d,function(a){b.name.matches(a)&&g.remove(c,a)})})});u=n.descendants(k,"*");a.each(u,function(b){a.exists(t.tags,
e.curry(c.name,b))&&m.remove(b)})},unwrapper:function(b,f){var g=d.merge({tags:[]},f),l=n.descendants(b,"*");a.each(l,function(b){a.exists(g.tags,e.curry(c.name,b))&&m.unwrap(b)})},transformer:function(b,f){var g=d.merge({tags:[]},f),l=n.descendants(b,"*");a.each(l,function(b){var d=a.find(g.tags,e.curry(c.name,b));void 0!==d&&null!==d&&d.mutate(b)})},validator:function(c){c=n.descendants(c,"*");a.each(c,function(a){b.validateStyles(a)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.pastiche.engine.Token",["ephox.compass.Obj","ephox.sugar.api.Css","ephox.sugar.api.Element"],function(a,d,c){var b=function(a,b,e){var m,n,s,h=c.fromDom(a);switch(a.nodeType){case 1:b?m="endElement":(m="startElement",d.setAll(h,e||{}));n="HTML"!==a.scopeName&&a.scopeName&&a.tagName&&0>=a.tagName.indexOf(":")?(a.scopeName+":"+a.tagName).toUpperCase():a.tagName;break;case 3:m="text";s=a.nodeValue;break;case 8:m="comment";s=a.nodeValue;break;default:console.log("WARNING: Unsupported node type encountered: "+
a.nodeType)}return{getNode:function(){return a},tag:function(){return n},type:function(){return m},text:function(){return s}}},e=function(a,c){return b(c.createElement(a),!0)};return{START_ELEMENT_TYPE:"startElement",END_ELEMENT_TYPE:"endElement",TEXT_TYPE:"text",COMMENT_TYPE:"comment",FINISHED:e("HTML",window.document),token:b,createStartElement:function(c,d,e,m){var n=m.createElement(c);a.each(d,function(a,b){n.setAttribute(b,a)});return b(n,!1,e)},createEndElement:e,createComment:function(a,c){return b(c.createComment(a),
!1)},createText:function(a,c){return b(c.createTextNode(a))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.engine.Serialiser",["ephox.pastiche.engine.Token"],function(a){return{create:function(d){var c=d.createDocumentFragment();return{dom:c,receive:function(b){var e=function(a){a=a.getNode().cloneNode(!1);c.appendChild(a);c=a},f=function(a,b){var e=d.createTextNode(a.text());c.appendChild(e)};switch(b.type()){case a.START_ELEMENT_TYPE:e(b);
break;case a.TEXT_TYPE:f(b);break;case a.END_ELEMENT_TYPE:c=c.parentNode;break;case a.COMMENT_TYPE:break;default:throw{message:"Unsupported token type: "+b.type()};}},label:"SERIALISER"}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.engine.Tokeniser",["ephox.pastiche.engine.Token"],function(a){return{tokenise:function(d,c){var b;c=c||window.document;b=c.createElement("div");c.body.appendChild(b);b.style.position="absolute";b.style.left=
"-10000px";b.innerHTML=d;nextNode=b.firstChild||a.FINISHED;var e=[];endNode=!1;return{hasNext:function(){return void 0!==nextNode},next:function(){var d=nextNode,g=endNode;!endNode&&nextNode.firstChild?(e.push(nextNode),nextNode=nextNode.firstChild):endNode||1!==nextNode.nodeType?nextNode.nextSibling?(nextNode=nextNode.nextSibling,endNode=!1):(nextNode=e.pop(),endNode=!0):endNode=!0;d===a.FINISHED||nextNode||(c.body.removeChild(b),nextNode=a.FINISHED);d=d===a.FINISHED?d:d?a.token(d,g):void 0;return d}}}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.engine.Pipeline",["ephox.pastiche.engine.Serialiser","ephox.pastiche.engine.Tokeniser"],function(a,d){var c=function(a,c,d){var g=d;for(d=c.length-1;0<=d;d--)g=c[d](g,{},a);return g};return{build:c,run:function(b,e,f){var g=a.create(b);e=d.tokenise(e,b);for(b=c(b,f,g);e.hasNext();)f=e.next(),b.receive(f);return g.dom}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.pastiche.api.HybridAction","ephox.compass.Arr ephox.pastiche.engine.Pipeless ephox.pastiche.engine.Pipeline ephox.sugar.api.Element ephox.sugar.api.Html ephox.sugar.api.Remove ephox.sugar.api.Traverse".split(" "),function(a,d,c,b,e,f,g){return{removal:function(a){return function(b){d.remover(b,a)}},unwrapper:function(a){return function(b){d.unwrapper(b,a)}},transformer:function(a){return function(b){d.transformer(b,a)}},validate:function(){return function(a){d.validator(a)}},pipeline:function(a){return function(b){var d=
e.get(b),s=g.owner(b),d=c.run(s.dom(),d,a);f.empty(b);b.dom().appendChild(d)}},isWordContent:function(a){return 0<=a.indexOf("<o:p>")||0<=a.indexOf("p.MsoNormal, li.MsoNormal, div.MsoNormal")||0<=a.indexOf("MsoListParagraphCxSpFirst")||0<=a.indexOf("<w:WordDocument>")},go:function(c,d,f){var g=b.fromTag("div",c.dom());g.dom().innerHTML=d;a.each(f,function(a){a(g)});return e.get(g)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.api.RuleConditions",
["ephox.compass.Arr","ephox.sugar.api.Attr","ephox.sugar.api.Html","ephox.sugar.api.Node","ephox.sugar.api.PredicateExists"],function(a,d,c,b,e){var f=function(a){var c=a.dom().attributes,c=void 0!==c&&null!==c&&0<c.length;return"span"===b.name(a)?c:!0},g=function(a){return void 0===a.dom().attributes||null===a.dom().attributes?!0:0===a.dom().attributes.length||1===a.dom().attributes.length&&"style"===a.dom().attributes[0].name};return{isNotImage:function(a){return"img"!==b.name(a)},hasContent:function(c){return g(c)?
f(c)&&e.descendant(c,function(c){var d=!g(c),e=!a.contains("font em strong samp acronym cite code dfn kbd tt b i u s sub sup ins del var span".split(" "),b.name(c));return b.isText(c)||d||e}):!0},isList:function(a){return"ol"===b.name(a)||"ul"===b.name(a)},isLocal:function(a){a=d.get(a,"src");return/^file:/.test(a)},hasNoAttributes:g,isEmpty:function(a){return 0===c.get(a).length}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.api.RuleMutations",
"ephox.compass.Arr ephox.compass.Obj ephox.sugar.api.Attr ephox.sugar.api.Css ephox.sugar.api.Element ephox.sugar.api.Html ephox.sugar.api.Insert ephox.sugar.api.InsertAll ephox.sugar.api.Node ephox.sugar.api.Remove ephox.sugar.api.Traverse".split(" "),function(a,d,c,b,e,f,g,l,m,n,h){var k=function(b,c){var d=e.fromTag(b);g.before(c,d);var f=c.dom().attributes;a.each(f,function(a){d.dom().setAttribute(a.name,a.value)});f=h.children(c);l.append(d,f);n.remove(c);return d};return{changeTag:k,addBrTag:function(a){0===
f.get(a).length&&g.append(a,e.fromTag("br"))},properlyNest:function(c){h.parent(c).each(function(d){d=m.name(d);a.contains(["ol","ul"],d)&&(d=e.fromTag("li"),b.set(d,"list-style-type","none"),g.wrap(c,d))})},fontToSpan:function(a){var e=k("span",a),f={"font-size":{1:"8pt",2:"10pt",3:"12pt",4:"14pt",5:"18pt",6:"24pt",7:"36pt"}};d.each({face:"font-family",size:"font-size",color:"font-color"},function(a,d){if(c.has(e,d)){var g=c.get(e,d);b.set(e,a,void 0!==f[a]&&void 0!==f[a][g]?f[a][g]:g);c.remove(e,
d)}})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.engine.Filter",["ephox.compass.Arr","ephox.pastiche.engine.Token"],function(a,d){return{createFilter:function(c,b,e){return function(f,g,l,m){var n,h,k,p=!1,t=function(){b&&b(z);p=!1;h=[];k=[]},u=function(b){a.each(b,function(a){f.receive(a)})},v=function(a){p?k.push(a):f.receive(a)},x=function(){y();u(k);t()},y=function(){a.each(n||[],function(a){v(a)});A()},A=function(){n=
[]},z={document:l||window.document,settings:g||{},emit:v,receive:function(a){b&&h.push(a);c(z,a);a===d.FINISHED&&x()},startTransaction:function(){p=!0},rollback:function(){u(h);t()},commit:x,defer:function(a){n=n||[];n.push(a)},hasDeferred:function(){return n&&0<n.length},emitDeferred:y,dropDeferred:A,label:e};t();return z}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.engine.TokenUtil","ephox.pastiche.cleanup.StyleAccess ephox.pastiche.engine.Token ephox.peanut.Fun ephox.sugar.api.Attr ephox.sugar.api.Css ephox.sugar.api.Element".split(" "),
function(a,d,c,b,e,f){return{getAttribute:function(a,c){var d=f.fromDom(a.getNode());return b.get(d,c)},getStyle:function(a,b){var c=f.fromDom(a.getNode());return e.get(c,b)},isWhitespace:function(a){return a.type()===d.TEXT_TYPE&&/^[\s\u00A0]*$/.test(a.text())},getMsoList:function(b){b=f.fromDom(b.getNode());return a.scan(b,["mso-list"],c.constant(!1))["mso-list"]}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.detect.ListSymbols",
["ephox.compass.Arr","ephox.highway.Merger"],function(a,d){var c=[{regex:/^\(?[dc][\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[DC][\.\)]$/,type:{tag:"OL",type:"upper-alpha"}},{regex:/^\(?M*(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})[\.\)]$/,type:{tag:"OL",type:"upper-roman"}},{regex:/^\(?m*(cm|cd|d?c{0,3})(xc|xl|l?x{0,3})(ix|iv|v?i{0,3})[\.\)]$/,type:{tag:"OL",type:"lower-roman"}},{regex:/^\(?[0-9]+[\.\)]$/,type:{tag:"OL"}},{regex:/^([0-9]+\.)*[0-9]+\.?$/,type:{tag:"OL",variant:"outline"}},
{regex:/^\(?[a-z]+[\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[A-Z]+[\.\)]$/,type:{tag:"OL",type:"upper-alpha"}}],b={"\u2022":{tag:"UL",type:"disc"},"\u00b7":{tag:"UL",type:"disc"},"\u00a7":{tag:"UL",type:"square"}},e={o:{tag:"UL",type:"circle"},"-":{tag:"UL",type:"disc"},"\u25cf":{tag:"UL",type:"disc"},"\ufffd":{tag:"UL",type:"circle"}},f=function(a,b){return void 0!==a.variant?a.variant:"("===b.charAt(0)?"()":")"===b.charAt(b.length-1)?")":"."},g=function(a){a=parseInt(a,10);return isNaN(a)?
{}:{start:a}};return{match:function(l,m){var n=e[l]?[e[l]]:[],h=m&&b[l]?[b[l]]:m?[{tag:"UL",variant:l}]:[],k=a.bind(c,function(a){return a.regex.test(l)?[d.merge(a.type,g(l),{variant:f(a.type,l)})]:[]}),n=n.concat(h).concat(k);return a.map(n,function(a){return void 0!==a.variant?a:d.merge(a,{variant:l})})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.detect.ListGuess",["ephox.compass.Arr","ephox.pastiche.list.detect.ListSymbols",
"ephox.perhaps.Option","ephox.violin.Strings"],function(a,d,c,b){var e=function(a,b,c){return a===b||a&&b&&a.tag===b.tag&&a.type===b.type&&(c||a.variant===b.variant)};return{guess:function(f,g){var l=f?b.trim(f.text):"",l=d.match(l,f?!!f.symbolFont:!1),m=a.find(l,function(a){return"UL"===a.tag||g&&e(a,g,!0)});return void 0!==m?c.some(m):0<l.length?c.some(l[0]):c.none()},eqListType:e}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.detect.ListTypes",
["ephox.pastiche.engine.Token","ephox.pastiche.engine.TokenUtil","ephox.pastiche.list.detect.ListGuess"],function(a,d,c){return{guess:function(a,e,f){return c.guess(a,e).fold(function(){return null},function(a){if("OL"===a.tag&&f&&("P"!==f.tag()||/^MsoHeading/.test(d.getAttribute(f,"class"))))listType=null;else return a})},eqListType:c.eqListType,checkFont:function(b,c){b.type()==a.START_ELEMENT_TYPE&&((font=d.getStyle(b,"font-family"))?c="Wingdings"===font||"Symbol"===font:/^(P|H[1-6]|DIV)$/.test(b.tag())&&
(c=!1));return c}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Microsoft",["ephox.pastiche.engine.Token","ephox.pastiche.engine.TokenUtil"],function(a,d){return{isList:function(a,b){var e=d.getMsoList(b);return e&&"skip"!==e},isIgnore:function(c,b){return b.type()==a.START_ELEMENT_TYPE&&"Ignore"===d.getMsoList(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Tags",
["ephox.compass.Arr","ephox.pastiche.engine.Token","ephox.violin.Strings"],function(a,d,c){var b=function(a,b){return b.type()===d.START_ELEMENT_TYPE},e=function(a,b){return b.type()===d.END_ELEMENT_TYPE},f=function(a){return function(b,c){return c.tag()===a}};return{isStart:b,isEnd:e,isTag:f,isStartOf:function(a){return function(c,d){return b(c,d)&&d.tag()===a}},isEndOf:function(a){return function(b,c){return e(b,c)&&c.tag()===a}},isStartAny:function(c){return function(d,e){return b(d,e)&&a.contains(c,
e.tag())}},isEndAny:function(b){return function(b,c,d){return e(b,c)&&a.contains(d,c.tag())}},isWhitespace:function(a){return function(b,d){return f(a)(b,d)&&""===c.trim(d.getNode().textContent)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Paragraphs",["ephox.pastiche.inspect.Microsoft","ephox.pastiche.inspect.Tags"],function(a,d){return{isNormal:function(c,b){return!c.skippedPara.get()&&d.isStart(c,b,"P")&&!a.isList(c,
b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Texts",["ephox.pastiche.engine.Token","ephox.pastiche.engine.TokenUtil","ephox.violin.Strings"],function(a,d,c){var b=function(b,c){return c.type()===a.TEXT_TYPE};return{isWhitespace:function(a,c){return b(a,c)&&d.isWhitespace(c)},is:b,isBlank:function(a,d){return b(a,d)&&""===c.trim(d.text())},eq:function(a){return function(c,d){return b(c,d)&&d.text()===a}},matches:function(a){return function(c,
d){return b(c,d)&&a.test(d.text())}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.Handler",["ephox.peanut.Fun"],function(a){return function(d,c,b){return{pred:d,action:c,label:a.constant(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.Handlers",["ephox.compass.Arr","ephox.peanut.Fun","ephox.perhaps.Option"],function(a,d,c){var b=function(a,
b){return function(a,c,d){return b(a,c,d)}};return function(e,f,g){var l=b(e+" :: FALLBACK --- ",g);g=function(g,n,h){c.from(a.find(f,function(a){return a.pred(n,h)})).fold(d.constant(l),function(a){var c=a.label();return void 0===c?a.action:b(e+" :: "+c,a.action)})(g,n,h)};g.toString=function(){return"Handlers for "+e};return g}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.state.Transitions",[],function(){var a=function(a,
b){if(void 0===a||void 0===b)throw console.trace(),"brick";a.nextFilter.set(b)},d=function(a,b,d){b.nextFilter.get()(a,b,d)};return{next:a,go:d,jump:function(c){return function(b,e,f){a(e,c);d(b,e,f)}},isNext:function(a,b){return a.nextFilter.get()===b},setNext:function(c){return function(b,d,f){a(d,c)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.AfterListState",["ephox.pastiche.inspect.Paragraphs","ephox.pastiche.inspect.Texts",
"ephox.pastiche.list.stage.Handler","ephox.pastiche.list.stage.Handlers","ephox.pastiche.list.state.Transitions"],function(a,d,c,b,e){return{run:function(f,g){return b("AfterListState",[c(d.isBlank,function(a,b,c){a.defer(c)},"blank text"),c(a.isNormal,function(a,b,c){b.openedTag.set(c);a.defer(c);e.next(b,f)},"normal paragraph")],function(a,b,c){g(a,b,c)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.States",["ephox.pastiche.engine.Token"],
function(a){return{isCloser:function(d,c){return c.type()===a.END_ELEMENT_TYPE&&d.originalToken.get()&&c.tag()===d.originalToken.get().tag()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.AfterNoBulletListState",["ephox.pastiche.inspect.States","ephox.pastiche.list.stage.Handler","ephox.pastiche.list.stage.Handlers","ephox.pastiche.list.state.Transitions"],function(a,d,c,b){return{run:function(e){return c("AfterNoBullet",
[d(a.isCloser,function(a,c,d){b.next(c,e);c.styleLevelAdjust.set(-1);a.emit(d)}," closing open tag")],function(a,b,c){a.emit(c)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Images",["ephox.pastiche.inspect.Tags"],function(a){var d=a.isEndOf("IMG");return{isStart:a.isStartOf("IMG"),isEnd:d}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Markers",
["ephox.pastiche.engine.Token"],function(a){var d=function(a,b){return"A"===b.tag()||"SPAN"===b.tag()};return{isStart:function(c,b){return b.type()===a.START_ELEMENT_TYPE&&d(c,b)},isEnd:function(c,b){return b.type()===a.END_ELEMENT_TYPE&&d(c,b)},is:d}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.state.Spans",["ephox.pastiche.list.state.Transitions"],function(a){var d=function(a,c,d){c.spanCount.get().push(d)},c=function(a,
c,d){c.spanCount.get().pop()};return{deferAndPush:function(a,c,f){a.defer(f);d(a,c,f)},deferAndPop:function(a,d,f){a.defer(f);c(a,d,f)},push:d,pop:c,pushThen:function(b){return function(c,f,g){d(c,f,g);a.next(f,b)}},popThen:function(b){return function(d,f,g){c(d,f,g);a.next(f,b)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.BeforeSpacerState","ephox.pastiche.inspect.Images ephox.pastiche.inspect.Markers ephox.pastiche.list.stage.Handler ephox.pastiche.list.stage.Handlers ephox.pastiche.list.state.Spans ephox.peanut.Fun".split(" "),
function(a,d,c,b,e,f){return{run:function(g,l,m){return b("BeforeSpacer",[c(d.isStart,e.pushThen(g),"start marker"),c(d.isEnd,e.popThen(l),"end marker"),c(a.isEnd,f.noop,"end image")],function(a,b,c){m(a,c)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Comments",["ephox.pastiche.engine.Token"],function(a){var d=function(c,b){return b.type()===a.COMMENT_TYPE};return{is:d,isNotEndIf:function(a,b){return d(a,b)&&"[endif]"!==
b.text()},isEndIf:function(a,b){return d(a,b)&&"[endif]"===b.text()},isListSupport:function(a,b){return d(a,b)&&"[if !supportLists]"===b.text()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Logic",["ephox.compass.Arr"],function(a){return{any:function(d){return function(c,b){return a.exists(d,function(a){return a(c,b)})}},all:function(d){return function(c,b){return a.forall(d,function(a){return a(c,b)})}}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.CloseSpansState","ephox.pastiche.inspect.Comments ephox.pastiche.inspect.Logic ephox.pastiche.inspect.Markers ephox.pastiche.inspect.Tags ephox.pastiche.inspect.Texts ephox.pastiche.list.detect.ListTypes ephox.pastiche.list.stage.Handler ephox.pastiche.list.stage.Handlers ephox.pastiche.list.state.Spans ephox.pastiche.list.state.Transitions ephox.peanut.Fun ephox.perhaps.Option ephox.perhaps.Result".split(" "),
function(a,d,c,b,e,f,g,l,m,n,h,k,p){return{run:function(t,u){var v=function(a){var b=a.emitter.getCurrentListType(),b=a.emitter.getCurrentLevel()==a.itemLevel.get()?b:null;return k.from(f.guess(a.bulletInfo.get(),b,a.originalToken.get()))},x=function(a,b,c){b.emitter.openItem(b.itemLevel.get(),b.originalToken.get(),b.listType.get(),b.skippedPara.get());for(a.emitDeferred();0<b.spanCount.get().length;)a.emit(b.spanCount.get().shift())},y=function(a,b){n.next(a,t);if(a.commentMode.get()){var c=a.indentGuesser.guessIndentLevel(b,
a.originalToken.get(),a.styles,a.bulletInfo.get());a.itemLevel.set(c)}},A=function(a,b,c){return v(b).fold(function(){b.listType.set(null);return p.error("Unknown list type: "+b.bulletInfo.get().text+" Symbol font? "+b.bulletInfo.get().symbolFont)},function(a){b.listType.set(a);return p.value(x)})},z=function(a,b,c){y(b,c);A(a,b,c).fold(function(b){console.log(b);a.rollback()},function(d){d(a,b,c);a.emit(c)})},z=[g(d.any([e.is,b.isStart]),z,"text or start tag"),g(a.isNotEndIf,z,"non-endif comment"),
g(a.isEndIf,function(a,b,c){y(b,c);A(a,b,c).fold(function(b){console.log(b);a.rollback()},function(d){d(a,b,c)})},"endif comment"),g(c.isEnd,m.pop,"end marker"),g(b.isEnd,h.noop,"end tag")];return l("CloseSpans",z,function(a,b,c){u(a,c)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.FindListTypeState","ephox.pastiche.inspect.Images ephox.pastiche.inspect.Markers ephox.pastiche.inspect.Texts ephox.pastiche.list.stage.Handler ephox.pastiche.list.stage.Handlers ephox.pastiche.list.state.Spans ephox.pastiche.list.state.Transitions ephox.peanut.Fun".split(" "),
function(a,d,c,b,e,f,g,l){return{run:function(m,n){var h=function(a){return function(b,c,d){g.next(c,m);c.bulletInfo.set(a(c,d))}},k=h(function(a,b){return{text:b.text(),symbolFont:a.symbolFont.get()}}),h=h(function(a,b){return{text:"\u2202",symbolFont:!0}});return e("FindListType",[b(c.isWhitespace,l.noop,"text is whitespace"),b(c.is,k,"text"),b(d.isStart,f.push,"start marker"),b(d.isEnd,f.pop,"end marker"),b(a.isStart,h,"start image")],function(a,b,c){n(a,c)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.ItemContentState",["ephox.pastiche.inspect.States","ephox.pastiche.list.stage.Handler","ephox.pastiche.list.stage.Handlers","ephox.pastiche.list.state.Transitions"],function(a,d,c,b){return{run:function(e){var f=[d(a.isCloser,function(a,c,d){b.next(c,e);c.skippedPara.set(!1)},"Closing open tag")];return c("ItemContentState",f,function(a,b,c){a.emit(c)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.pastiche.list.state.CommentOff",["ephox.pastiche.inspect.Comments","ephox.pastiche.inspect.Texts"],function(a,d){return{isText:function(a,b){return!a.commentMode.get()&&d.is(a,b)},isComment:function(c,b){return!c.commentMode.get()&&a.is(c,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.state.CommentOn",["ephox.pastiche.engine.TokenUtil","ephox.pastiche.inspect.Comments","ephox.pastiche.inspect.Texts"],
function(a,d,c){return{isText:function(a,d){return a.commentMode.get()&&c.is(a,d)},isComment:function(a,c){return a.commentMode.get()&&d.is(a,c)},isUnstyled:function(b,c){var d=a.getAttribute(c,"style");return b.commentMode.get()&&""===d||null===d}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.ListStartState","ephox.compass.Arr ephox.pastiche.inspect.Logic ephox.pastiche.inspect.Microsoft ephox.pastiche.inspect.Tags ephox.pastiche.inspect.Texts ephox.pastiche.list.detect.ListSymbols ephox.pastiche.list.stage.Handler ephox.pastiche.list.stage.Handlers ephox.pastiche.list.state.CommentOff ephox.pastiche.list.state.CommentOn ephox.pastiche.list.state.Spans ephox.pastiche.list.state.Transitions ephox.peanut.Fun".split(" "),
function(a,d,c,b,e,f,g,l,m,n,h,k,p){return{run:function(t,u,v){var x=function(b,c,d){var e=c.originalToken.get(),f=c.spanCount.get();c.emitter.closeAllLists();b.emit(e);a.each(f,b.emit);b.emit(d);b.commit();c.originalToken.set(e);k.next(c,u)},y=function(a,b){return e.is(a,b)&&0<f.match(b.text(),a.symbolFont.get()).length},A=function(a,f,s){c.isIgnore(f,s)&&k.next(f,t);return l("ListStartState",[g(d.all([b.isStartOf("SPAN"),n.isUnstyled]),h.pushThen(t),"unstyled span"),g(b.isStartOf("SPAN"),h.push,
"starting span"),g(b.isStartOf("A"),h.push,"starting a"),g(b.isEndOf("A"),h.pop,"ending a"),g(n.isText,k.jump(t),"commentOn -> text"),g(y,k.jump(t),"mogran :: text is [1-9]."),g(e.is,x,"text"),g(m.isComment,p.noop,"commentOff -> comment"),g(b.isStartOf("IMG"),k.jump(t),"mogran :: start image tag")],function(a,b,c){v(a,c)})(a,f,s)};A.toString=function(){return"Handlers for ListStartState"};return A}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Browser",
["ephox.pastiche.engine.Token","ephox.pastiche.inspect.Microsoft","global!document","global!navigator"],function(a,d,c,b){return{supportsCustomStyles:function(){if(0<b.userAgent.indexOf("Gecko")&&0>b.userAgent.indexOf("WebKit"))return!1;var e=c.createElement("div");try{e.innerHTML='<p style="mso-list: Ignore;">&nbsp;</p>'}catch(f){return!1}e=a.token(e.firstChild);return d.isIgnore({},e)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.detect.ImageList",
[],function(){return{is:function(a,d,c){a=void 0!==d&&null!==d&&void 0!==d.getAttribute&&null!==d.getAttribute?d.getAttribute("alt"):"";return!!d&&"IMG"===d.tagName&&"*"===a}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.detect.TextList",["ephox.pastiche.list.detect.ListTypes","ephox.violin.Strings"],function(a,d){return{is:function(c,b,e){var f=b.nodeValue;d.trim(f)||(f=(b=b.parentNode.nextSibling)?b.nodeValue:"");return b&&
d.trim(b.parentNode.textContent)==f?(listType=a.guess({text:f,symbolFont:e},null,c.originalToken.get()))?!!b.nextSibling&&"SPAN"===b.nextSibling.tagName&&/^[\u00A0\s]/.test(b.nextSibling.firstChild.nodeValue)&&("P"===c.openedTag.get().tag()||"UL"===listType.tag):!1:!1}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.detect.ListDetect",["ephox.highway.Merger","ephox.pastiche.inspect.Tags","ephox.pastiche.list.detect.ImageList",
"ephox.pastiche.list.detect.TextList"],function(a,d,c,b){var e=function(b){var c=b.node.style.fontFamily;return c?a.merge(b,{symbol:"Wingdings"===c||"Symbol"===c}):b},f=function(a){return!!a.firstChild&&("SPAN"===a.firstChild.tagName||"A"===a.firstChild.tagName)};return{isUnofficialList:function(a,l){var m;if(!d.isStartOf("SPAN")(a,l)||!a.openedTag.get())return!1;m=a.openedTag.get().getNode();for(var n=e({node:m}),n=1<m.childNodes.length&&"A"===n.node.firstChild.tagName&&""===n.node.firstChild.textContent?
{node:n.node.childNodes[1],symbol:n.symbol}:n;f(n.node);)n=e({node:n.node.firstChild,symbol:n.symbol});m=n.node.firstChild;return(m&&3===m.nodeType?b:c).is(a,m,n.symbol)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.inspect.Lists","ephox.pastiche.engine.TokenUtil ephox.pastiche.inspect.Browser ephox.pastiche.inspect.Comments ephox.pastiche.inspect.Microsoft ephox.pastiche.inspect.Tags ephox.pastiche.list.detect.ListDetect ephox.perhaps.Option".split(" "),
function(a,d,c,b,e,f,g){var l=function(b){b=a.getMsoList(b);return(b=/ level([0-9]+)/.exec(b))&&b[1]?g.some(parseInt(b[1],10)):g.none()},m=function(a,c){return e.isStart(a,c)&&b.isList(a,c)&&"LI"!==c.tag()};return{getLevel:l,isStart:m,isValidStart:function(a,b){return m(a,b)&&l(b).isSome()},isInvalidStart:function(a,b){return m(a,b)&&l(b).isNone()},isSpecial:function(a,b){return!d.supportsCustomStyles()&&c.isListSupport(a,b)||f.isUnofficialList(a,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.NoListState","ephox.pastiche.inspect.Lists ephox.pastiche.inspect.Markers ephox.pastiche.inspect.Tags ephox.pastiche.list.stage.Handler ephox.pastiche.list.stage.Handlers ephox.pastiche.list.state.Spans ephox.pastiche.list.state.Transitions".split(" "),function(a,d,c,b,e,f,g){return{run:function(l){var m=function(a,b,c){b.emitter.closeAllLists();a.emitDeferred();b.openedTag.set(null);a.emit(c);g.next(b,h)},h=e("NoListState",[b(a.isValidStart,
function(b,c,d){a.getLevel(d).each(function(a){c.itemLevel.set(a+c.styleLevelAdjust.get());(g.isNext(c,h)?b.emitDeferred:b.dropDeferred)();g.next(c,l());b.startTransaction();c.originalToken.set(d);c.commentMode.set(!1)})},"valid list so start it"),b(a.isInvalidStart,m,"invalid list so close lists"),b(a.isSpecial,function(a,b,d){c.isStartOf("SPAN")(b,d)&&f.push(a,b,d);g.next(b,l());a.startTransaction();b.originalToken.set(b.openedTag.get());b.commentMode.set(!0);b.openedTag.set(null);a.dropDeferred()},
"special list"),b(d.isEnd,f.deferAndPop,"closing marker"),b(d.isStart,f.deferAndPush,"starting marker"),b(c.isStart,function(a,b,c){b.openedTag.get()&&(b.emitter.closeAllLists(),a.emitDeferred());b.openedTag.set(c);a.defer(c)},"starting tag")],m);return h}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.SkipEmptyParaState","ephox.pastiche.inspect.Browser ephox.pastiche.inspect.Logic ephox.pastiche.inspect.Microsoft ephox.pastiche.inspect.Tags ephox.pastiche.inspect.Texts ephox.pastiche.list.detect.ListDetect ephox.pastiche.list.stage.Handler ephox.pastiche.list.stage.Handlers ephox.pastiche.list.state.Spans ephox.pastiche.list.state.Transitions".split(" "),
function(a,d,c,b,e,f,g,l,m,h){return{run:function(k,p){return l("SkipEmptyPara",[g(d.all([function(a,c){return b.isStartOf("SPAN")(a,c)&&0===a.spanCount.get().length},function(b,d){return(a.supportsCustomStyles()||!f.isUnofficialList(b,d))&&!c.isList(b,d)}]),m.deferAndPush,"first marker or not list"),g(b.isEndOf("SPAN"),m.deferAndPop,"end span"),g(b.isEndOf("P"),function(a,b,c){a.defer(c);b.skippedPara.set(!0);b.openedTag.set(null);h.next(b,p())},"end p"),g(b.isEnd,h.jump(k),"end tag"),g(e.isWhitespace,
function(a,b,c){a.defer(c)},"whitespace")],h.jump(k))}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.stage.SpacerState","ephox.pastiche.inspect.Markers ephox.pastiche.inspect.Tags ephox.pastiche.list.stage.Handler ephox.pastiche.list.stage.Handlers ephox.pastiche.list.state.Spans ephox.pastiche.list.state.Transitions ephox.peanut.Fun".split(" "),function(a,d,c,b,e,f,g){return{run:function(l){return b("Spacer",[c(a.isEnd,e.popThen(l),
"end marker"),c(d.isEnd,f.setNext(l),"end tag")],g.noop)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.emit.Emission",["ephox.scullion.Struct"],function(a){var d=a.immutable("state","result"),c=a.immutable("state","value");return{state:a.immutable("level","type","types","items"),value:c,result:d}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.emit.ItemStack",
["ephox.pastiche.list.emit.Emission","ephox.perhaps.Option"],function(a,d){return{start:function(c,b){var e=c.items().slice(0),f=void 0!==b&&"P"!==b?d.some(b):d.none();f.fold(function(){e.push("P")},function(a){e.push(a)});var g=a.state(c.level(),c.type(),c.types(),e);return a.value(g,f)},finish:function(c){var b=c.items().slice(0);if(0<b.length&&"P"!==b[b.length-1]){var e=b[b.length-1];b[b.length-1]="P";c=a.state(c.level(),c.type(),c.types(),b);return a.value(c,d.some(e))}return a.value(c,d.none())}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.emit.ListLevels",["ephox.pastiche.list.emit.Emission"],function(a){var d=function(c,b,d){for(var f=[];b(c);){var g=d(c);c=g.state();f=f.concat(g.result())}return a.result(c,f)};return{moveRight:function(a,b,e){return d(a,function(a){return a.level()<b},e)},moveLeft:function(a,b,e){return d(a,function(a){return a.level()>b},e)},moveUntil:d}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.pastiche.list.emit.ListItemStyles",["ephox.pastiche.engine.TokenUtil"],function(a){return{from:function(d){var c={"list-style-type":"none"};d?(d=a.getStyle(d,"margin-left"),d=void 0!==d&&"0px"!==d?{"margin-left":d}:{}):d=c;return d}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.emit.SkippedTokens",["ephox.pastiche.engine.Token","ephox.peanut.Fun"],function(a,d){return{para:function(c){return[d.curry(a.createStartElement,
"P",{},{}),d.curry(a.createText,"\u00a0"),d.curry(a.createEndElement,"P")]}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.emit.ListTokens","ephox.pastiche.cleanup.Cleaners ephox.pastiche.engine.Token ephox.pastiche.list.detect.ListTypes ephox.pastiche.list.emit.Emission ephox.pastiche.list.emit.ItemStack ephox.pastiche.list.emit.ListItemStyles ephox.pastiche.list.emit.SkippedTokens ephox.peanut.Fun".split(" "),function(a,
d,c,b,e,f,g,l){var m=function(a,c,e){var f=c.start&&1<c.start?{start:c.start}:{},g=a.level()+1,m=a.types().concat([c]);e=[l.curry(d.createStartElement,c.tag,f,e)];a=b.state(g,c,m,a.items());return b.result(a,e)},h=function(a){var c=a.types().slice(0),e=[l.curry(d.createEndElement,c.pop().tag)],f=a.level()-1;a=b.state(f,c[c.length-1],c,a.items());return b.result(a,e)},k=function(a,c,d){a=h(a);d=d?g.para():[];c=m(a.state(),c,c.type?{"list-style-type":c.type}:{});return b.result(c.state(),a.result().concat(d).concat(c.result()))};
return{open:m,openItem:function(g,m,h,n){var p=f.from(m);g=g.type()&&!c.eqListType(g.type(),h)?k(g,h,n):b.result(g,[]);p=[l.curry(d.createStartElement,"LI",{},p)];h=e.start(g.state(),m&&m.tag());n=h.value().map(function(b){a.styleDom(m.getNode(),l.constant(!0));return[l.constant(m)]}).getOr([]);return b.result(h.state(),g.result().concat(p).concat(n))},close:h,closeItem:function(a){var c=l.curry(d.createEndElement,"LI");a=e.finish(a);var f=a.value().fold(function(){return[c]},function(a){return[l.curry(d.createEndElement,
a),c]});return b.result(a.state(),f)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.emit.ListModel","ephox.compass.Arr ephox.pastiche.engine.Token ephox.pastiche.list.emit.Emission ephox.pastiche.list.emit.ItemStack ephox.pastiche.list.emit.ListLevels ephox.pastiche.list.emit.ListTokens ephox.peanut.Fun ephox.perhaps.Option".split(" "),function(a,d,c,b,e,f,g,l){var m=function(b){if(0===b.length)throw"Compose must have at least one element in the list";
var d=b[b.length-1];b=a.bind(b,function(a){return a.result()});return c.result(d.state(),b)},h=function(a){a=f.closeItem(a);var b=f.close(a.state());return m([a,b])},k=function(a,b,c,d){return e.moveRight(a,c,function(a){var e=a.level()===c-1&&b.type?{"list-style-type":b.type}:{};a=f.open(a,b,e);e=f.openItem(a.state(),a.state().level()==c?d:void 0,b);return m([a,e])})},p=function(a,b){return e.moveLeft(a,b,h)},w=function(a,e,f,m){var h=1<f?b.finish(a):c.value(a,l.none());a=h.value().map(function(a){return[g.curry(d.createEndElement,
a)]}).getOr([]);h.state().level();e=k(h.state(),e,f,m);return c.result(e.state(),a.concat(e.result()))};return{openItem:function(a,b,d,e,g){a=a.level()>b?p(a,b):c.result(a,[]);a.state().level()===b?(b=a.state(),b=0<b.level()?f.closeItem(b):c.result(b,[]),d=f.openItem(b.state(),d,e,g),d=m([b,d])):d=w(a.state(),e,b,d);return m([a,d])},closeAllLists:p}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.emit.Emitter",["ephox.compass.Arr",
"ephox.pastiche.list.emit.Emission","ephox.pastiche.list.emit.ListModel"],function(a,d,c){var b=["disc","circle","square"];return function(e,f){var g=d.state(0,void 0,[],[]),l=function(b){a.each(b.result(),function(a){a=a(f);e.emit(a)})};return{closeAllLists:function(){var a=c.closeAllLists(g,0);g=a.state();l(a);e.commit()},openItem:function(a,d,e,f){e&&("UL"===e.tag&&b[a-1]===e.type&&(e={tag:"UL"}),a=c.openItem(g,a,d,e,f),g=a.state(),l(a))},getCurrentListType:function(){return g.type()},getCurrentLevel:function(){return g.level()}}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.indent.ListIndent",["ephox.compass.Arr","ephox.pastiche.engine.TokenUtil","ephox.violin.Strings","global!Math"],function(a,d,c,b){var e=function(a){return a.ownerDocument.defaultView?a.ownerDocument.defaultView.getComputedStyle(a,null):a.currentStyle||{}},f=function(a){var b={};return function(c,d){var e,f=c+","+d;if(b.hasOwnProperty(f))return b[f];e=a.call(null,c,d);return b[f]=e}}(function(b,d){var f,h=/([^{]+){([^}]+)}/g,
k;for(h.lastIndex=0;null!==(f=h.exec(b))&&!k;)a.each(f[1].split(","),function(a){var b=a.indexOf(".");if(0<=b&&c.trim(a.substring(b+1))===d)return k=f[2],!1});return k?(h=document.createElement("p"),h.setAttribute("style",k),(h=e(h))?""+h.marginLeft:!1):!1});return{indentGuesser:function(){var a,c;return{guessIndentLevel:function(e,h,k,s){var p=1;if(s&&/^([0-9]+\.)+[0-9]+\.?$/.test(s.text))return s.text.replace(/([0-9]+|\.$)/g,"").length+1;k=c||parseInt(f(k,d.getAttribute(h,"class")));e=e.getNode();
h=h.getNode();s=0;for(e=e.parentNode;null!==e&&e!==h.parentNode;)s+=e.offsetLeft,e=e.offsetParent;h=s;k?a?h+=a:0===h&&(a=k,h+=k):k=48;c=k=b.min(h,k);return p=b.max(1,b.floor(h/k))||1}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.state.ListStyles",["ephox.pastiche.engine.Token"],function(a){return function(){var d=!1,c="";return{check:function(b){return d&&b.type()===a.TEXT_TYPE?(c+=b.text(),!0):b.type()===a.START_ELEMENT_TYPE&&
"STYLE"===b.tag()?d=!0:b.type()===a.END_ELEMENT_TYPE&&"STYLE"===b.tag()?(d=!1,!0):!1}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.scullion.Cell",[],function(){var a=function(d){var c=d;return{get:function(){return c},set:function(a){c=a},clone:function(){return a(c)}}};return a})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.list.state.ListState",["ephox.pastiche.list.emit.Emitter",
"ephox.pastiche.list.indent.ListIndent","ephox.pastiche.list.state.ListStyles","ephox.peanut.Fun","ephox.scullion.Cell"],function(a,d,c,b,e){var f=d.indentGuesser(),g=c(),l={getCurrentListType:function(){return m().getCurrentListType()},getCurrentLevel:function(){return m().getCurrentLevel()},closeAllLists:function(){return m().closeAllLists.apply(void 0,arguments)},openItem:function(){return m().openItem.apply(void 0,arguments)}},m=function(){return{getCurrentListType:b.constant({}),getCurrentLevel:b.constant(1),
closeAllLists:b.identity,openItem:b.identity}};return function(c){var d=e(c),h=e(0),k=e(null),p=e(!1),u=e(null),v=e(!1),x=e(null),y=e([]),A=e(!1),z=e(0),B=e(void 0),D=e([]);return{reset:function(e){d.set(c);h.set(0);k.set(null);p.set(!1);u.set(null);v.set(!1);x.set(null);y.set([]);A.set(!1);z.set(0);B.set(void 0);D.set([]);_emitter=a(e,e.document);m=b.constant(_emitter)},nextFilter:d,itemLevel:h,originalToken:k,commentMode:p,openedTag:u,symbolFont:v,listType:x,spanCount:y,skippedPara:A,styleLevelAdjust:z,
bulletInfo:B,logger:D,emitter:l,styles:g,indentGuesser:f}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.pastiche.filter.Lists","ephox.pastiche.engine.Filter ephox.pastiche.list.detect.ListTypes ephox.pastiche.list.stage.AfterListState ephox.pastiche.list.stage.AfterNoBulletListState ephox.pastiche.list.stage.BeforeSpacerState ephox.pastiche.list.stage.CloseSpansState ephox.pastiche.list.stage.FindListTypeState ephox.pastiche.list.stage.ItemContentState ephox.pastiche.list.stage.ListStartState ephox.pastiche.list.stage.NoListState ephox.pastiche.list.stage.SkipEmptyParaState ephox.pastiche.list.stage.SpacerState ephox.pastiche.list.state.ListState ephox.pastiche.list.state.Transitions".split(" "),
function(a,d,c,b,e,f,g,l,m,h,k,p,w,t){var u=function(a,b){var c="Type: "+b.type()+", Tag: "+b.tag()+" Text: "+b.text();console.log("Unexpected token in list conversion: "+c,y.nextFilter.get());a.rollback()};h=h.run(function(){return x});k=k.run(h,function(){return v});var v=c.run(k,h);c=l.run(v);f=f.run(c,u);p=p.run(f);e=e.run(p,f,u);g=g.run(e,u);b=b.run(v);var x=m.run(g,b,u),y=w(h);return a.createFilter(function(a,b){y.styles&&y.styles.check(b)||(y.symbolFont.set(d.checkFont(b,y.symbolFont.get())),
t.go(a,y,b))},y.reset,"lists")})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.limbo.process.Strategies","ephox.pastiche.api.HtmlPatterns ephox.pastiche.api.HybridAction ephox.pastiche.api.RuleConditions ephox.pastiche.api.RuleMutations ephox.pastiche.filter.Lists ephox.peanut.Fun ephox.sugar.api.Class ephox.sugar.api.Css ephox.violin.StringMatch".split(" "),function(a,d,c,b,e,f,g,l,m){var h=d.unwrapper({tags:[{name:m.pattern(/^([OVWXP]|U[0-9]+|ST[0-9]+):/i)}]});
e=d.pipeline([e]);var k=d.removal({attributes:[{name:m.pattern(/^v:/)},{name:m.exact("href"),value:m.contains("#_toc")},{name:m.exact("href"),value:m.contains("#_mso")},{name:m.pattern(/^xmlns(:|$)/)},{name:m.exact("align")},{name:m.exact("type"),condition:c.isList}]}),p=d.removal({tags:[{name:m.exact("script")},{name:m.exact("meta")},{name:m.exact("link")},{name:m.exact("style"),condition:c.isEmpty}],attributes:[{name:m.starts("on")},{name:m.exact('"')},{name:m.exact("id")},{name:m.exact("name")},
{name:m.exact("lang")},{name:m.exact("language")}],styles:[{name:m.all(),value:m.pattern(/OLE_LINK/i)}]}),w=d.removal({styles:[{name:m.not(m.pattern(/width|height|list-style-type/)),condition:function(a){return!g.has(a,"ephox-limbo-transform")}},{name:m.pattern(/width|height/),condition:c.isNotImage}]}),t=d.removal({classes:[{name:m.not(m.exact("rtf-data-image"))}]}),u=d.removal({styles:[{name:m.pattern(a.validStyles())}]}),v=d.removal({classes:[{name:m.pattern(/mso/i)}]}),x=d.unwrapper({tags:[{name:m.exact("img"),
condition:c.isLocal},{name:m.exact("a"),condition:c.hasNoAttributes}]}),y=d.unwrapper({tags:[{name:m.exact("a"),condition:c.hasNoAttributes}]}),A=d.removal({attributes:[{name:m.exact("style"),value:m.exact(""),debug:!0}]}),z=d.removal({attributes:[{name:m.exact("class"),value:m.exact(""),debug:!0}]});a=d.unwrapper({tags:[{name:m.pattern(a.specialInline()),condition:f.not(c.hasContent)}]});c=d.transformer({tags:[{name:m.exact("p"),mutate:b.addBrTag}]});var B=d.transformer({tags:[{name:m.pattern(/ol|ul/),
mutate:b.properlyNest}]}),D=d.transformer({tags:[{name:m.exact("b"),mutate:f.curry(b.changeTag,"strong")},{name:m.exact("i"),mutate:f.curry(b.changeTag,"em")},{name:m.exact("u"),mutate:function(a){a=b.changeTag("span",a);g.add(a,"ephox-limbo-transform");l.set(a,"text-decoration","underline")}},{name:m.exact("s"),mutate:f.curry(b.changeTag,"strike")},{name:m.exact("font"),mutate:b.fontToSpan,debug:!0}]}),G=d.removal({classes:[{name:m.exact("ephox-limbo-transform")}]});d=d.removal({attributes:[{name:m.exact("href"),
value:m.starts("file:///"),debug:!0}]});return{unwrapWordTags:h,removeWordAttributes:k,parseLists:e,removeExcess:p,cleanStyles:w,cleanClasses:t,mergeStyles:u,mergeClasses:v,removeLocalImages:x,removeVacantLinks:y,removeEmptyStyle:A,removeEmptyClass:z,pruneInlineTags:a,addPlaceholders:c,nestedListFixes:B,inlineTagFixes:D,cleanupFlags:G,removeLocalLinks:d,none:f.noop}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.limbo.process.PasteFilters",
"ephox.compass.Arr ephox.limbo.api.RtfImage ephox.limbo.process.Strategies ephox.pastiche.api.HybridAction ephox.pastiche.engine.Filter ephox.pastiche.engine.Token ephox.sugar.api.Element".split(" "),function(a,d,c,b,e,f,g){var l=function(a){return a.browser.isIE()&&11<=a.browser.version.major},m=function(a){return e.createFilter(function(b,c){var d=a(g.fromDom(c.getNode())).fold(function(){return c},function(a){a=a.dom();return f.token(a,c.type()===f.END_ELEMENT_TYPE,{})});b.emit(d)})},h=function(a,
b,d){a=[c.mergeStyles,c.mergeClasses];d=[c.cleanStyles,c.cleanClasses];return b?a:d},k=function(a,b,d){if(!a)return c.none;a=[c.unwrapWordTags];d=l(d)?[]:c.parseLists;return a.concat(d)};return{derive:function(e,f,g){var p,v;v=g.browser.isFirefox()?d.local:d.vshape;p=l(g)?c.none:b.pipeline([m(v)]);p=[e?p:c.none];v=[v===d.local?c.none:c.removeLocalImages];return a.flatten([p,[c.inlineTagFixes],k(e,f,g),[c.nestedListFixes],[c.removeExcess],v,h(e,f,g),[c.removeLocalLinks,c.removeVacantLinks],[c.removeEmptyStyle],
[c.removeEmptyClass],[c.pruneInlineTags],[c.addPlaceholders],[c.cleanupFlags]])}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.boss.common.TagBoundaries",[],function(){return"body p div article aside figcaption figure footer header nav section ol ul li table thead tbody caption tr td th h1 h2 h3 h4 h5 h6 blockquote pre address".split(" ")})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.Text",
["ephox.sugar.api.Node","ephox.sugar.impl.NodeValue"],function(a,d){var c=d(a.isText,"text");return{get:function(a){return c.get(a)},getOption:function(a){return c.getOption(a)},set:function(a,d){c.set(a,d)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.boss.api.DomUniverse","ephox.boss.common.TagBoundaries ephox.compass.Arr ephox.peanut.Fun ephox.sugar.api.Attr ephox.sugar.api.Compare ephox.sugar.api.Css ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.InsertAll ephox.sugar.api.Node ephox.sugar.api.PredicateFilter ephox.sugar.api.PredicateFind ephox.sugar.api.Remove ephox.sugar.api.SelectorFilter ephox.sugar.api.SelectorFind ephox.sugar.api.Text ephox.sugar.api.Traverse".split(" "),
function(a,d,c,b,e,f,g,l,m,h,k,p,w,t,u,v,x){return function(){return{up:c.constant({selector:u.ancestor,closest:u.closest,predicate:p.ancestor,all:x.parents}),down:c.constant({selector:t.descendants,predicate:k.descendants}),styles:c.constant({get:f.get,set:f.set,remove:f.remove}),attrs:c.constant({get:b.get,set:b.set,remove:b.remove,copyTo:function(a,c){var d=b.clone(a);b.setAll(c,d)}}),insert:c.constant({before:l.before,after:l.after,afterAll:m.after,append:l.append,appendAll:m.append,prepend:l.prepend,
wrap:l.wrap}),remove:c.constant({unwrap:w.unwrap,remove:w.remove}),create:c.constant({nu:g.fromTag,clone:function(a){return g.fromDom(a.dom().cloneNode(!1))},text:g.fromText}),query:c.constant({comparePosition:function(a,b){return a.dom().compareDocumentPosition(b.dom())},prevSibling:x.prevSibling,nextSibling:x.nextSibling}),property:c.constant({children:x.children,name:h.name,parent:x.parent,isText:h.isText,isElement:h.isElement,getText:v.get,setText:v.set,isBoundary:function(b){if(!h.isElement(b))return!1;
if("body"===h.name(b))return!0;var c=f.get(b,"display");return void 0!==c&&0<c.length?d.contains(["block","table-cell","table-row","table","list-item"],c):d.contains(a,h.name(b))},isEmptyTag:function(a){return h.isElement(a)?d.contains(["br","img","hr"],h.name(a)):!1}}),eq:e.eq,is:e.is}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.api.data.NamedPattern",["ephox.scullion.Struct"],function(a){return a.immutable("word","pattern")})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.api.data.Spot",["ephox.scullion.Struct"],function(a){var d=a.immutable("element","offset"),c=a.immutable("element","deltaOffset"),b=a.immutable("element","start","finish"),e=a.immutable("begin","end");a=a.immutable("element","text");return{point:d,delta:c,range:b,points:e,text:a}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.extract.TypedItem",
["ephox.peanut.Fun","ephox.perhaps.Option"],function(a,d){var c=a.constant(!1),b=a.constant(!0),e=function(e){return{isBoundary:function(){return e(b,c,c)},fold:e,toText:function(){return e(d.none,d.none,function(a,b){return d.some(a)})},is:function(a){return e(c,c,function(b,c){return c.eq(b,a)})},len:function(){return e(a.constant(0),a.constant(1),function(a,b){return b.property().getText(a).length})}}};return{text:function(a,b){return e(function(c,d,e){return e(a,b)})},boundary:function(a,b){return e(function(c,
d,e){return c(a,b)})},empty:function(a,b){return e(function(c,d,e){return d(a,b)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.array.Boundaries",["ephox.compass.Arr","ephox.peanut.Fun"],function(a,d){return{boundAt:function(c,b,e,f){b=a.findIndex(c,d.curry(f,b));b=-1<b?b:0;e=a.findIndex(c,d.curry(f,e));return c.slice(b,-1<e?e+1:c.length)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,
h,k){a("ephox.polaris.array.Slice",["ephox.compass.Arr"],function(a){return{sliceby:function(d,c){var b=a.findIndex(d,c);return d.slice(0,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.array.Split",["ephox.compass.Arr"],function(a){return{splitby:function(d,c){var b=[],e=[];a.each(d,function(a){c(a)?(b.push(e),e=[]):e.push(a)});0<e.length&&b.push(e);return b}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.polaris.api.Arrays",["ephox.polaris.array.Boundaries","ephox.polaris.array.Slice","ephox.polaris.array.Split"],function(a,d,c){return{splitby:function(a,d){return c.splitby(a,d)},sliceby:function(a,c){return d.sliceby(a,c)},boundAt:function(b,c,d,g){return a.boundAt(b,c,d,g)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.extract.TypedList",["ephox.compass.Arr","ephox.peanut.Fun","ephox.perhaps.Option",
"ephox.phoenix.api.data.Spot","ephox.polaris.api.Arrays"],function(a,d,c,b,e){return{count:function(b){return a.foldr(b,function(a,b){return b.len()+a},0)},dropUntil:function(a,b){return e.sliceby(a,function(a){return a.is(b)})},gen:function(a,d){return a.fold(c.none,function(a){return c.some(b.range(a,d,d+1))},function(e){return c.some(b.range(e,d,d+a.len()))})},justText:function(b){return a.bind(b,function(a){return a.fold(d.constant([]),d.constant([]),d.identity)})}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.extract.Extract",["ephox.compass.Arr","ephox.phoenix.api.data.Spot","ephox.phoenix.extract.TypedItem","ephox.phoenix.extract.TypedList"],function(a,d,c,b){var e=function(b,d,f){if(b.property().isText(d))return[c.text(d,b)];if(b.property().isEmptyTag(d))return[c.empty(d,b)];if(b.property().isElement(d)){var h=b.property().children(d),k=b.property().isBoundary(d)?[c.boundary(d,b)]:[];d=void 0!==f&&f(d)?[]:a.bind(h,
function(a){return e(b,a,f)});return k.concat(d).concat(k)}return[]},f=function(a,c,f,h,k){a=e(a,h,k);c=b.dropUntil(a,c);c=b.count(c);return d.point(h,c+f)};return{typed:e,items:function(b,c,d){b=e(b,c,d);var f=function(a,b){return a};return a.map(b,function(a){return a.fold(f,f,f)})},extractTo:function(a,b,c,e,h){return a.up().predicate(b,e).fold(function(){return d.point(b,c)},function(d){return f(a,b,c,d,h)})},extract:function(a,b,c,e){return a.property().parent(b).fold(function(){return d.point(b,
c)},function(d){return f(a,b,c,d,e)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.extract.ExtractText",["ephox.compass.Arr","ephox.peanut.Fun","ephox.phoenix.extract.Extract"],function(a,d,c){var b=function(a,b){return"img"===b.property().name(a)?" ":"\n"};return{from:function(e,f,g){f=c.typed(e,f,g);return a.map(f,function(a){return a.fold(d.constant("\n"),b,e.property().getText)}).join("")}}})})(a.bolt.module.api.define,a.bolt.module.api.require,
a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.parray.Generator",["ephox.compass.Arr","ephox.peanut.Fun"],function(a,d){return{make:function(c,b,e){return a.foldl(c,function(a,c){return b(c,a.len).fold(d.constant(a),function(b){return{len:b.finish(),list:a.list.concat([b])}})},{len:void 0!==e?e:0,list:[]}).list}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.parray.Query",["ephox.compass.Arr","ephox.perhaps.Option"],
function(a,d){var c=function(a,b){return b>=a.start()&&b<=a.finish()},b=function(b,c){return a.findIndex(b,function(a){return a.start()===c})};return{get:function(b,f){var g=a.find(b,function(a){return c(a,f)});return d.from(g)},find:function(b,c){return d.from(a.find(b,c))},inUnit:c,sublist:function(a,c,d){c=b(a,c);var l=b(a,d);d=-1<l?l:a[a.length-1]&&a[a.length-1].finish()===d?a.length+1:-1;return-1<c&&-1<d?a.slice(c,d):[]}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.polaris.parray.Translate",["ephox.compass.Arr","ephox.highway.Merger","ephox.peanut.Fun"],function(a,d,c){return{translate:function(b,e){return a.map(b,function(a){return d.merge(a,{start:c.constant(a.start()+e),finish:c.constant(a.finish()+e)})})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.parray.Split",["ephox.compass.Arr","ephox.polaris.parray.Query","ephox.polaris.parray.Translate"],function(a,d,
c){var b=function(a,b,d){b=d(a,b);return c.translate(b,a.start())};return{splits:function(c,f,g){return 0===f.length?c:a.bind(c,function(c){var e=a.bind(f,function(a){return d.inUnit(c,a)?[a-c.start()]:[]});return 0<e.length?b(c,e,g):[c]})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.api.PositionArray",["ephox.polaris.parray.Generator","ephox.polaris.parray.Query","ephox.polaris.parray.Split","ephox.polaris.parray.Translate"],
function(a,d,c,b){return{generate:function(b,c,d){return a.make(b,c,d)},get:function(a,b){return d.get(a,b)},find:function(a,b){return d.find(a,b)},splits:function(a,b,d){return c.splits(a,b,d)},translate:function(a,c){return b.translate(a,c)},sublist:function(a,b,c){return d.sublist(a,b,c)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.extract.Find",["ephox.phoenix.api.data.Spot","ephox.phoenix.extract.Extract","ephox.phoenix.extract.TypedList",
"ephox.polaris.api.PositionArray"],function(a,d,c,b){return{find:function(e,f,g,l){e=d.typed(e,f,l);e=b.generate(e,c.gen);return b.get(e,g).map(function(b){return a.point(b.element(),g-b.start())})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.api.general.Extract",["ephox.phoenix.extract.Extract","ephox.phoenix.extract.ExtractText","ephox.phoenix.extract.Find"],function(a,d,c){return{extract:function(b,c,d,g){return a.extract(b,
c,d,g)},extractTo:function(b,c,d,g,l){return a.extractTo(b,c,d,g,l)},all:function(b,c,d){return a.items(b,c,d)},from:function(b,c,d){return a.typed(b,c,d)},find:function(a,d,f,g){return c.find(a,d,f,g)},toText:function(a,c,f){return d.from(a,c,f)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.family.Group",["ephox.compass.Arr","ephox.phoenix.api.general.Extract","ephox.polaris.api.Arrays"],function(a,d,c){return{group:function(b,
e,f){e=a.bind(e,function(a){return d.from(b,a,f)});e=c.splitby(e,function(a){return a.isBoundary()});return a.filter(e,function(a){return 0<a.length})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.family.Parents",["ephox.compass.Arr","ephox.perhaps.Option"],function(a,d){return{common:function(c,b,e){b=[b].concat(c.up().all(b));var f=[e].concat(c.up().all(e));e=a.find(b,function(b){return a.exists(f,function(a){return c.eq(a,b)})});
return d.from(e)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.wrap.OrphanText",["ephox.compass.Arr"],function(a){var d="table tbody thead tfoot tr ul ol".split(" ");return function(c){var b=c.property(),e=function(c,d){return b.parent(c).map(b.name).map(function(b){return!a.contains(d,b)}).getOr(!1)};return{validateText:function(a){return b.isText(a)&&e(a,d)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.phoenix.family.Range",["ephox.compass.Arr","ephox.peanut.Fun","ephox.phoenix.api.general.Extract","ephox.phoenix.family.Parents","ephox.phoenix.wrap.OrphanText"],function(a,d,c,b,e){return{range:function(f,g,l,h,k){return f.eq(g,h)?[g]:b.common(f,g,h).fold(function(){return[]},function(b){b=[b].concat(c.all(f,b,d.constant(!1)));var p=a.findIndex(b,d.curry(f.eq,g)),w=a.findIndex(b,d.curry(f.eq,h));b=-1<p&&-1<w?p<w?b.slice(p+l,w+k):b.slice(w+k,p+l):[];p=e(f);return a.filter(b,
p.validateText)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.api.general.Family",["ephox.phoenix.family.Group","ephox.phoenix.family.Range"],function(a,d){return{range:function(a,b,e,f,g){return d.range(a,b,e,f,g)},group:function(c,b,d){return a.group(c,b,d)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.string.Sanitise",[],function(){return{css:function(a){var d=
/^[a-zA-Z]/.test(a)?"":"e";a=a.replace(/[^\w]/gi,"-");return d+a}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.string.Split",["ephox.compass.Arr"],function(a){return{splits:function(d,c){if(0===c.length)return[d];var b=a.foldl(c,function(a,b){if(0===b)return a;var c=d.substring(a.prev,b);return{prev:b,values:a.values.concat([c])}},{prev:0,values:[]}),e=c[c.length-1];return e<d.length?b.values.concat(d.substring(e)):b.values}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.api.Strings",["ephox.polaris.string.Sanitise","ephox.polaris.string.Split"],function(a,d){return{cssSanitise:function(c){return a.css(c)},splits:function(a,b){return d.splits(a,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.search.Splitter",["ephox.compass.Arr","ephox.perhaps.Option","ephox.phoenix.api.data.Spot","ephox.polaris.api.PositionArray",
"ephox.polaris.api.Strings"],function(a,d,c,b,e){return{subdivide:function(f,g,l){var h=f.property().getText(g);l=a.filter(e.splits(h,l),function(a){return 0<a.length});if(1>=l.length)return[c.range(g,0,h.length)];f.property().setText(g,l[0]);var h=b.generate(l.slice(1),function(a,b){var e=f.create().text(a),e=c.range(e,b,b+a.length);return d.some(e)},l[0].length),k=a.map(h,function(a){return a.element()});f.insert().afterAll(g,k);return[c.range(g,0,l[0].length)].concat(h)}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.search.MatchSplitter",["ephox.compass.Arr","ephox.peanut.Fun","ephox.phoenix.search.Splitter","ephox.polaris.api.PositionArray"],function(a,d,c,b){return{separate:function(e,f,g){var l=a.bind(g,function(a){return[a.start(),a.finish()]}),h=b.splits(f,l,function(a,b){return c.subdivide(e,a.element(),b)});return a.map(g,function(c){var f=b.sublist(h,c.start(),c.finish()),f=a.map(f,function(a){return a.element()}),g=
a.map(f,e.property().getText).join("");return{elements:d.constant(f),word:c.word,exact:d.constant(g)}})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.bud.Unicode",[],function(){return{zeroWidth:function(){return"\u200b"},trimNative:function(a){return a.replace(/\u200B/,"").trim()},trimWithRegex:function(a){return a.replace(/^\s+|\s+$/g,"").replace(/\u200B/,"")}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.polaris.pattern.Chars",["ephox.bud.Unicode","ephox.peanut.Fun"],function(a,d){var c="\\w'\\-\\u00C0-\\u00FF"+a.zeroWidth()+"\\u2018\\u2019",b="[^"+c+"]",e="["+c+"]";return{chars:d.constant(c),wordbreak:d.constant(b),wordchar:d.constant(e)}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.pattern.Custom",["global!RegExp"],function(a){return function(d,c,b,e){return{term:function(){return new a(d,e.getOr("g"))},
prefix:c,suffix:b}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.pattern.Unsafe",["ephox.peanut.Fun","ephox.perhaps.Option","ephox.polaris.pattern.Chars","ephox.polaris.pattern.Custom"],function(a,d,c,b){return{token:function(c){return b(c,a.constant(0),a.constant(0),d.none())},word:function(a){a="((?:^'?)|(?:"+c.wordbreak()+"+'?))"+a+"((?:'?$)|(?:'?"+c.wordbreak()+"+))";return b(a,function(a){return 1<a.length?a[1].length:0},function(a){return 2<
a.length?a[2].length:0},d.none())}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.pattern.Safe",["ephox.polaris.pattern.Unsafe"],function(a){var d=function(a){return a.replace(/[-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")};return{sanitise:d,word:function(c){c=d(c);return a.word(c)},token:function(c){c=d(c);return a.token(c)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.api.Pattern",
["ephox.polaris.pattern.Chars","ephox.polaris.pattern.Custom","ephox.polaris.pattern.Safe","ephox.polaris.pattern.Unsafe"],function(a,d,c,b){return{safeword:function(a){return c.word(a)},safetoken:function(a){return c.token(a)},custom:function(a,b,c,l){return d(a,b,c,l)},unsafeword:function(a){return b.word(a)},unsafetoken:function(a){return b.token(a)},sanitise:function(a){return c.sanitise(a)},chars:function(){return a.chars()},wordbreak:function(){return a.wordbreak()},wordchar:function(){return a.wordchar()}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.search.Find",["ephox.peanut.Fun"],function(a){return{all:function(d,c){for(var b=c.term(),e=[],f=b.exec(d);f;){var g=f.index+c.prefix(f),f=f[0].length-c.prefix(f)-c.suffix(f);e.push({start:a.constant(g),finish:a.constant(g+f)});b.lastIndex=g+f;f=b.exec(d)}return e}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.search.Sleuth",["ephox.compass.Arr",
"ephox.highway.Merger","ephox.polaris.search.Find","global!Array"],function(a,d,c,b){var e=function(a){a=b.prototype.slice.call(a,0);a.sort(function(a,b){return a.start()<b.start()?-1:b.start()<a.start()?1:0});return a};return{search:function(b,g){var l=a.bind(g,function(e){var g=c.all(b,e.pattern());return a.map(g,function(a){return d.merge(e,{start:a.start,finish:a.finish})})});return e(l)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.polaris.api.Search",
["ephox.polaris.search.Find","ephox.polaris.search.Sleuth"],function(a,d){return{findall:function(c,b){return a.all(c,b)},findmany:function(a,b){return d.search(a,b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.search.Searcher","ephox.compass.Arr ephox.perhaps.Option ephox.phoenix.api.data.NamedPattern ephox.phoenix.api.data.Spot ephox.phoenix.api.general.Family ephox.phoenix.extract.TypedList ephox.phoenix.search.MatchSplitter ephox.polaris.api.Pattern ephox.polaris.api.PositionArray ephox.polaris.api.Search".split(" "),
function(a,d,c,b,e,f,g,l,h,k){var p=function(a,c){return h.generate(c,function(c,e){var f=e+a.property().getText(c).length;return d.from(b.range(c,e,f))})},q=function(b,c,d,l){c=e.group(b,c,l);return a.bind(c,function(c){c=f.justText(c);var e=a.map(c,b.property().getText).join(""),e=k.findmany(e,d);c=p(b,c);return g.separate(b,c,e)})};return{safeWords:function(b,d,e,f){e=a.map(e,function(a){var b=l.safeword(a);return c(a,b)});return q(b,d,e,f)},safeToken:function(a,b,d,e){d=c(d,l.safetoken(d));return q(a,
b,[d],e)},run:q}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.api.general.Search",["ephox.phoenix.search.Searcher"],function(a){return{safeWords:function(d,c,b,e){return a.safeWords(d,c,b,e)},safeToken:function(d,c,b,e){return a.safeToken(d,c,b,e)},run:function(d,c,b,e){return a.run(d,c,b,e)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.phoenix.api.dom.DomSearch",["ephox.boss.api.DomUniverse",
"ephox.phoenix.api.general.Search"],function(a,d){var c=a();return{safeWords:function(a,e,f){return d.safeWords(c,a,e,f)},safeToken:function(a,e,f){return d.safeToken(c,a,e,f)},run:function(a,e,f){return d.run(c,a,e,f)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sugar.api.SelectorExists",["ephox.sugar.api.SelectorFind"],function(a){return{any:function(d){return a.first(d).isSome()},ancestor:function(d,c,b){return a.ancestor(d,c,b).isSome()},
sibling:function(d,c){return a.sibling(d,c).isSome()},child:function(d,c){return a.child(d,c).isSome()},descendant:function(d,c){return a.descendant(d,c).isSome()},closest:function(d,c,b){return a.closest(d,c,b).isSome()}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.limbo.process.Preprocessor","ephox.compass.Arr ephox.perhaps.Option ephox.phoenix.api.dom.DomSearch ephox.polaris.api.Pattern ephox.scullion.Struct ephox.sugar.api.Attr ephox.sugar.api.Css ephox.sugar.api.Element ephox.sugar.api.Html ephox.sugar.api.Insert ephox.sugar.api.InsertAll ephox.sugar.api.Node ephox.sugar.api.SelectorExists".split(" "),
function(a,d,c,b,e,f,g,l,h,k,p,q,w){var t=function(a){var d=e.immutable("word","pattern"),f=b.unsafetoken("((([A-Za-z]{3,9}:(?:\\/\\/)?)(?:[\\-;:&=\\+\\$,\\w]+@)?[A-Za-z0-9\\.\\-]+|(?:www\\.|[\\-;:&=\\+\\$,\\w]+@)[A-Za-z0-9\\.\\-]+)(:[0-9]+)?((?:\\/[\\+~%\\/\\.\\w\\-_]*)?\\??(?:[\\-\\+=&;%@\\.\\w_]*)#?(?:[\\.\\!\\/\\\\\\w]*))?)"),d=d("__INTERNAL__",f);return c.run(a,[d])},u=function(a){return!w.closest(a,"a")},v=function(a){return d.from(a[0]).filter(u).map(function(b){var c=l.fromTag("a");k.before(b,
c);p.append(c,a);f.set(c,"href",h.get(c));return c})};return{links:function(b){b=t(b);a.each(b,function(a){0>a.exact().indexOf("@")&&v(a.elements())})},position:function(b){a.each(b,function(a){q.isElement(a)&&g.remove(a,"position")})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.limbo.api.RunPaste","ephox.compass.Arr ephox.limbo.process.PasteFilters ephox.limbo.process.Preprocessor ephox.pastiche.api.HybridAction ephox.sugar.api.Html ephox.sugar.api.Traverse".split(" "),
function(a,d,c,b,e,f){var g=function(b,d){var e=f.children(d);a.each([c.links,c.position],function(a){a(e)})};return{go:function(a,c,f,h,k){g(c,f);f=e.get(f);c=d.derive(k,h,c);return b.go(a,f,c)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.limbo.api.Sources",["ephox.pastiche.api.HybridAction","ephox.sugar.api.Attr","ephox.sugar.api.Html","ephox.sugar.api.PredicateExists"],function(a,d,c,b){var e=function(a){return b.descendant(a,function(a){return d.has(a,
"style")?-1<d.get(a,"style").indexOf("mso-"):!1})},f=function(b){b=c.get(b);return a.isWordContent(b)};return{isWord:function(a,b){var c=a.browser;return(c.isIE()&&11<=c.version.major?e:f)(b)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sloth.data.Range",["ephox.peanut.Fun","ephox.sugar.api.Compare"],function(a,d){return function(c,b,e,f){var g=d.eq(c,e)&&b===f;return{startContainer:a.constant(c),startOffset:a.constant(b),endContainer:a.constant(e),
endOffset:a.constant(f),collapsed:a.constant(g)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sloth.api.BodySwitch",["ephox.sloth.data.Range","ephox.sugar.api.Element","ephox.sugar.api.Insert","ephox.sugar.api.Remove","ephox.sugar.api.Traverse"],function(a,d,c,b,e){return function(f){var g=d.fromTag("br");return{cleanup:function(){b.remove(g)},toOn:function(a,b){a.dom().focus()},toOff:function(b,d){var h;h=e.owner(d).dom().defaultView;
h.focus();c.append(d,g);f.set(h,a(g,0,g,0))}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sloth.api.DivSwitch",["ephox.peanut.Fun"],function(a){return function(){return{toOn:function(a,c){a.dom().focus()},toOff:function(a,c){c.dom().focus()},cleanup:a.identity}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.plumber.tap.control.BlockControl",[],function(){return{create:function(){var a=
!1;return{isBlocked:function(){return a},block:function(){a=!0},unblock:function(){a=!1}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.plumber.tap.wrap.Tapped",[],function(){return{create:function(a,d){return{control:a,instance:d}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.plumber.tap.function.BlockTap",["ephox.plumber.tap.control.BlockControl","ephox.plumber.tap.wrap.Tapped"],
function(a,d){return{tap:function(c){var b=a.create();return d.create(b,function(){b.isBlocked()||c.apply(null,arguments)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sloth.api.Paste",["ephox.fred.PlatformDetection","ephox.peanut.Fun","global!setTimeout"],function(a,d,c){var b=a.detect();a=function(a,b,c){b.control.block();a.dom().execCommand("paste");c();b.control.unblock()};var e=function(a,b,d){c(d,1)},f=(b=b.browser.isIE()&&10>=
b.browser.version.major)?a:e;return{willBlock:d.constant(b),run:function(a,b,c){return f(a,b,c)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sloth.engine.Consolidator","ephox.compass.Arr ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.InsertAll ephox.sugar.api.Remove ephox.sugar.api.Traverse".split(" "),function(a,d,c,b,e,f){var g=function(g,h){var k=f.children(g);a.each(k,function(a){if(h(a)){var g=f.children(a),l=d.fromTag("div",
f.owner(a).dom());b.append(l,g);c.before(a,l);e.remove(a)}})};return{consolidate:function(a,c){f.nextSibling(a).filter(c).each(function(c){var d=f.children(c);b.append(a,d);e.remove(c)});g(a,c)}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sloth.engine.Offscreen","ephox.epithet.Id ephox.scullion.Struct ephox.sloth.engine.Consolidator ephox.sugar.api.Attr ephox.sugar.api.Class ephox.sugar.api.Css ephox.sugar.api.Element ephox.sugar.api.Html ephox.sugar.api.Insert ephox.sugar.api.Remove ephox.sugar.api.SelectorFind ephox.sugar.api.Traverse".split(" "),
function(a,d,c,b,e,f,g,h,k,n,p,q){var w=a.generate("ephox-sloth-bin");return function(a){var s=g.fromTag("div");b.set(s,"contenteditable","true");e.add(s,w);f.setAll(s,{position:"absolute",left:"0px",top:"0px",width:"0px",height:"0px",overflow:"hidden"});var v=function(a){return e.has(a,w)};return{attach:function(a){n.empty(s);k.append(a,s)},focus:function(){p.ancestor(s,"body").each(function(b){a.toOff(b,s)})},contents:function(){c.consolidate(s,v);var a=d.immutable("elements","html","container"),
b=q.children(s),e=h.get(s);return a(b,e,s)},container:function(){return s},detach:function(){n.remove(s)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.sloth.api.ProxyPaste","ephox.peanut.Fun ephox.plumber.tap.function.BlockTap ephox.porkbun.Event ephox.porkbun.Events ephox.sloth.api.Paste ephox.sloth.engine.Offscreen ephox.sugar.api.Traverse".split(" "),function(a,d,c,b,e,f,g){return function(h,k){var n=f(h),p=function(){h.cleanup();
var a=n.contents();n.detach();w.trigger.after(a.elements(),a.html(),n.container())},q=d.tap(function(){w.trigger.before();n.attach(k);n.focus();e.run(g.owner(k),q,p)}),w=b.create({before:c([]),after:c(["elements","html","container"])}),t=a.noop;return{instance:a.constant(function(){q.instance()}),destroy:t,events:w.registry}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.pastiche.Pastiche","ephox.cement.api.CementConstants ephox.cement.pastiche.IeBlob ephox.compass.Arr ephox.fred.PlatformDetection ephox.fussy.api.WindowSelection ephox.limbo.api.RunPaste ephox.limbo.api.Sources ephox.peanut.Fun ephox.perhaps.Option ephox.porkbun.Event ephox.porkbun.Events ephox.sloth.api.BodySwitch ephox.sloth.api.DivSwitch ephox.sloth.api.ProxyPaste ephox.sugar.api.Class ephox.sugar.api.Elements ephox.sugar.api.Node ephox.sugar.api.Remove ephox.sugar.api.Traverse global!console global!setTimeout".split(" "),
function(a,d,c,b,e,f,g,h,k,n,p,q,w,t,u,v,x,y,A,z,B){var D=b.detect();return function(b,C,I,K){var E=p.create({paste:n(["elements","assets"]),error:n(["message"])}),J=(D.browser.isIE()&&"body"!==x.name(C)?w:q)({set:function(a,b){e.setExact(a,b.startContainer(),b.startOffset(),b.endContainer(),b.endOffset())}}),L=A.owner(C),H=t(J,C),F=k.none();H.events.after.bind(function(d){var e=d.container();J.toOn(C,e);b(e);u.add(e,a.binStyle());var n=g.isWord(D,e),p=function(a){a=!0===(n&&a.mergeOfficeStyles)||
!n&&!0===a.mergeHtmlStyles;try{var b=f.go(L,D,e,a,n);if(void 0!==b&&null!==b&&0<b.length){var c=v.fromHtml(b);F.fold(function(){E.trigger.paste(c,[])},function(a){a.get(function(a){E.trigger.paste(c,a)})});F=k.none()}else E.trigger.paste([],[])}catch(d){z.error(d),E.trigger.error("errors.paste.process.failure")}};d=h.curry(I.get,n,p);(void 0===K||n?k.none():K.findClipboardTags(A.children(e))).fold(d,function(a){c.each(a,y.remove);B(function(){p({mergeHtmlStyles:!0})},0)})});return{handler:function(a){try{F=
d.convert(a),H.instance()()}catch(b){z.error(b),E.trigger.error("errors.paste.process.failure")}},isSupported:h.constant(!0),events:E.registry,destroy:function(){H.destroy()}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.smartpaste.PasteHandlers","ephox.cement.html.HtmlPaste ephox.cement.images.ImagePaste ephox.cement.pastiche.Pastiche ephox.porkbun.Event ephox.porkbun.Events ephox.scullion.Struct ephox.violin.Strings".split(" "),
function(a,d,c,b,e,f,g){var h=f.immutable("captured");return function(f,k,p,q){var w=q.intraFlag,t=a(p,q),u=d(q),v=c(f,k,p,w),x=e.create({paste:b(["elements","assets"]),error:b(["message"]),cancel:b([])});f=function(a){a.events.error.bind(function(a){x.trigger.error(a.message())});a.events.paste.bind(function(a){var b=a.elements();a=a.assets();0===b.length&&0===a.length?x.trigger.cancel():x.trigger.paste(b,a)})};f(t);f(u);f(v);k=function(a,b){var c=u.handler(a.clipboardData.items);return h(c)};f=
function(a,b){v.handler(a);return h(!1)};k={html:function(a,b){var c=a.clipboardData.getData(b);g.contains(c,"<html")&&(g.contains(c,'xmlns:o="urn:schemas-microsoft-com:office:office"')||g.contains(c,'xmlns:x="urn:schemas-microsoft-com:office:excel"'))?c=t.handler(c):(v.handler(a),c=!1);return h(c)},image:u.isSupported()?k:f,files:u.isSupported()?k:f,fallback:f};void 0!==w&&(k[w.clipboardType()]=f);return{events:x.registry,handlers:k,destroy:function(){v.destroy()}}}})})(a.bolt.module.api.define,
a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.smartpaste.PasteBroker",["ephox.cement.smartpaste.Inspection","ephox.cement.smartpaste.PasteHandlers","ephox.perhaps.Option","ephox.perhaps.Options","global!RegExp"],function(a,d,c,b,e){var f=["html","image","files"],g={flavor:"fallback"},h=function(d,f){if(!a.isValidData(f))return g;var h=f.types;return b.findMap(d,function(a){var d=new e(a,"i");return b.findMap(h,function(b){return null!==b.match(d)?c.some({type:b,
flavor:a}):c.none()})}).getOr(g)},k=function(a,b){var c=void 0===a?f:[a.clipboardType()].concat(f);return function(a){var d=h(c,a.clipboardData);(0,b[d.flavor])(a,d.type).captured()&&a.preventDefault()}};return function(a,b,c,e){var f=d(a,b,c,e);a=k(e.intraFlag,f.handlers);return{events:f.events,handlePaste:a,destroy:function(){f.destroy()}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.porkbun.SourceEvent",["ephox.compass.Arr","ephox.peanut.Fun",
"ephox.porkbun.Event"],function(a,d,c){return function(b,e){var f=c(b),g=0,h=function(c){var d=a.map(b,function(a){return c[a]()});f.trigger.apply(null,d)};return{bind:function(a){f.bind(a);g++;1===g&&e.bind(h)},unbind:function(a){f.unbind(a);g--;0===g&&e.unbind(h)},trigger:d.die("Cannot trigger a source event.")}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.cement.api.Cement","ephox.cement.flash.Flash ephox.cement.smartpaste.MergeSettings ephox.cement.smartpaste.PasteBroker ephox.limbo.api.RtfImage ephox.plumber.tap.function.BlockTap ephox.porkbun.Event ephox.porkbun.Events ephox.porkbun.SourceEvent ephox.sloth.api.Paste ephox.sugar.api.Element ephox.sugar.api.InsertAll".split(" "),
function(a,d,c,b,e,f,g,h,k,n,p){return function(q,w,t,u){var v=a(w,u);w=d(w,u);var x=c(t,q,w,{baseUrl:u.baseUrl,allowLocalImages:u.allowLocalImages,intraFlag:u.intraFlag}),y=g.create({cancel:h([],w.events.cancel),error:f(["message"]),insert:f(["elements","assets"])}),A=function(a){z.control.unblock();y.trigger.insert(a.elements(),a.assets())};v.events.insert.bind(A);var z=e.tap(function(a){k.willBlock()&&(z.control.block(),a.preventDefault());x.handlePaste(a);z.control.isBlocked()&&a.preventDefault()});
w.events.open.bind(z.control.block);w.events.close.bind(z.control.unblock);x.events.paste.bind(function(a){var c=a.elements(),d=n.fromTag("div");p.append(d,c);b.exists(d)?(z.control.block(),v.gordon(d,a.assets())):A(a)});q=function(a){z.control.unblock();y.trigger.error(a.message())};v.events.error.bind(q);x.events.error.bind(q);return{paste:z.instance,isBlocked:z.control.isBlocked,destroy:function(){x.destroy()},events:y.registry}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);
(function(a,h,k){a("ephox.powerpaste.settings.Defaults",[],function(){return{officeStyles:"prompt",htmlStyles:"clean"}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.styles.Styles","ephox.sugar.api.Attr ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.Remove ephox.sugar.api.SelectorExists ephox.sugar.api.SelectorFind global!document".split(" "),function(a,d,c,b,e,f,g){return{injectStyles:function(b){if(!e.any("#powerpaste-styles")){b=
d.fromHtml("<style>.ephox-cement-flashbin-helpcopy-kbd {font-size: 3em !important; text-align: center !important; vertical-align: middle !important; margin: .5em 0} .ephox-cement-flashbin-helpcopy-kbd .ephox-polish-help-kbd {font-size: 3em !important; vertical-align: middle !important;} .ephox-cement-flashbin-helpcopy a {text-decoration: underline} .ephox-cement-flashbin-loading-spinner {background-image: url("+b+") !important; width: 96px !important; height:96px !important; display: block; margin-left: auto !important; margin-right: auto !important; margin-top: 2em !important; margin-bottom: 2em !important;} .ephox-cement-flashbin-loading p {text-align: center !important; margin: 2em 0 !important} .ephox-cement-flashbin-target {height: 1px !important;} .ephox-cement-flashbin-target.ephox-cement-flash-activate {height: 150px !important; width: 100% !important;} .ephox-cement-flashbin-target object {height: 1px !important;} .ephox-cement-flashbin-target.ephox-cement-flash-activate object {height: 150px !important; width: 100% !important;} </style>");
a.set(b,"type","text/css");a.set(b,"id","powerpaste-styles");var g=f.first("head").getOrDie("Head element could not be found.");c.append(g,b)}},removeStyles:function(){if(e.any("#powerpaste-styles")){var a=f.first("head").getOrDie("Head element could not be found."),a=f.descendant(a,"#powerpaste-styles").getOrDie("The style element could not be removed");b.remove(a)}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.tinymce.ModernTinyDialog",
"ephox.porkbun.Event ephox.porkbun.Events ephox.powerpaste.util.NodeUtil ephox.sugar.api.Attr ephox.sugar.api.Element ephox.sugar.api.Insert ephox.sugar.api.Remove ephox.sugar.api.SelectorFind".split(" "),function(a,d,c,b,e,f,g,h){return function(k){return{createDialog:function(){var n,p="",q="",w=[],t=null,u=d.create({close:a([])}),v=function(a){u.trigger.close()},x=function(){n.off("close",v);n.close("close")};return{events:u.registry,setTitle:function(a){p=a},setContent:function(a){q=[{type:"container",
html:c.nodeToString(a.dom())}];t=a},setButtons:function(a){var b=[];a.forEach(function(a,c,d){b.push({text:a.text,onclick:a.click})});w=b},show:function(){0===w.length&&(w=[{text:"Close",onclick:function(){n.close()}}]);n=k.windowManager.open({title:p,spacing:10,padding:10,items:q,buttons:w});var a=e.fromDom(n.getEl()),a=h.descendant(a,"."+b.get(t,"class")).getOrDie("We must find this element or we cannot continue");f.before(a,t);g.remove(a);n.on("close",v)},hide:function(){x()},destroy:function(){x()},
reflow:function(){}}}}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.tinymce.ModernPowerPaste","ephox.cement.api.Cement ephox.compass.Arr ephox.peanut.Fun ephox.powerpaste.i18n.I18n ephox.powerpaste.settings.Defaults ephox.powerpaste.styles.Styles ephox.powerpaste.tinymce.ErrorDialog ephox.powerpaste.tinymce.ModernTinyDialog ephox.powerpaste.util.NodeUtil ephox.sugar.api.DomEvent ephox.sugar.api.Element global!setTimeout global!tinymce".split(" "),
function(a,d,c,b,e,f,g,h,k,n,p,q,w){return function(t,u,v,x){var y,A,z,B,D;D=(v?v.swfUrl:u)+"/js";A=(v?v.swfUrl:u)+"/flash/textboxpaste.swf";z=(v?v.imgUrl:u)+"/img/spinner_96.gif";B=(v?v.cssUrl:u)+"/css/editorcss.css";t.on("init",function(u){f.injectStyles(z);t.dom.loadCSS(B);u={baseUrl:D,swf:A,officeStyles:t.settings.powerpaste_word_import||e.officeStyles,htmlStyles:t.settings.powerpaste_html_import||e.htmlStyles,translations:b.translate,allowLocalImages:t.settings.powerpaste_allow_local_images};
var v=h(t),w=p.fromDom(t.getBody()),C=a(w,v.createDialog,c.noop,u);C.events.cancel.bind(function(){y=null});C.events.error.bind(function(a){y=null;g.showDialog(t,b.translate(a.message()))});C.events.insert.bind(function(a){var b=d.map(a.elements(),function(a){return k.nodeToString(a.dom())}).join("");if(t.hasEventListeners("PastePostProcess")){var c=t.dom.add(t.getBody(),"div",{style:"display:none"},b),b=t.fire("PastePostProcess",{node:c}).node.innerHTML;t.dom.remove(c)}t.focus();q(function(){t.selection.moveToBookmark(y);
y=null;t.undoManager.transact(function(){t.insertContent(b,{merge:!1!==t.settings.paste_merge_formats});x.prepareImages(a.assets())});x.uploadImages(a.assets())},1)});n.bind(w,"paste",function(a){y||(y=t.selection.getBookmark());C.paste(a.raw());q(function(){if(t.windowManager.windows!==undefined)t.windowManager.windows[0]&&t.windowManager.windows[0].getEl().focus()},1)})});t.on("remove",function(a){1===w.editors.length&&f.removeStyles()})}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,
k){a("ephox.powerpaste.tinymce.TinyPowerPaste",["ephox.powerpaste.imageupload.UploaderFactory","ephox.powerpaste.tinymce.LegacyPowerPaste","ephox.powerpaste.tinymce.ModernPowerDrop","ephox.powerpaste.tinymce.ModernPowerPaste","global!tinymce"],function(a,d,c,b,e){return function(f){return function(g,h){var k=function(){var d=a(g);b(g,h,f,d);g.settings.powerpaste_block_drop||c(g,h,f,d)};e.Env.ie&&10>e.Env.ie?d(g,f):k();var n=function(a){g.dom.bind(a,"drop dragstart dragend dragover dragenter dragleave dragdrop draggesture",
function(a){return e.dom.Event.cancel(a)})};if(g.settings.powerpaste_block_drop)g.on("init",function(a){n(g.getBody());n(g.getDoc())});if(g.settings.paste_postprocess)g.on("PastePostProcess",function(a){g.settings.paste_postprocess.call(this,this,a)})}}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);(function(a,h,k){a("ephox.powerpaste.PowerPastePlugin",["ephox.powerpaste.tinymce.TinyPowerPaste","global!tinymce"],function(a,d){return function(c){d.PluginManager.requireLangPack("powerpaste",
"ar,ca,cs,da,de,el,es,fa,fi,fr_FR,he_IL,hr,hu_HU,it,ja,kk,ko_KR,nb_NO,nl,pl,pt_BR,pt_PT,ro,ru,sk,sl_SI,sv_SE,th_TH,tr,uk,zh_CN,zh_TW");d.PluginManager.add("powerpaste",a(c))}})})(a.bolt.module.api.define,a.bolt.module.api.require,a.bolt.module.api.demand);I("ephox.powerpaste.PowerPastePlugin")();this.ephox&&this.ephox.bolt&&(this.ephox.bolt=S)})();
