.multi-upload-modal {
  position: fixed;
  z-index: 1000;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.22);
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-content.outlook-style {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 8px 40px rgba(0,0,0,0.22);
  padding: 2.3em 2.2em 1.7em 2.2em;
  min-width: 350px;
  max-width: 98vw;
  min-height: 220px;
  max-height: 96vh;
  overflow-y: auto;
  position: relative;
}
.toggle-switch-row {
  display: flex; align-items: center; gap: 1.2em; margin-bottom: 1.1em;
}
.toggle-label-active {
  color: #1976d2;
  font-weight: 500;
}
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 22px;
}
.toggle-switch input {
  opacity: 0; width: 0; height: 0;
}
.toggle-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0; left: 0; right: 0; bottom: 0;
  background: #ccc;
  border-radius: 22px;
  transition: .3s;
}
.toggle-switch input:checked + .slider {
  background: #1976d2;
}
.toggle-switch .slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.2s;
}
.toggle-switch input:checked + .slider:before {
  transform: translateX(24px);
}
.simple-upload-block {
  display: flex; flex-direction: column; align-items: flex-start; gap: 0.5em;
}
.simple-file-list {
  margin: 0.5em 0 0 0;
}
.multi-file-upload-panel {
  border: 2px dashed #bbb;
  border-radius: 8px;
  padding: 24px;
  background: #fafbfc;
  transition: border-color 0.2s, background 0.2s;
  cursor: pointer;
  margin-bottom: 0.5em;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.multi-file-upload-panel.drag-over {
  border-color: #1976d2;
  background: #e3f2fd;
}
.panel-drop-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  width: 100%;
}
.panel-drop-icon {
  margin-bottom: 8px;
}
.panel-drop-text {
  color: #666;
  font-size: 1.1em;
}
.panel-drop-link {
  color: #1976d2;
  text-decoration: underline;
  cursor: pointer;
}
.dragdrop-file-list {
  margin: 1em 0 0 0; width: 100%;
}
.outlook-btn {
  font-family: inherit;
  font-size: 1rem;
  border-radius: 5px;
  padding: 0.5em 1.3em;
  border: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.outlook-btn-primary {
  background: #0078d4;
  color: #fff;
  font-weight: 500;
}
.outlook-btn-primary:hover {
  background: #005fa3;
}
.outlook-btn-secondary {
  background: #f3f2f1;
  color: #323130;
  border: 1px solid #c7c7c7;
}
.outlook-btn-secondary:hover {
  background: #e1dfdd;
}
.outlook-file-list {
  padding: 0; list-style: none;
}
.outlook-file-item {
  display: flex; align-items: center; gap: 0.5em;
  padding: 0.3em 0.5em;
  border-radius: 4px;
  background: #f8f8f8;
  margin-bottom: 0.3em;
  font-size: 0.97rem;
  position: relative;
}
.outlook-file-icon {
  width: 18px; height: 18px; 
  display: flex; justify-content: center; align-items: center;
}
.file-thumb {
  width: 24px;
  height: 24px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
  background: #fff;
}
.file-name {
  flex: 1 1 auto;
  overflow: hidden; text-overflow: ellipsis; white-space: nowrap;
}
.file-type-label {
  color: #888;
  font-size: 0.93em;
  margin-left: 0.5em;
  font-family: 'Segoe UI', Arial, sans-serif;
  letter-spacing: 0.02em;
}
.file-size {
  color: #888; font-size: 0.92em; margin-left: 0.5em;
}
.remove-file-btn {
  background: none; border: none; color: #d13438; font-size: 1.2em; margin-left: 0.5em; cursor: pointer; padding: 0 0.3em; border-radius: 2px; transition: background 0.1s;
}
.remove-file-btn:hover {
  background: #fbe4e6;
}
.thumb-tooltip-wrap {
  position: relative;
  display: inline-block;
}
.thumb-tooltip {
  position: absolute;
  left: 48px;
  top: 0;
  max-width: calc(100vw - 80px);
  z-index: 100;
  pointer-events: none;
  transition: opacity 0.18s, transform 0.18s;
  opacity: 0;
  transform: scale(0.95);
}
.thumb-tooltip[aria-visible="true"] {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}
.thumb-tooltip::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 24px;
  border-width: 8px 10px 8px 0;
  border-style: solid;
  border-color: transparent #fff transparent transparent;
  filter: drop-shadow(-2px 0 2px rgba(0,0,0,0.10));
}
.thumb-large {
  max-width: 240px;
  max-height: 240px;
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.18);
  background: #fff;
  padding: 4px;
  z-index: 10;
  display: block;
}
.thumb-caption {
  position: absolute;
  left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.55);
  color: #fff;
  font-size: 0.93em;
  padding: 4px 8px;
  border-radius: 0 0 6px 6px;
  text-align: center;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@media (max-width: 600px) {
  .thumb-large {
    max-width: 160px;
    max-height: 160px;
  }
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.18s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
.progress-bar-wrap {
  margin-top: 0.24em;
  display: flex;
  align-items: center;
  gap: 0.5em;
}
.progress-bar-bg {
  width: 80px;
  height: 8px;
  background: #f3f2f1;
  border-radius: 5px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}
.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #0078d4 70%, #55aaff 100%);
  border-radius: 5px;
  transition: width 0.23s cubic-bezier(.4,1.2,.6,1);
}
.progress-label {
  font-size: 0.92em;
  color: #0078d4;
  min-width: 2.2em;
  text-align: right;
}
.upload-error-msg {
  color: #d13438;
  margin-top: 0.7em;
  font-size: 1em;
  background: #fbe4e6;
  border-radius: 5px;
  padding: 0.5em 1em;
}
.outlook-file-item.too-large {
  background: #ffeaea;
  color: #c62828;
  border-left: 4px solid #d13438;
  opacity: 0.95;
}
.outlook-file-item.too-large .file-name,
.outlook-file-item.too-large .file-type-label,
.outlook-file-item.too-large .file-size {
  color: #c62828;
  font-weight: 500;
}
.too-large-label {
  color: #c62828;
  font-size: 0.97em;
  font-weight: 700;
  margin-left: 8px;
  vertical-align: middle;
}
.too-large-icon {
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
}
.outlook-file-item.valid-file:hover {
  background: #f0f8ff;
}
.spinner, .spinner-large {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1976d2;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: spin 0.9s linear infinite;
  display: inline-block;
  vertical-align: middle;
}
.spinner-large {
  width: 44px;
  height: 44px;
  border-width: 5px;
  margin: 1em auto;
}
@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}
.upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(255,255,255,0.6);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.uploading-msg {
  margin-top: 1em;
  color: #1976d2;
  font-weight: bold;
  font-size: 1.2em;
}
.upload-max-info {
  display: flex;
  align-items: center;
  gap: 0.5em;
  color: #1976d2;
  background: #e3f2fd;
  border-radius: 5px;
  padding: 0.4em 0.8em;
  font-size: 0.98em;
  margin-bottom: 0.8em;
}
.upload-max-info .info-icon {
  display: inline-flex;
  align-items: center;
}

/* Link para limpar arquivos */
.clear-files-link {
  color: #1976d2;
  text-decoration: none;
  font-size: 0.9375rem;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 0.5em 0.75em;
  border-radius: 4px;
  transition: background-color 0.2s ease-in-out;

  &:hover {
    background-color: rgba(25, 118, 210, 0.08);
    text-decoration: none;
  }

  i {
    font-size: 0.9em;
    color: #1976d2;
  }
}
