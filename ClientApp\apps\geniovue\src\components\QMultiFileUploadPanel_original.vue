<template>
    <div v-if="visible" class="multi-upload-modal">
        <div class="modal-content outlook-style">
            <h3>{{ texts.title }}</h3>
            <div v-if="MAX_UPLOAD_SIZE_MB" class="upload-max-info">
                <span class="info-icon" aria-label="info">
                    <i class="fas fa-info-circle"></i>
                </span>
                <span v-html="maximumFileSizeText"></span>
            </div>
            <div :class="['multi-file-upload-panel', { 'drag-over': dragOver }]"
                 @dragover.prevent="onDragOver"
                 @dragleave.prevent="onDragLeave"
                 @drop.prevent="onDrop">
                <div class="panel-drop-area" @click="triggerFileInput">
                    <div class="panel-drop-icon">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 2.5rem;"></i>
                    </div>
                    <div class="panel-drop-text">
                        {{ texts.infoToDrag }} <span class="panel-drop-link">{{ texts.selectFilesLink }}</span>
                    </div>
                </div>
                <input ref="fileInput" type="file" multiple style="display:none" @change="onFileChange" />
                <ul v-if="documList.length" class="outlook-file-list dragdrop-file-list">
                    <li v-for="(docum, idx) in documList" :key="docum.file.name + docum.file.size + idx" class="outlook-file-item" :class="{ 'too-large': docum.tooLarge, 'valid-file': !docum.tooLarge }" @click.stop.prevent="">
                        <span class="outlook-file-icon">
                            <template v-if="isImage(docum.file) && !docum.tooLarge">
                                <span class="thumb-tooltip-wrap">
                                    <img :src="filePreview(docum.file)" alt="preview" class="file-thumb" @mouseover="showPreview(idx)" @mouseleave="hidePreview(idx)" />
                                    <transition name="fade">
                                        <div v-if="previewIndex === idx" class="thumb-tooltip" :aria-visible="previewIndex === idx ? 'true' : 'false'">
                                            <img :src="filePreview(docum.file)" alt="preview" class="thumb-large" />
                                            <div class="thumb-caption">{{ docum.file.name }}</div>
                                        </div>
                                    </transition>
                                </span>
                            </template>
                            <template v-else>
                                <span v-html="fileTypeIcon(docum.file)"></span>
                            </template>
                        </span>
                        <span class="file-name">
                            <span v-if="docum.tooLarge" class="too-large-icon" title="File too large" aria-label="File too large">
                                <i class="fas fa-exclamation-circle text-danger"></i>
                            </span>
                            {{ docum.file.name }}
                            <span v-if="docum.tooLarge" class="too-large-label" :title="`{{ texts.fileTooLarge }} (max ${MAX_UPLOAD_SIZE_MB}MB)`">
                                {{ texts.tooLarge }} (max {{ MAX_UPLOAD_SIZE_MB }}MB)
                            </span>
                        </span>
                        <span class="file-type-label">{{ fileTypeLabel(docum.file) }}</span>
                        <span class="file-size">({{ formatSize(docum.file.size) }})</span>
                        <div v-if="uploading && !docum.tooLarge" class="progress-bar-wrap">
                            <div class="progress-bar-bg">
                                <div class="progress-bar-fill" :style="{width: (progressMap[docum.file.name + docum.file.size] || 0) + '%'}"></div>
                            </div>
                            <span class="progress-label">{{ Math.round(progressMap[docum.file.name + docum.file.size] || 0) }}%</span>
                        </div>
                        <button class="remove-file-btn" @click.stop="removeFile(idx)" :title="docum.tooLarge ? texts.removeInvalidFile : texts.removeFile" :aria-label="docum.tooLarge ? texts.removeInvalidFile : texts.removeFile">&times;</button>
                    </li>
                </ul>
            </div>
            <div style="margin-top: 1.5em; display: flex; justify-content: flex-start; gap: 0.5em; flex-wrap: wrap; align-items: center;">
                <q-button 
                    @click="inserirVarios"
                    :disabled="uploading || !documList.length"
                    id="bottom-save-btn"
                    class="q-button q-button--primary q-button--bold"
                    :variant="'primary'"
                    :label="!uploading ? texts.save : texts.uploadingDots"
                    bold>
                    <q-icon icon="save" />
                    <i v-if="uploading" class="fas fa-spinner fa-spin"></i>
                </q-button>
                <q-button 
                    @click.stop.prevent="handleCancel"
                    :disabled="uploading"
                    id="bottom-cancel-btn"
                    class="q-button q-button--outlined q-button--primary"
                    :variant="'primary'"
                    :label="texts.cancel"
                    outlined>
                    <q-icon icon="cancel" />
                </q-button>
                <div v-if="documList.length > 0" style="margin-left: 1.5em; display: flex; align-items: center;">
                    <button @click.prevent="clearAllFiles"
                           class="q-button q-button--link">
                        <span class="q-button__content">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="trash" viewBox="0 0 24 24" class="q-icon q-icon__svg">
                                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"></path>
                            </svg>
                            {{ texts.clearAllFiles }}
                        </span>
                    </button>
                </div>
            </div>
            <div v-if="uploadResults && uploadResults.length" class="upload-results">
                <ul class="outlook-file-list" style="margin-top:0.8em;">
                    <li v-for="(result, idx) in uploadResults" :key="result.fileName + idx">
                        {{ result.fileName }} 
                        <span v-if="result.success" class="success-icon"><i class="fas fa-check-circle"></i></span>
                        <span v-else class="error-icon"><i class="fas fa-times-circle"></i></span>
                    </li>
                </ul>
            </div>
            <div v-else-if="lastGlobalError" class="error-message" style="font-size:0.96em; margin-top:0.2em; color:#b7472a;">
                {{ lastGlobalError }}
            </div>
            <div v-if="uploading" class="upload-overlay">
                <div class="spinner-large"></div>
                <div class="uploading-msg">Uploading files, please wait…</div>
                <div v-if="typeof currentUploadIdx === 'number' && documList[currentUploadIdx]">
                    <span class="uploading-file-name">
                        {{ texts.uploading }} {{ truncateFileName(documList[currentUploadIdx].file.name, 32) }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
	import { fetchMaxUploadSize, gravarTodosOsDocum } from '@/api/network/documMultiUpload';
	import genericFunctions from '@quidgest/clientapp/utils/genericFunctions'
	import { displayMessage, validateFileExtAndSize, validateImageFormat, validateTexts } from '@quidgest/clientapp/utils/genericFunctions'

    // The texts needed by the component.
    const DEFAULT_TEXTS = {
        title: 'Upload files',
        maximumFileSizeText: 'Maximum file size per upload: ',
        fileSizeError: 'The selected file exceeds the allowed size of {0}.',
        infoToDrag: 'Drag files here or',
        selectFilesLink: 'click to select',
        tooLarge: 'Too large',
        fileTooLarge: 'File too large',
        removeInvalidFile: 'Remove invalid file',
        removeFile: 'Remove file',
        uploading: 'Uploading:',
        uploadingDots: 'Uploading...',
        clearAllFiles: 'Clear all files',
        save: 'Save',
        cancel: 'Cancel',
        yesLabel: 'Yes',
        noLabel: 'No',
        confirmExitMessage: 'Are you sure you want to leave? Unsent files will be lost.',
    }

    export default {
        name: 'QMultiFileUploadPanel',
        mixins: [genericFunctions],
        props: {
            visible: Boolean,
            parentId: String,
            table: String,
            parentTable: String,
            ticket: String,
            Resources: {
                type: Object,
                required: true
            },
            /**
             * Necessary strings to be used in labels and buttons.
             */
            texts: {
                type: Object,
                validator: (value) => validateTexts(DEFAULT_TEXTS, value),
                default: () => DEFAULT_TEXTS
            }
        },
        emits: ['close', 'multi-files-selected', 'multi-files-uploaded'],
        data() {
            return {
                files: [],
                uploading: false,
                progressMap: {},
                previewIndex: null,
                dragOver: false,
                lastGlobalError: null,
                lastGlobalSuccess: null,
                documList: [],
                uploadResults: [],
                MAX_UPLOAD_SIZE_MB: null, // agora dinâmico
                MAX_UPLOAD_SIZE: null, // em bytes
                currentUploadIdx: null // índice do ficheiro em progresso
            };
        },
        methods: {
            async handleCancel() {
                if (this.documList.length > 0) {
                    const buttons = {
                        confirm: {
                            label: this.texts.yesLabel,
                            action: () => this.closePanel()
                        },
                        cancel: {
                            label: this.texts.noLabel,
                            action: () => null
                        }
                    };

                    displayMessage(this.texts.confirmExitMessage, 'question', null, buttons);
                    return;
                }
                this.closePanel();
            },

            closePanel() {
                console.log('Fechando o painel...');
                this.$emit('close');
                this.files = [];
                this.dragOver = false;
                this.progressMap = {};
                this.uploadResults = [];
                this.uploadError = null;
                this.lastGlobalError = null;
                this.documList = [];
                console.log('Painel fechado com sucesso');
            },

            close() {
                console.log('Método close chamado');
                this.handleCancel();
            },

            onDragLeave() {
                this.dragOver = false;
            },

            onDrop(e) {
                this.dragOver = false;
                const files = Array.from(e.dataTransfer.files);
                if (files.length) {
                    this.onFileChange({ target: { files } });
                }
            },

            triggerFileInput() {
                this.$refs.fileInput && this.$refs.fileInput.click();
            },

            async onFileChange(e) {
                // Só permite seleção se já temos o valor do limite
                if (!this.MAX_UPLOAD_SIZE) return;
                const files = Array.from(e.target.files || []);
                // Aceita todos os ficheiros válidos (nome e tamanho), independentemente de extensão ou type
                const validFiles = files.filter(f =>
                    f &&
                    typeof f.name === 'string' &&
                    typeof f.size === 'number' &&
                    f.name // nome não vazio
                );
                const existing = this.documList.map(d => d.file && d.file.name + d.file.size);
                const novos = validFiles.filter(f =>
                    !existing.includes(f.name + f.size)
                );
                if (novos.length === 0) return;
                this.documList = this.documList.concat(
                    novos.map(file => ({
                        file,
                        tooLarge: file.size > this.MAX_UPLOAD_SIZE,
                        nome: file.name,
                        descricao: '',
                        parentId: this.parentId,
                        parentTable: this.parentTable
                    }))
                );
            },

            isImage(file) {
                return file && file.type && file.type.startsWith('image/');
            },

            isPdf(file) {
                return file && file.type === 'application/pdf';
            },

            isAudio(file) {
                return file && file.type && file.type.startsWith('audio/');
            },

            isVideo(file) {
                return file && file.type && file.type.startsWith('video/');
            },

            filePreview(file) {
                if (!file) return '';
                if (file.type && file.type.startsWith('image/')) {
                    return URL.createObjectURL(file);
                }
                return '';
            },

            fileTypeIcon(file) {
                if (!file || typeof file.name !== 'string') return '';
                const ext = file.name.split('.').pop().toLowerCase();
                const map = {
                    pdf: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#b7472a" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#b7472a" font-family="Segoe UI, Arial" font-weight="bold">PDF</text></svg>`,
                    doc: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#1976d2" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#1976d2" font-family="Segoe UI, Arial" font-weight="bold">DOC</text></svg>`,
                    docx: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#1976d2" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#1976d2" font-family="Segoe UI, Arial" font-weight="bold">DOCX</text></svg>`,
                    xls: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#217346" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#217346" font-family="Segoe UI, Arial" font-weight="bold">XLS</text></svg>`,
                    xlsx: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#217346" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#217346" font-family="Segoe UI, Arial" font-weight="bold">XLSX</text></svg>`,
                    ppt: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#b7472a" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#b7472a" font-family="Segoe UI, Arial" font-weight="bold">PPT</text></svg>`,
                    pptx: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#b7472a" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#b7472a" font-family="Segoe UI, Arial" font-weight="bold">PPTX</text></svg>`,
                    txt: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#605e5c" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#605e5c" font-family="Segoe UI, Arial" font-weight="bold">TXT</text></svg>`,
                    zip: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#b8860b" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#b8860b" font-family="Segoe UI, Arial" font-weight="bold">ZIP</text></svg>`,
                    rar: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#b8860b" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#b8860b" font-family="Segoe UI, Arial" font-weight="bold">RAR</text></svg>`,
                    csv: `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#217346" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#217346" font-family="Segoe UI, Arial" font-weight="bold">CSV</text></svg>`
                };
                if (map[ext]) return map[ext];
                if (file.type && file.type.startsWith('audio/')) return `<svg width="20" height="20" viewBox="0 0 24 24"><circle cx="10" cy="12" r="7" fill="#fff" stroke="#0078d4" stroke-width="2"/><rect x="13" y="8" width="7" height="8" rx="1.5" fill="#0078d4"/></svg>`;
                if (file.type && file.type.startsWith('video/')) return `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#fff" stroke="#0078d4" stroke-width="2"/><polygon points="8,7 16,12 8,17" fill="#0078d4"/></svg>`;
                return `<svg width="20" height="20" viewBox="0 0 24 24"><rect width="20" height="24" rx="3" fill="#f3f2f1" stroke="#605e5c" stroke-width="2"/><text x="10" y="16" text-anchor="middle" font-size="10" fill="#605e5c" font-family="Segoe UI, Arial" font-weight="bold">FILE</text></svg>`;
            },

            fileTypeLabel(file) {
                if (!file || typeof file.name !== 'string') return '';
                const ext = file.name.split('.').pop().toUpperCase();
                if (ext && ext.length <= 5 && /[A-Z0-9]/.test(ext)) return ext;
                if (file.type) {
                    const parts = file.type.split('/');
                    if (parts.length > 1) return parts[1].toUpperCase();
                    return parts[0].toUpperCase();
                }
                return 'FILE';
            },

            formatSize(size) {
                if (typeof size !== 'number' || isNaN(size)) return '';
                return this.formatFileSize(size, 1);
            },

            /**
             * Format file size to human readable format
             * @param {number} bytes - File size in bytes
             * @param {number} [decimals=2] - Number of decimal places
             * @returns {string} Formatted file size with unit
             */
            formatFileSize(bytes, decimals = 2) {
                if (bytes === 0) return '0 Bytes';

                const k = 1024;
                const dm = decimals < 0 ? 0 : decimals;
                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

                const i = Math.floor(Math.log(bytes) / Math.log(k));

                // Ensure we don't go beyond our sizes array
                const unit = sizes[Math.min(i, sizes.length - 1)];
                const value = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

                return `${value} ${unit}`;
            },

            showPreview(idx) {
                this.previewIndex = idx;
            },

            hidePreview(idx) {
                // Só esconde se for o mesmo index
                if (this.previewIndex === idx) this.previewIndex = null;
            },

            async inserirVarios() {
                this.uploading = true;
                this.currentUploadIdx = null;
                try {
                    const documListWithParent = this.documList
                        .filter(item => !item.tooLarge && item.file)
                        .map(item => ({
                            ...item,
                            parentId: this.parentId,
                            parentTable: this.parentTable
                        }));
                    if (documListWithParent.length === 0) {
                        this.lastGlobalError = `No valid file to upload (max ${this.MAX_UPLOAD_SIZE_MB}MB).`;
                        return;
                    }
                    // Upload sequencial mostrando nome
                    this.uploadResults = [];
                    for (let i = 0; i < documListWithParent.length; i++) {
                        this.currentUploadIdx = this.documList.findIndex(d => d.file && d.file.name === documListWithParent[i].file.name && d.file.size === documListWithParent[i].file.size);
                        const result = await gravarTodosOsDocum([documListWithParent[i]]);
                        if (Array.isArray(result)) {
                            this.uploadResults.push(...result);
                        } else {
                            this.uploadResults.push(result);
                        }
                    }
                    this.$emit('close');
                    this.$emit('uploaded');
                } finally {
                    this.uploading = false;
                    this.currentUploadIdx = null;
                }
            },

            truncateFileName(name, maxLen) {
                if (!name) return '';
                return name.length > maxLen ? name.slice(0, maxLen - 1) + '…' : name;
            },

            removeFile(idx) {
                this.documList.splice(idx, 1);
            },

            clearAllFiles() {
                this.documList = [];
                this.uploadResults = [];
                this.previewIndex = null;
                // Opcional: limpa também os progressos
                this.progressMap = {};
                // Limpa também a mensagem de erro global
                this.lastGlobalError = null;
                // Limpa o input de ficheiros para permitir nova seleção
                if (this.$refs.fileInput) this.$refs.fileInput.value = '';
                this.currentUploadIdx = null;
            }
        },
        async mounted() {
            try {
                const maxMB = await fetchMaxUploadSize();
                this.MAX_UPLOAD_SIZE_MB = maxMB;
                this.MAX_UPLOAD_SIZE = maxMB * 1024 * 1024;
            } catch (e) {
                // fallback seguro
                this.MAX_UPLOAD_SIZE_MB = 28;
                this.MAX_UPLOAD_SIZE = 28 * 1024 * 1024;
            }
            // Add Escape key handler
            this.handleEscapeKey = (event) => {
                if (event.key === 'Escape' && !this.uploading) {
                    this.handleCancel();
                }
            };
            document.addEventListener('keydown', this.handleEscapeKey);

            // Adiciona um event listener direto para depuração
            const cancelButton = document.getElementById('cancel-button');
            if (cancelButton) {
                cancelButton.addEventListener('click', (e) => {
                    this.handleCancel();
                });
            }
        },
        beforeUnmount() {
            // Clean up the event listener when component is destroyed
            if (this.handleEscapeKey) {
                document.removeEventListener('keydown', this.handleEscapeKey);
            }
        },
        computed: {
            /**
             * Returns the formatted text with the maximum file size
             */
            maximumFileSizeText() {
                const maxSizeInBytes = this.MAX_UPLOAD_SIZE_MB * 1024 * 1024; // Convert MB to bytes
                const formattedSize = this.formatFileSize(maxSizeInBytes);
                return `${this.texts.maximumFileSizeText} <strong>${formattedSize}</strong>`;
            }
        }
    };
</script>

<style>
/* Estilos mínimos adaptados para usar as cores e aparência padrão da aplicação */
.multi-upload-modal {
  position: fixed;
  z-index: 1050; /* Usando z-index padrão de modais do Bootstrap */
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.5); /* Cor de fundo padrão de modais */
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  /* Usando exatamente as mesmas propriedades de modal-content do Bootstrap */
  position: relative;
  display: flex;
  flex-direction: column;
  width: 600px;
  background-color: var(--white, #fff); 
  background-clip: padding-box;
  border: 1px solid var(--modal-border-color, rgba(0, 0, 0, 0.2));
  border-radius: var(--modal-border-radius, 0.3rem);
  padding: 1rem;
  max-width: 95vw;
  max-height: 95vh;
  overflow-y: auto;
  min-width: 500px;
  box-shadow: var(--modal-box-shadow, 0 0.5rem 1rem rgba(0, 0, 0, 0.15));
}

/* Área de upload adaptada para usar as cores padrão */
.multi-file-upload-panel {
  border: 2px dashed var(--border-color, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  padding: 1.5rem;
  text-align: center;
  margin: 1rem 0;
  cursor: pointer;
}

.multi-file-upload-panel.drag-over {
  border-color: var(--primary, #007bff);
  background-color: var(--light, #f8f9fa);
}

/* Lista de arquivos */
.outlook-file-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.outlook-file-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.35rem 0.5rem;
  margin-bottom: 0.3rem;
  border-radius: var(--border-radius, 0.25rem);
  background-color: var(--light, #f8f9fa);
}

/* Ajustes adicionais de estilo para melhor integração */
.panel-drop-icon i {
  color: var(--primary, #007bff);
}

.panel-drop-link {
  color: var(--primary, #007bff);
  text-decoration: underline;
  cursor: pointer;
}

.info-icon i {
  color: var(--info, #17a2b8);
}

/* Ajustando estilo do botão de remover para combinar */
.remove-file-btn {
  background: none;
  border: none;
  color: var(--danger, #dc3545);
  cursor: pointer;
  font-size: 1.2rem;
}

/* Estilo para os indicadores de sucesso/falha consistentes com o Bootstrap */
.upload-results .success-icon {
  color: var(--success, #28a745);
}

.upload-results .error-icon {
  color: var(--danger, #dc3545);
}
</style>
