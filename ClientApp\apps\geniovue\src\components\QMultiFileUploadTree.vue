<template>
	<div class="multi-file-upload-tree">
		<!-- Mensagem se não há dados -->
		<div v-if="!treeData || treeData.length === 0"
			 style="text-align: center; padding: 40px; color: #666; background-color: #f8f9fa; border-radius: 6px;">
			<i class="fas fa-folder-open" style="font-size: 48px; margin-bottom: 16px; color: #ddd;"></i>
			<p style="font-size: 16px; margin-bottom: 8px;">Nenhum documento encontrado</p>
			<p style="font-size: 14px; color: #666;">Use "Upload Pasta" para adicionar ficheiros com estrutura de pastas</p>
		</div>

		<!-- Renderização da árvore -->
		<div v-else>
			<!-- Cabeçalho da tabela -->
			<div style="display: grid; grid-template-columns: 1fr 100px 100px 150px; gap: 10px; padding: 12px 10px; background-color: #f8f9fa; border-bottom: 2px solid #dee2e6; font-weight: bold; font-size: 14px;">
				<div>Nome</div>
				<div style="text-align: center;">Tipo</div>
				<div style="text-align: right;">Tamanho</div>
				<div style="text-align: center;">Modificado</div>
			</div>

			<!-- Itens da árvore -->
			<template v-for="item in treeData" :key="item.id">
				<QTreeItem
					:item="item"
					:level="0"
					@toggle-folder="toggleFolder"
					@move-item="handleMoveItem" />
			</template>
		</div>
	</div>
</template>

<script>
import { fetchDocumentTree, moveDocuments, buildTreeFromApiData, countDocuments, countFolders, formatFileSize } from './documMultiUploadTree.js'
import QTreeItem from './QTreeItem.vue'

export default {
	name: 'QMultiFileUploadTree',

	props: {
		parentId: {
			type: String,
			required: true
		},
		parentTable: {
			type: String,
			default: 'DSCPP'
		}
	},

	emits: ['move-success', 'move-error'],

	data() {
		return {
			treeData: [],
			loading: false,

			// Drag and Drop
			draggedItem: null,
			dropTargetItem: null
		};
	},

	computed: {
		/**
		 * Conta o total de documentos (ficheiros) na árvore recursivamente
		 */
		totalDocuments() {
			return countDocuments(this.treeData || []);
		},

		/**
		 * Conta o total de pastas na árvore recursivamente
		 */
		totalFolders() {
			return countFolders(this.treeData || []);
		}
	},

	components: {
		QTreeItem
	},

	methods: {
		/**
		 * Carrega os dados da árvore
		 */
		async loadTreeData() {
			if (!this.parentId) {
				console.warn('parentId não fornecido para carregar árvore');
				return;
			}

			this.loading = true;
			try {
				console.log('Carregando árvore para parentId:', this.parentId);
				const apiData = await fetchDocumentTree(this.parentId);
				this.treeData = buildTreeFromApiData(apiData);
				console.log('Árvore carregada com sucesso:', this.treeData.length, 'itens na raiz');
			} catch (error) {
				console.error('Erro ao carregar árvore:', error);
				this.treeData = [];
			} finally {
				this.loading = false;
			}
		},

		/**
		 * Alterna a expansão de uma pasta
		 */
		toggleFolder(item) {
			if (item.isFolder) {
				item.expanded = !item.expanded;
			}
		},

		/**
		 * Manipula o movimento de itens
		 */
		async handleMoveItem(moveData) {
			const { draggedItem, targetFolder, newRelPath } = moveData;

			console.log('Movendo item:', {
				from: draggedItem.name,
				to: targetFolder.name,
				newPath: newRelPath
			});

			try {
				const response = await moveDocuments([draggedItem.id], newRelPath);

				if (response && response.success) {
					// Recarregar a árvore
					await this.loadTreeData();

					this.$emit('move-success', {
						item: draggedItem.name,
						target: targetFolder.name,
						message: response.message
					});
				} else {
					throw new Error(response?.message || 'Erro desconhecido');
				}
			} catch (error) {
				console.error('Erro ao mover documento:', error);
				this.$emit('move-error', {
					item: draggedItem.name,
					target: targetFolder.name,
					error: error.message
				});
			}
		}
	},

	mounted() {
		// Carregar dados quando o componente é montado
		if (this.parentId) {
			this.loadTreeData();
		}
	},

	watch: {
		parentId: {
			handler(newParentId) {
				if (newParentId) {
					this.loadTreeData();
				}
			}
		}
	}
};
</script>

<style scoped>
.multi-file-upload-tree {
	width: 100%;
}

.hover-row:hover {
	background-color: #f8f9fa !important;
}

.drag-over {
	background-color: #e3f2fd !important;
}
</style>
