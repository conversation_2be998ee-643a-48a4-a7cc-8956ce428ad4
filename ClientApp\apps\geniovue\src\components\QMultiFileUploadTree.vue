<template>
	<div class="multi-file-upload-tree">
		<!-- Mensagem se não há dados -->
		<div v-if="!treeData || treeData.length === 0"
			 style="text-align: center; padding: 40px; color: #666; background-color: #f8f9fa; border-radius: 6px;">
			<i class="fas fa-folder-open" style="font-size: 48px; margin-bottom: 16px; color: #ddd;"></i>
			<p style="font-size: 16px; margin-bottom: 8px;">Nenhum documento encontrado</p>
			<p style="font-size: 14px; color: #666;">Use "Upload Pasta" para adicionar ficheiros com estrutura de pastas</p>
		</div>

		<!-- Renderização da árvore -->
		<div v-else>
			<!-- Cabeçalho da tabela -->
			<div style="display: grid; grid-template-columns: 1fr 100px 100px 150px; gap: 10px; padding: 12px 10px; background-color: #f8f9fa; border-bottom: 2px solid #dee2e6; font-weight: bold; font-size: 14px;">
				<div>Nome</div>
				<div style="text-align: center;">Tipo</div>
				<div style="text-align: right;">Tamanho</div>
				<div style="text-align: center;">Modificado</div>
			</div>

			<!-- Itens da árvore -->
			<template v-for="item in treeData" :key="item.id">
				<TreeItem
					:item="item"
					:level="0"
					@toggle-folder="toggleFolder"
					@move-item="handleMoveItem" />
			</template>
		</div>
	</div>
</template>

<script>
import { fetchDocumentTree, moveDocuments, buildTreeFromApiData, countDocuments, countFolders, formatFileSize } from './documMultiUploadTree.js'

export default {
	name: 'QMultiFileUploadTree',

	props: {
		parentId: {
			type: String,
			required: true
		},
		parentTable: {
			type: String,
			default: 'DSCPP'
		}
	},

	emits: ['move-success', 'move-error'],

	data() {
		return {
			treeData: [],
			loading: false,

			// Drag and Drop
			draggedItem: null,
			dropTargetItem: null
		};
	},

	computed: {
		/**
		 * Conta o total de documentos (ficheiros) na árvore recursivamente
		 */
		totalDocuments() {
			return countDocuments(this.treeData || []);
		},

		/**
		 * Conta o total de pastas na árvore recursivamente
		 */
		totalFolders() {
			return countFolders(this.treeData || []);
		}
	},

	components: {
		// Componente recursivo para renderizar itens da árvore
		TreeItem: {
			name: 'TreeItem',
			props: ['item', 'level'],
			emits: ['toggle-folder', 'move-item'],
			data() {
				return {
					isDragging: false,
					isDropTarget: false
				};
			},
			template: `
				<div>
					<div
						@click="item.isFolder ? $emit('toggle-folder', item) : null"
						:draggable="true"
						@dragstart="handleDragStart"
						@dragover.prevent
						@dragenter.prevent="handleDragEnter"
						@drop.prevent="handleDrop"
						@dragleave="handleDragLeave"
						:style="{
							display: 'grid',
							gridTemplateColumns: '1fr 100px 100px 150px',
							gap: '10px',
							padding: '8px 10px',
							borderBottom: '1px solid #f0f0f0',
							cursor: item.isFolder ? 'pointer' : 'default',
							backgroundColor: item.isFolder && item.expanded ? '#f8f9fa' : (level > 0 ? '#fafafa' : 'transparent'),
							transition: 'all 0.2s ease',
							opacity: isDragging ? 0.5 : 1,
							borderLeft: isDropTarget ? '3px solid #007bff' : 'none'
						}"
						:class="{ 'hover-row': item.isFolder, 'drag-over': isDropTarget }">

						<!-- Coluna Nome -->
						<div :style="{
							paddingLeft: (level * 20) + 'px',
							display: 'flex',
							alignItems: 'center',
							gap: '8px'
						}">
							<!-- Seta de expansão -->
							<i v-if="item.isFolder && item.children && item.children.length > 0"
							   :class="item.expanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"
							   style="font-size: 12px; color: #666; width: 12px;"></i>
							<span v-else style="width: 12px;"></span>

							<!-- Ícone da pasta/ficheiro -->
							<span v-if="item.isFolder && item.expanded" 
							      style="color: #ffc107; margin-right: 8px; font-size: 16px;">📂</span>
							<span v-else-if="item.isFolder" 
							      style="color: #ffc107; margin-right: 8px; font-size: 16px;">📁</span>
							<span v-else 
							      style="color: #6c757d; margin-right: 8px; font-size: 16px;">📄</span>

							<!-- Nome -->
							<span>{{ item.name }}</span>
						</div>

						<!-- Coluna Tipo -->
						<div style="text-align: center;">
							<span v-if="item.isFolder" 
							      style="padding: 2px 8px; border-radius: 12px; font-size: 12px; background-color: #fff3cd; color: #856404;">
								Pasta
							</span>
							<span v-else 
							      style="padding: 2px 8px; border-radius: 12px; font-size: 12px; background-color: #d1ecf1; color: #0c5460;">
								Ficheiro
							</span>
						</div>

						<!-- Coluna Tamanho -->
						<div style="text-align: right;">
							{{ item.isFolder ? '--' : formatFileSize(item.size) }}
						</div>

						<!-- Coluna Modificado -->
						<div style="text-align: center; font-size: 14px; color: #666;">
							{{ item.modified }}
						</div>
					</div>

					<!-- Filhos (recursivo) -->
					<template v-if="item.isFolder && item.expanded && item.children && item.children.length > 0">
						<TreeItem
							v-for="child in item.children"
							:key="child.id"
							:item="child"
							:level="level + 1"
							@toggle-folder="$emit('toggle-folder', $event)"
							@move-item="$emit('move-item', $event)" />
					</template>
				</div>
			`,
			methods: {
				formatFileSize,
				
				handleDragStart(event) {
					this.isDragging = true;
					
					const dragData = {
						id: this.item.id,
						name: this.item.name,
						isFolder: this.item.isFolder,
						parentPath: this.item.parentPath,
						fullPath: this.item.fullPath
					};
					
					event.dataTransfer.setData('application/json', JSON.stringify(dragData));
					event.dataTransfer.effectAllowed = 'move';
				},
				
				handleDragEnter(event) {
					if (this.item.isFolder) {
						this.isDropTarget = true;
					}
				},
				
				handleDragLeave(event) {
					this.isDropTarget = false;
				},
				
				handleDrop(event) {
					this.isDropTarget = false;
					
					if (!this.item.isFolder) return;
					
					try {
						const dragData = JSON.parse(event.dataTransfer.getData('application/json'));
						
						if (!dragData || !dragData.id) return;
						if (dragData.id === this.item.id) return;
						
						if (dragData.isFolder && this.item.fullPath.startsWith(dragData.fullPath)) {
							alert('Não é possível mover uma pasta para dentro de si mesma.');
							return;
						}
						
						this.$emit('move-item', {
							draggedItem: dragData,
							targetFolder: this.item,
							newRelPath: this.item.fullPath
						});
						
					} catch (error) {
						console.error('Erro ao processar drop:', error);
					}
				}
			}
		}
	},

	methods: {
		/**
		 * Carrega os dados da árvore
		 */
		async loadTreeData() {
			if (!this.parentId) {
				console.warn('parentId não fornecido para carregar árvore');
				return;
			}

			this.loading = true;
			try {
				console.log('Carregando árvore para parentId:', this.parentId);
				const apiData = await fetchDocumentTree(this.parentId);
				this.treeData = buildTreeFromApiData(apiData);
				console.log('Árvore carregada com sucesso:', this.treeData.length, 'itens na raiz');
			} catch (error) {
				console.error('Erro ao carregar árvore:', error);
				this.treeData = [];
			} finally {
				this.loading = false;
			}
		},

		/**
		 * Alterna a expansão de uma pasta
		 */
		toggleFolder(item) {
			if (item.isFolder) {
				item.expanded = !item.expanded;
			}
		},

		/**
		 * Manipula o movimento de itens
		 */
		async handleMoveItem(moveData) {
			const { draggedItem, targetFolder, newRelPath } = moveData;

			console.log('Movendo item:', {
				from: draggedItem.name,
				to: targetFolder.name,
				newPath: newRelPath
			});

			try {
				const response = await moveDocuments([draggedItem.id], newRelPath);

				if (response && response.success) {
					// Recarregar a árvore
					await this.loadTreeData();

					this.$emit('move-success', {
						item: draggedItem.name,
						target: targetFolder.name,
						message: response.message
					});
				} else {
					throw new Error(response?.message || 'Erro desconhecido');
				}
			} catch (error) {
				console.error('Erro ao mover documento:', error);
				this.$emit('move-error', {
					item: draggedItem.name,
					target: targetFolder.name,
					error: error.message
				});
			}
		}
	},

	mounted() {
		// Carregar dados quando o componente é montado
		if (this.parentId) {
			this.loadTreeData();
		}
	},

	watch: {
		parentId: {
			handler(newParentId) {
				if (newParentId) {
					this.loadTreeData();
				}
			}
		}
	}
};
</script>

<style scoped>
.multi-file-upload-tree {
	width: 100%;
}

.hover-row:hover {
	background-color: #f8f9fa !important;
}

.drag-over {
	background-color: #e3f2fd !important;
}
</style>
