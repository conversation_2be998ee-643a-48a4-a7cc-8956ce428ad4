<template>
	<div>
		<div
			@click="item.isFolder ? $emit('toggle-folder', item) : null"
			:draggable="true"
			@dragstart="handleDragStart"
			@dragover.prevent
			@dragenter.prevent="handleDragEnter"
			@drop.prevent="handleDrop"
			@dragleave="handleDragLeave"
			:style="{
				display: 'grid',
				gridTemplateColumns: '1fr 100px 100px 150px',
				gap: '10px',
				padding: '8px 10px',
				borderBottom: '1px solid #f0f0f0',
				cursor: item.isFolder ? 'pointer' : 'default',
				backgroundColor: item.isFolder && item.expanded ? '#f8f9fa' : (level > 0 ? '#fafafa' : 'transparent'),
				transition: 'all 0.2s ease',
				opacity: isDragging ? 0.5 : 1,
				borderLeft: isDropTarget ? '3px solid #007bff' : 'none'
			}"
			:class="{ 'hover-row': item.isFolder, 'drag-over': isDropTarget }">

			<!-- Coluna Nome -->
			<div :style="{
				paddingLeft: (level * 20) + 'px',
				display: 'flex',
				alignItems: 'center',
				gap: '8px'
			}">
				<!-- Seta de expansão -->
				<i v-if="item.isFolder && item.children && item.children.length > 0"
				   :class="item.expanded ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"
				   style="font-size: 12px; color: #666; width: 12px;"></i>
				<span v-else style="width: 12px;"></span>

				<!-- Ícone da pasta/ficheiro -->
				<span v-if="item.isFolder && item.expanded" 
				      style="color: #ffc107; margin-right: 8px; font-size: 16px;">📂</span>
				<span v-else-if="item.isFolder" 
				      style="color: #ffc107; margin-right: 8px; font-size: 16px;">📁</span>
				<span v-else 
				      style="color: #6c757d; margin-right: 8px; font-size: 16px;">📄</span>

				<!-- Nome -->
				<span>{{ item.name }}</span>
			</div>

			<!-- Coluna Tipo -->
			<div style="text-align: center;">
				<span v-if="item.isFolder" 
				      style="padding: 2px 8px; border-radius: 12px; font-size: 12px; background-color: #fff3cd; color: #856404;">
					Pasta
				</span>
				<span v-else 
				      style="padding: 2px 8px; border-radius: 12px; font-size: 12px; background-color: #d1ecf1; color: #0c5460;">
					Ficheiro
				</span>
			</div>

			<!-- Coluna Tamanho -->
			<div style="text-align: right;">
				{{ item.isFolder ? '--' : formatFileSize(item.size) }}
			</div>

			<!-- Coluna Modificado -->
			<div style="text-align: center; font-size: 14px; color: #666;">
				{{ item.modified }}
			</div>
		</div>

		<!-- Filhos (recursivo) -->
		<template v-if="item.isFolder && item.expanded && item.children && item.children.length > 0">
			<QTreeItem
				v-for="child in item.children"
				:key="child.id"
				:item="child"
				:level="level + 1"
				@toggle-folder="$emit('toggle-folder', $event)"
				@move-item="$emit('move-item', $event)" />
		</template>
	</div>
</template>

<script>
import { formatFileSize } from './documMultiUploadTree.js'

export default {
	name: 'QTreeItem',

	props: {
		item: {
			type: Object,
			required: true
		},
		level: {
			type: Number,
			default: 0
		}
	},

	emits: ['toggle-folder', 'move-item'],

	data() {
		return {
			isDragging: false,
			isDropTarget: false
		};
	},

	methods: {
		formatFileSize,
		
		handleDragStart(event) {
			this.isDragging = true;
			
			const dragData = {
				id: this.item.id,
				name: this.item.name,
				isFolder: this.item.isFolder,
				parentPath: this.item.parentPath,
				fullPath: this.item.fullPath
			};
			
			event.dataTransfer.setData('application/json', JSON.stringify(dragData));
			event.dataTransfer.effectAllowed = 'move';
		},
		
		handleDragEnter(event) {
			if (this.item.isFolder) {
				this.isDropTarget = true;
			}
		},
		
		handleDragLeave(event) {
			this.isDropTarget = false;
		},
		
		handleDrop(event) {
			this.isDropTarget = false;
			
			if (!this.item.isFolder) return;
			
			try {
				const dragData = JSON.parse(event.dataTransfer.getData('application/json'));
				
				if (!dragData || !dragData.id) return;
				if (dragData.id === this.item.id) return;
				
				if (dragData.isFolder && this.item.fullPath.startsWith(dragData.fullPath)) {
					alert('Não é possível mover uma pasta para dentro de si mesma.');
					return;
				}
				
				this.$emit('move-item', {
					draggedItem: dragData,
					targetFolder: this.item,
					newRelPath: this.item.fullPath
				});
				
			} catch (error) {
				console.error('Erro ao processar drop:', error);
			}
		}
	}
};
</script>

<style scoped>
.hover-row:hover {
	background-color: #f8f9fa !important;
}

.drag-over {
	background-color: #e3f2fd !important;
}
</style>
