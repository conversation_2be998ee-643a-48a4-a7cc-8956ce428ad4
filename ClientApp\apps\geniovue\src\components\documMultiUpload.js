import { postData } from '@quidgest/clientapp/network'

// Cria um novo registo DOCUM com ficheiro associado (multi-upload)
export async function criarDocumComFicheiro(file, { nome, descricao, parentId, parentTable }) {
	const formData = new FormData();
	formData.append('File', file); // Nome igual ao DTO
	formData.append('Nome', nome || file.name);
	if (descricao) formData.append('Descricao', descricao);
	// Só adiciona ParentId se for uma string não vazia (GUID)
	if (typeof parentId === 'string' && parentId.trim() !== '') {
		formData.append('ParentId', parentId);
	}
	if (typeof parentTable === 'string' && parentTable.trim() !== '') {
		formData.append('ParentTable', parentTable);
	}

	// Debug: mostra todos os campos do FormData
	//for (let [key, value] of formData.entries()) {
	//	console.log(`FormData: ${key} =`, value);
	//}

	// Embrulha o postData numa Promise para garantir resultado
	return new Promise((resolve, reject) => {
		postData(
			'DOCUM_MultiUpload',
			'MultiUploadCreate',
			formData,
			//{
			//	maxContentLength: Infinity,
			//	maxBodyLength: Infinity,
			//	timeout: 3600000 // Opcional: 1 hora, se esperas uploads lentos
			//},
			(data, response) => {
				const result = (response && response.data) || data;
				if (!result) {
					reject(new Error('Resposta inválida da API ao criar DOCUM'));
				} else {
					resolve(result);
				}
			},
			(err) => {
				console.error('Erro ao criar DOCUM:', err);
				if (err && err.response) {
					console.error('Resposta do backend:', err.response.data);
					alert('Erro do backend: ' + JSON.stringify(err.response.data));
				}
				reject(err);
			}
		);
	});
}

// Multi-upload: processa vários ficheiros
export async function gravarTodosOsDocum(documList) {
	const results = [];
	for (const { file, nome, descricao, parentId, parentTable } of documList) {
		try {
			const result = await criarDocumComFicheiro(file, { nome, descricao, parentId, parentTable });
			results.push({ fileName: file.name, ...result });
		} catch (err) {
			results.push({ fileName: file.name, success: false, error: err.message });
		}
	}
	return results;
}

// Busca o tamanho máximo de upload permitido pelo backend (em MB)
export function fetchMaxUploadSize() {
	return new Promise((resolve, reject) => {
		postData(
			'DOCUM_MultiUpload',
			'GetMaxUploadSize',
			{}, // parâmetros se necessário
			(data, response) => {
				const result = (response && response.data) || data;
				if (!result || typeof result.maxUploadSizeMB !== 'number') {
					reject(new Error('Invalid API response when fetching max upload size'));
				} else {
					resolve(result.maxUploadSizeMB);
				}
			},
			(err) => {
				console.error('Error fetching max upload size:', err);
				reject(err);
			}
		);
	});
}

