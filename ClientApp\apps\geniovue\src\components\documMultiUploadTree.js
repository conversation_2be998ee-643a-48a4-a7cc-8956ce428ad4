import { postData } from '@quidgest/clientapp/network'

/**
 * Busca a árvore de documentos do backend
 */
export function fetchDocumentTree(coddscrp) {
	return new Promise((resolve, reject) => {
		console.log('fetchDocumentTree chamado com coddscrp:', coddscrp, 'tipo:', typeof coddscrp);

		// Validar se coddscrp é uma string válida
		if (!coddscrp || typeof coddscrp !== 'string') {
			reject(new Error(`coddscrp inválido: ${coddscrp} (tipo: ${typeof coddscrp})`));
			return;
		}

		// Criar FormData como nos outros métodos
		const formData = new FormData();
		formData.append('coddscrp', coddscrp);

		console.log('FormData criado com coddscrp:', coddscrp);

		postData(
			'DOCUM_MultiUpload',
			'GetDocumentTree',
			formData,
			(data, response) => {
				console.log('Response from GetDocumentTree:', { data, response });

				// Estrutura correta encontrada: data.data
				if (data && data.data) {
					const result = data.data;
					console.log('Dados extraídos de data.data:', result.length, 'registos');
					resolve(result);
				} else {
					console.error('Estrutura de resposta inesperada. Esperado: data.data', { data, response });
					reject(new Error('Dados não encontrados na estrutura esperada'));
				}
			},
			(err) => {
				console.error('Error fetching document tree:', err);
				reject(err);
			}
		);
	});
}

/**
 * Move documentos para uma nova pasta (altera o RELPATH)
 */
export function moveDocuments(documentIds, newRelPath) {
	return new Promise((resolve, reject) => {
		console.log('moveDocuments chamado:', { documentIds, newRelPath });

		const requestData = {
			DocumentIds: documentIds,
			NewRelPath: newRelPath
		};

		postData(
			'DOCUM_MultiUpload',
			'MoveDocuments',
			requestData,
			(data, response) => {
				console.log('Response from MoveDocuments:', { data, response });

				const result = (response && response.data) || data;
				if (result && result.success) {
					resolve(result);
				} else {
					reject(new Error(result?.message || 'Erro desconhecido ao mover documentos'));
				}
			},
			(err) => {
				console.error('Error moving documents:', err);
				reject(err);
			}
		);
	});
}

/**
 * Constrói árvore hierárquica a partir dos dados da API
 */
export function buildTreeFromApiData(apiData) {
	console.log('=== CONSTRUINDO ÁRVORE DE DOCUMENTOS ===');
	console.log('Dados recebidos:', apiData.length, 'itens');

	const tree = [];
	const folderMap = new Map();

	// Função para garantir que uma pasta existe
	const ensureFolderExists = (path) => {
		if (!path || folderMap.has(path)) return;
        
		const normalizedPath = path.replace(/\\/g, '/');
		const pathParts = normalizedPath.split('/').filter(part => part.length > 0);
        
		let currentPath = '';
		pathParts.forEach((part, index) => {
			const parentPath = currentPath;
			currentPath = currentPath ? `${currentPath}/${part}` : part;
            
			if (!folderMap.has(currentPath)) {
				const folderItem = {
					id: `folder_${currentPath.replace(/[^a-zA-Z0-9]/g, '_')}`,
					name: part,
					fullPath: currentPath,
					isFolder: true,
					level: index,
					parentPath: parentPath || null,
					children: [],
					expanded: false,
					size: 0,
					modified: new Date().toLocaleDateString(),
					extension: '',
					version: ''
				};
                
				folderMap.set(currentPath, folderItem);
				console.log(`📁 Pasta criada: "${part}" em "${currentPath}"`);
			}
		});
	};

	// Processar todos os itens
	console.log('=== PROCESSANDO ITENS ===');
	apiData.forEach((item, index) => {
		const relPath = item.RELPATH || '';
		const fileName = item.DOCUM || item.DOCNAME || 'Ficheiro sem nome';
        
		console.log(`\nProcessando item ${index}: "${fileName}" em "${relPath}"`);
        
		// Garantir que todas as pastas do caminho existem
		if (relPath) {
			ensureFolderExists(relPath);
		}
        
		// Criar item do ficheiro
		const normalizedRelPath = relPath ? relPath.replace(/\\/g, '/') : '';
		const fileItem = {
			id: item.CODDOCUM || `file_${index}`,
			name: fileName,
			fullPath: normalizedRelPath ? `${normalizedRelPath}/${fileName}` : fileName,
			isFolder: false,
			level: normalizedRelPath ? normalizedRelPath.split('/').filter(p => p.length > 0).length : 0,
			parentPath: normalizedRelPath,
			size: item.tamanho || item.TAMANHO || 0,
			modified: new Date().toLocaleDateString(),
			extension: item.extensao || item.EXTENSAO || '',
			version: item.versao || item.VERSAO || '',
			originalData: item
		};
        
		// Adicionar ficheiro à pasta correta
		if (normalizedRelPath && folderMap.has(normalizedRelPath)) {
			folderMap.get(normalizedRelPath).children.push(fileItem);
			console.log(`✅ Ficheiro "${fileName}" adicionado à pasta "${normalizedRelPath}"`);
		} else {
			tree.push(fileItem);
			console.log(`📄 Ficheiro "${fileName}" adicionado à raiz`);
		}
	});

	// Organizar hierarquia de pastas
	console.log('\n=== ORGANIZANDO HIERARQUIA ===');
	console.log('Pastas criadas:', Array.from(folderMap.keys()));
    
	// Ordenar pastas por profundidade (raiz primeiro)
	const sortedFolders = Array.from(folderMap.entries()).sort((a, b) => {
		const depthA = a[0].split('/').length;
		const depthB = b[0].split('/').length;
		return depthA - depthB;
	});
    
	sortedFolders.forEach(([path, folder]) => {
		console.log(`\n📁 Organizando pasta: "${folder.name}" (${path})`);
		console.log(`   ParentPath: "${folder.parentPath}"`);
		console.log(`   Children: ${folder.children.length}`);
        
		if (folder.parentPath && folderMap.has(folder.parentPath)) {
			// Pasta filha - adicionar à pasta pai
			const parentFolder = folderMap.get(folder.parentPath);
			parentFolder.children.push(folder);
			console.log(`   ✅ Adicionada como filha de "${parentFolder.name}"`);
		} else {
			// Pasta raiz - adicionar à árvore principal
			tree.push(folder);
			console.log(`   🌳 Adicionada à raiz`);
		}
	});

	// Log da árvore final
	console.log('\n=== ÁRVORE FINAL ===');
	const logTreeLevel = (items, level = 0) => {
		const indent = '  '.repeat(level);
		items.forEach((item) => {
			const icon = item.isFolder ? '📁' : '📄';
			const childCount = item.children ? item.children.length : 0;
			const childInfo = item.isFolder ? ` (${childCount} itens)` : '';
            
			console.log(`${indent}${icon} ${item.name}${childInfo}`);
            
			if (item.children && item.children.length > 0) {
				logTreeLevel(item.children, level + 1);
			}
		});
	};
    
	logTreeLevel(tree);
	console.log('\n✅ Árvore construída com sucesso!');
	return tree;
}

/**
 * Conta o total de documentos (ficheiros) na árvore recursivamente
 */
export function countDocuments(items) {
	let count = 0;
	items.forEach(item => {
		if (item.isFolder) {
			// Se é pasta, contar documentos nos filhos
			if (item.children && item.children.length > 0) {
				count += countDocuments(item.children);
			}
		} else {
			// Se é ficheiro, contar
			count++;
		}
	});
	return count;
}

/**
 * Conta o total de pastas na árvore recursivamente
 */
export function countFolders(items) {
	let count = 0;
	items.forEach(item => {
		if (item.isFolder) {
			// Contar a pasta atual
			count++;
			// Contar pastas filhas recursivamente
			if (item.children && item.children.length > 0) {
				count += countFolders(item.children);
			}
		}
	});
	return count;
}

/**
 * Formata o tamanho do ficheiro
 */
export function formatFileSize(size) {
	if (!size || size === 0) return '--';
	const units = ['B', 'KB', 'MB', 'GB'];
	let unitIndex = 0;
	let fileSize = size;
    
	while (fileSize >= 1024 && unitIndex < units.length - 1) {
		fileSize /= 1024;
		unitIndex++;
	}
    
	return fileSize.toFixed(1) + ' ' + units[unitIndex];
}
