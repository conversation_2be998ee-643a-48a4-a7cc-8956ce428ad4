﻿import { propsConverter } from './routeUtils.js'

export default function getFormsRoutes()
{
	return [
		{
			path: '/:culture/:system/:module/form/APPLP/:mode/:id?',
			name: 'form-APPLP',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormApplp/QFormApplp.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'APPLP',
				humanKeyFields: []
			}
		},
		{
			path: '/:culture/:system/:module/form/COMPY/:mode/:id?',
			name: 'form-COMPY',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormCompy/QFormCompy.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'COMPY',
				humanKeyFields: ['ValName']
			}
		},
		{
			path: '/:culture/:system/:module/form/COUNT/:mode/:id?',
			name: 'form-COUNT',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormCount/QFormCount.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'COUNT',
				humanKeyFields: ['ValCountry']
			}
		},
		{
			path: '/:culture/:system/:module/form/DIMPO/:mode/:id?',
			name: 'form-DIMPO',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormDimpo/QFormDimpo.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'DIMPO',
				humanKeyFields: []
			}
		},
		{
			path: '/:culture/:system/:module/form/DMCDR/:mode/:id?',
			name: 'form-DMCDR',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormDmcdr/QFormDmcdr.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'DMCDR',
				humanKeyFields: ['ValCode']
			}
		},
		{
			path: '/:culture/:system/:module/form/DMCDROOT/:mode/:id?',
			name: 'form-DMCDROOT',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormDmcdroot/QFormDmcdroot.vue'),
			meta: {
				routeType: 'form',
				baseArea: '',
				humanKeyFields: []
			}
		},
		{
			path: '/:culture/:system/:module/form/DOCUM/:mode/:id?',
			name: 'form-DOCUM',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormDocum/QFormDocum.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'DOCUM',
				humanKeyFields: ['ValDocname']
			}
		},
		{
			path: '/:culture/:system/:module/form/DOCUMS/:mode/:id?',
			name: 'form-DOCUMS',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormDocums/QFormDocums.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'DSCPP',
				humanKeyFields: ['ValId']
			}
		},
		{
			path: '/:culture/:system/:module/form/DSCPP/:mode/:id?',
			name: 'form-DSCPP',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormDscpp/QFormDscpp.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'DSCPP',
				humanKeyFields: ['ValId']
			}
		},
		{
			path: '/:culture/:system/:module/form/DSCPPVER/:mode/:id?',
			name: 'form-DSCPPVER',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormDscppver/QFormDscppver.vue'),
			meta: {
				routeType: 'form',
				baseArea: '',
				humanKeyFields: []
			}
		},
		{
			path: '/:culture/:system/:module/form/FAVORITS/:mode/:id?',
			name: 'form-FAVORITS',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormFavorits/QFormFavorits.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'FAVOR',
				humanKeyFields: []
			}
		},
		{
			path: '/:culture/:system/:module/form/IETDD/:mode/:id?',
			name: 'form-IETDD',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormIetdd/QFormIetdd.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'IETDD',
				humanKeyFields: ['ValDrawingnumber']
			}
		},
		{
			path: '/:culture/:system/:module/form/LANGU/:mode/:id?',
			name: 'form-LANGU',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormLangu/QFormLangu.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'LANGU',
				humanKeyFields: ['ValLanguageisocode']
			}
		},
		{
			path: '/:culture/:system/:module/form/PA_LG/:mode/:id?',
			name: 'form-PA_LG',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormPaLg/QFormPaLg.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'PA_LG',
				humanKeyFields: []
			}
		},
		{
			path: '/:culture/:system/:module/form/RECDSCP/:mode/:id?',
			name: 'form-RECDSCP',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormRecdscp/QFormRecdscp.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'DSCPP',
				humanKeyFields: ['ValId']
			}
		},
		{
			path: '/:culture/:system/:module/form/RECORD/:mode/:id?',
			name: 'form-RECORD',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormRecord/QFormRecord.vue'),
			meta: {
				routeType: 'form',
				baseArea: '',
				humanKeyFields: []
			}
		},
		{
			path: '/:culture/:system/:module/form/RLDSC/:mode/:id?',
			name: 'form-RLDSC',
			props: route => propsConverter(route),
			component: () => import('@/views/forms/FormRldsc/QFormRldsc.vue'),
			meta: {
				routeType: 'form',
				baseArea: 'RLDSC',
				humanKeyFields: []
			}
		},
	]
}
