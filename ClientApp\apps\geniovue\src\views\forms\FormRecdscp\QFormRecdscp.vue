<template>
	<teleport
		v-if="formModalIsReady && showFormHeader"
		:to="`#${uiContainersId.header}`"
		:disabled="!isPopup || isNested">
		<div
			ref="formHeader"
			:class="{ 'c-sticky-header': isStickyHeader, 'sticky-top': isStickyTop }">
			<div
				v-if="showFormHeader"
				class="c-action-bar">
				<h1
					v-if="formControl.uiComponents.header && formInfo.designation"
					:id="formTitleId"
					class="form-header">
					{{ formInfo.designation }}
				</h1>

				<div class="c-action-bar__menu">
					<template
						v-for="(section, sectionId) in formButtonSections"
						:key="sectionId">
						<span
							v-if="showHeadingSep(sectionId)"
							class="main-title-sep" />

						<q-toggle-group
							v-if="formControl.uiComponents.headerButtons"
							borderless>
							<template
								v-for="btn in section"
								:key="btn.id">
								<q-toggle-group-item
									v-if="showFormHeaderButton(btn)"
									:model-value="btn.isSelected"
									:id="`top-${btn.id}`"
									:title="btn.text"
									:label="btn.label"
									:disabled="btn.disabled"
									@click="btn.action">
									<q-icon
										v-if="btn.icon"
										v-bind="btn.icon" />
								</q-toggle-group-item>
							</template>
						</q-toggle-group>
					</template>
					<q-row-container v-show="controls.RECDSCP_DSCPPTOGGLE__.isVisible">
						<q-control-wrapper v-show="controls.RECDSCP_DSCPPTOGGLE__.isVisible"
										   class="control-join-group">
							<base-input-structure class="i-text"
												  v-bind="controls.RECDSCP_DSCPPTOGGLE__"
												  v-on="controls.RECDSCP_DSCPPTOGGLE__.handlers"
												  :loading="controls.RECDSCP_DSCPPTOGGLE__.props.loading"
												  :reporting-mode-on="reportingModeCAV"
												  :suggestion-mode-on="suggestionModeOn">
								<q-toggle-input v-if="controls.RECDSCP_DSCPPTOGGLE__.isVisible"
												id="RECDSCP_DSCPPTOGGLE__"
												:model-value="model.ValToggle.value"
												:true-label="controls.RECDSCP_DSCPPTOGGLE__.trueLabel"
												:false-label="controls.RECDSCP_DSCPPTOGGLE__.falseLabel"
												
												@update:model-value="model.ValToggle.fnUpdateValue" />
							</base-input-structure>
						</q-control-wrapper>
					</q-row-container>
				</div>

			</div>

			<q-anchor-container-horizontal
				v-if="$app.layout.FormAnchorsPosition === 'form-header' && visibleGroups.length > 0"
				:anchors="anchorGroups"
				:controls="visibleControls"
				@focus-control="(...args) => focusControl(...args)" />
		</div>
	</teleport>

	<teleport
		v-if="formModalIsReady && showFormBody"
		:to="`#${uiContainersId.body}`"
		:disabled="!isPopup || isNested">
		<q-validation-summary
			:messages="validationErrors"
			@error-clicked="focusField" />

		<div :class="[`float-${actionsPlacement}`, 'c-action-bar']">
			<q-button-group borderless>
				<template
					v-for="btn in formButtons"
					:key="btn.id">
					<q-button
						v-if="btn.isActive && btn.isVisible && btn.showInHeading"
						:id="`heading-${btn.id}`"
						:label="btn.text"
						:variant="btn.variant"
						:disabled="btn.disabled"
						:icon-pos="btn.iconPos"
						:class="btn.classes"
						@click="btn.action(); btn.emitAction ? $emit(btn.emitAction.name, btn.emitAction.params) : null">
						<q-icon
							v-if="btn.icon"
							v-bind="btn.icon" />
					</q-button>
				</template>
			</q-button-group>
		</div>

		<div
			class="form-flow"
			data-key="RECDSCP"
			:data-loading="!formInitialDataLoaded">
			<template v-if="formControl.initialized && showFormBody">
				<q-row-container
					v-show="controls.RECDSCP_PSEUDTABDSCPP.isVisible || controls.RECDSCP_PSEUDTABRECOR.isVisible || controls.RECDSCP_PSEUDTABVERSI.isVisible"
					is-large>
					<q-control-wrapper
						v-show="controls.RECDSCP_PSEUDTABDSCPP.isVisible || controls.RECDSCP_PSEUDTABRECOR.isVisible || controls.RECDSCP_PSEUDTABVERSI.isVisible"
						class="row-line-group">
						<q-tab-container
							id="q-tabs-RECDSCP"
							v-bind="controls.formTabs.props"
							@tab-changed="controls.formTabs.selectTab($event)">
							<template #tab-panel>
								<section
									v-if="controls.RECDSCP_PSEUDTABDSCPP.isVisible"
									v-show="controls.formTabs.selectedTab === 'RECDSCP_PSEUDTABDSCPP'">
									<div
										id="RECDSCP_PSEUDTABDSCPP"
										role="tabpanel"
										aria-labelledby="tab-container-RECDSCP_PSEUDTABDSCPP">
										<q-row-container
											v-show="controls.TABDSCPPPSEUDDESCRIPT.isVisible"
											is-large>
											<q-control-wrapper
												v-show="controls.TABDSCPPPSEUDDESCRIPT.isVisible"
												class="row-line-group">
												<q-group-box-container
													id="TABDSCPPPSEUDDESCRIPT"
													v-bind="controls.TABDSCPPPSEUDDESCRIPT"
													:is-visible="controls.TABDSCPPPSEUDDESCRIPT.isVisible">
													<!-- Start TABDSCPPPSEUDDESCRIPT -->
													<q-row-container v-show="controls.TABDSCPPDSCPPID______.isVisible || controls.TABDSCPPDSCPPDATE____.isVisible || controls.TABDSCPPDSCPPTITLE___.isVisible || controls.TABDSCPPDSCPPCREATOR_.isVisible || controls.TABDSCPPDSCPPSUBJECT_.isVisible || controls.TABDSCPPDSCPPPUBLISHE.isVisible || controls.TABDSCPPDSCPPCONTRIBU.isVisible || controls.TABDSCPPDSCPPTYPE____.isVisible || controls.TABDSCPPDSCPPFORMAT__.isVisible || controls.TABDSCPPDSCPPIDENTIFI.isVisible || controls.TABDSCPPDSCPPSOURCE__.isVisible || controls.TABDSCPPDSCPPRELATION.isVisible || controls.TABDSCPPDSCPPCOVERAGE.isVisible || controls.TABDSCPPCOUNTCOUNTRY_.isVisible || controls.TABDSCPPLANGULANGUISO.isVisible || controls.TABDSCPPDSCPPRIGHTS__.isVisible">
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPID______.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPID______"
																v-on="controls.TABDSCPPDSCPPID______.handlers"
																:loading="controls.TABDSCPPDSCPPID______.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-numeric-input
																	v-if="controls.TABDSCPPDSCPPID______.isVisible"
																	v-bind="controls.TABDSCPPDSCPPID______.props"
																	@update:model-value="model.ValId.fnUpdateValue" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPDATE____.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPDATE____"
																v-on="controls.TABDSCPPDSCPPDATE____.handlers"
																:loading="controls.TABDSCPPDSCPPDATE____.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-date-time-picker
																	v-if="controls.TABDSCPPDSCPPDATE____.isVisible"
																	v-bind="controls.TABDSCPPDSCPPDATE____.props"
																	:model-value="model.ValDate.value"
																	@reset-icon-click="model.ValDate.fnUpdateValue(model.ValDate.originalValue ?? new Date())"
																	@update:model-value="model.ValDate.fnUpdateValue($event ?? '')" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPTITLE___.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPTITLE___"
																v-on="controls.TABDSCPPDSCPPTITLE___.handlers"
																:loading="controls.TABDSCPPDSCPPTITLE___.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPTITLE___.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPTITLE___, model.ValTitle.value)"
																	@change="model.ValTitle.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPCREATOR_.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPCREATOR_"
																v-on="controls.TABDSCPPDSCPPCREATOR_.handlers"
																:loading="controls.TABDSCPPDSCPPCREATOR_.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPCREATOR_.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPCREATOR_, model.ValCreator.value)"
																	@change="model.ValCreator.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPSUBJECT_.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPSUBJECT_"
																v-on="controls.TABDSCPPDSCPPSUBJECT_.handlers"
																:loading="controls.TABDSCPPDSCPPSUBJECT_.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPSUBJECT_.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPSUBJECT_, model.ValSubject.value)"
																	@change="model.ValSubject.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPPUBLISHE.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPPUBLISHE"
																v-on="controls.TABDSCPPDSCPPPUBLISHE.handlers"
																:loading="controls.TABDSCPPDSCPPPUBLISHE.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPPUBLISHE.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPPUBLISHE, model.ValPublisher.value)"
																	@change="model.ValPublisher.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPCONTRIBU.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPCONTRIBU"
																v-on="controls.TABDSCPPDSCPPCONTRIBU.handlers"
																:loading="controls.TABDSCPPDSCPPCONTRIBU.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPCONTRIBU.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPCONTRIBU, model.ValContributor.value)"
																	@change="model.ValContributor.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPTYPE____.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPTYPE____"
																v-on="controls.TABDSCPPDSCPPTYPE____.handlers"
																:loading="controls.TABDSCPPDSCPPTYPE____.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPTYPE____.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPTYPE____, model.ValType.value)"
																	@change="model.ValType.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPFORMAT__.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPFORMAT__"
																v-on="controls.TABDSCPPDSCPPFORMAT__.handlers"
																:loading="controls.TABDSCPPDSCPPFORMAT__.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPFORMAT__.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPFORMAT__, model.ValFormat.value)"
																	@change="model.ValFormat.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPIDENTIFI.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPIDENTIFI"
																v-on="controls.TABDSCPPDSCPPIDENTIFI.handlers"
																:loading="controls.TABDSCPPDSCPPIDENTIFI.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPIDENTIFI.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPIDENTIFI, model.ValIdentifier.value)"
																	@change="model.ValIdentifier.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPSOURCE__.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPSOURCE__"
																v-on="controls.TABDSCPPDSCPPSOURCE__.handlers"
																:loading="controls.TABDSCPPDSCPPSOURCE__.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPSOURCE__.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPSOURCE__, model.ValSource.value)"
																	@change="model.ValSource.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPRELATION.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPRELATION"
																v-on="controls.TABDSCPPDSCPPRELATION.handlers"
																:loading="controls.TABDSCPPDSCPPRELATION.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPRELATION.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPRELATION, model.ValRelation.value)"
																	@change="model.ValRelation.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPCOVERAGE.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPCOVERAGE"
																v-on="controls.TABDSCPPDSCPPCOVERAGE.handlers"
																:loading="controls.TABDSCPPDSCPPCOVERAGE.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPCOVERAGE.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPCOVERAGE, model.ValCoverage.value)"
																	@change="model.ValCoverage.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPCOUNTCOUNTRY_.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPCOUNTCOUNTRY_"
																v-on="controls.TABDSCPPCOUNTCOUNTRY_.handlers"
																:loading="controls.TABDSCPPCOUNTCOUNTRY_.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-lookup
																	v-if="controls.TABDSCPPCOUNTCOUNTRY_.isVisible"
																	v-bind="controls.TABDSCPPCOUNTCOUNTRY_.props"
																	v-on="controls.TABDSCPPCOUNTCOUNTRY_.handlers" />
																<q-see-more-tabdscppcountcountry
																	v-if="controls.TABDSCPPCOUNTCOUNTRY_.seeMoreIsVisible"
																	v-bind="controls.TABDSCPPCOUNTCOUNTRY_.seeMoreParams"
																	v-on="controls.TABDSCPPCOUNTCOUNTRY_.handlers" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPLANGULANGUISO.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPLANGULANGUISO"
																v-on="controls.TABDSCPPLANGULANGUISO.handlers"
																:loading="controls.TABDSCPPLANGULANGUISO.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-lookup
																	v-if="controls.TABDSCPPLANGULANGUISO.isVisible"
																	v-bind="controls.TABDSCPPLANGULANGUISO.props"
																	v-on="controls.TABDSCPPLANGULANGUISO.handlers" />
																<q-see-more-tabdscpplangulanguiso
																	v-if="controls.TABDSCPPLANGULANGUISO.seeMoreIsVisible"
																	v-bind="controls.TABDSCPPLANGULANGUISO.seeMoreParams"
																	v-on="controls.TABDSCPPLANGULANGUISO.handlers" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPRIGHTS__.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPRIGHTS__"
																v-on="controls.TABDSCPPDSCPPRIGHTS__.handlers"
																:loading="controls.TABDSCPPDSCPPRIGHTS__.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPRIGHTS__.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPRIGHTS__, model.ValRights.value)"
																	@change="model.ValRights.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
													</q-row-container>
													<!-- End TABDSCPPPSEUDDESCRIPT -->
												</q-group-box-container>
											</q-control-wrapper>
										</q-row-container>
										<q-row-container
											v-show="controls.TABDSCPPPSEUDZNIDENTI.isVisible"
											is-large>
											<q-control-wrapper
												v-show="controls.TABDSCPPPSEUDZNIDENTI.isVisible"
												class="row-line-group">
												<q-group-collapsible
													id="TABDSCPPPSEUDZNIDENTI"
													v-bind="controls.TABDSCPPPSEUDZNIDENTI"
													v-on="controls.TABDSCPPPSEUDZNIDENTI.handlers">
													<!-- Start TABDSCPPPSEUDZNIDENTI -->
													<q-row-container v-show="controls.TABDSCPPDSCPPEXTEPROD.isVisible || controls.TABDSCPPDSCPPEXTECODE.isVisible || controls.TABDSCPPDSCPPMODICODE.isVisible || controls.TABDSCPPDSCPPSYSTEMDI.isVisible || controls.TABDSCPPDMCDRCOMPLCOD.isVisible || controls.TABDSCPPDSCPPLEARNCOD.isVisible || controls.TABDSCPPDSCPPLEAREVCD.isVisible || controls.TABDSCPPDSCPPISSUENUM.isVisible || controls.TABDSCPPDSCPPINWORK__.isVisible || controls.TABDSCPPDSCPPTECHNAME.isVisible || controls.TABDSCPPDSCPPINFONAME.isVisible || controls.TABDSCPPDSCPPISSUETYP.isVisible">
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPEXTEPROD.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPEXTEPROD"
																v-on="controls.TABDSCPPDSCPPEXTEPROD.handlers"
																:loading="controls.TABDSCPPDSCPPEXTEPROD.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPEXTEPROD.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPEXTEPROD, model.ValExtensionproducer.value)"
																	@change="model.ValExtensionproducer.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPEXTECODE.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPEXTECODE"
																v-on="controls.TABDSCPPDSCPPEXTECODE.handlers"
																:loading="controls.TABDSCPPDSCPPEXTECODE.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPEXTECODE.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPEXTECODE, model.ValExtensioncode.value)"
																	@change="model.ValExtensioncode.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPMODICODE.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPMODICODE"
																v-on="controls.TABDSCPPDSCPPMODICODE.handlers"
																:loading="controls.TABDSCPPDSCPPMODICODE.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPMODICODE.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPMODICODE, model.ValModelidentcode.value)"
																	@change="model.ValModelidentcode.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPSYSTEMDI.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPSYSTEMDI"
																v-on="controls.TABDSCPPDSCPPSYSTEMDI.handlers"
																:loading="controls.TABDSCPPDSCPPSYSTEMDI.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPSYSTEMDI.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPSYSTEMDI, model.ValSystemdiffcode.value)"
																	@change="model.ValSystemdiffcode.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDMCDRCOMPLCOD.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDMCDRCOMPLCOD"
																v-on="controls.TABDSCPPDMCDRCOMPLCOD.handlers"
																:loading="controls.TABDSCPPDMCDRCOMPLCOD.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-lookup
																	v-if="controls.TABDSCPPDMCDRCOMPLCOD.isVisible"
																	v-bind="controls.TABDSCPPDMCDRCOMPLCOD.props"
																	v-on="controls.TABDSCPPDMCDRCOMPLCOD.handlers" />
																<q-see-more-tabdscppdmcdrcomplcod
																	v-if="controls.TABDSCPPDMCDRCOMPLCOD.seeMoreIsVisible"
																	v-bind="controls.TABDSCPPDMCDRCOMPLCOD.seeMoreParams"
																	v-on="controls.TABDSCPPDMCDRCOMPLCOD.handlers" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPLEARNCOD.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPLEARNCOD"
																v-on="controls.TABDSCPPDSCPPLEARNCOD.handlers"
																:loading="controls.TABDSCPPDSCPPLEARNCOD.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPLEARNCOD.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPLEARNCOD, model.ValLearncode.value)"
																	@change="model.ValLearncode.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPLEAREVCD.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPLEAREVCD"
																v-on="controls.TABDSCPPDSCPPLEAREVCD.handlers"
																:loading="controls.TABDSCPPDSCPPLEAREVCD.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPLEAREVCD.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPLEAREVCD, model.ValLearneventcode.value)"
																	@change="model.ValLearneventcode.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPISSUENUM.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPISSUENUM"
																v-on="controls.TABDSCPPDSCPPISSUENUM.handlers"
																:loading="controls.TABDSCPPDSCPPISSUENUM.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPISSUENUM.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPISSUENUM, model.ValIssuenumber.value)"
																	@change="model.ValIssuenumber.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPINWORK__.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPINWORK__"
																v-on="controls.TABDSCPPDSCPPINWORK__.handlers"
																:loading="controls.TABDSCPPDSCPPINWORK__.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPINWORK__.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPINWORK__, model.ValInwork.value)"
																	@change="model.ValInwork.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPTECHNAME.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPTECHNAME"
																v-on="controls.TABDSCPPDSCPPTECHNAME.handlers"
																:loading="controls.TABDSCPPDSCPPTECHNAME.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPTECHNAME.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPTECHNAME, model.ValTechname.value)"
																	@change="model.ValTechname.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPINFONAME.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPINFONAME"
																v-on="controls.TABDSCPPDSCPPINFONAME.handlers"
																:loading="controls.TABDSCPPDSCPPINFONAME.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPINFONAME.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPINFONAME, model.ValInfoname.value)"
																	@change="model.ValInfoname.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPISSUETYP.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPISSUETYP"
																v-on="controls.TABDSCPPDSCPPISSUETYP.handlers"
																:loading="controls.TABDSCPPDSCPPISSUETYP.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-select
																	v-if="controls.TABDSCPPDSCPPISSUETYP.isVisible"
																	v-bind="controls.TABDSCPPDSCPPISSUETYP.props"
																	@update:model-value="model.ValIssuetype.fnUpdateValue" />
															</base-input-structure>
														</q-control-wrapper>
													</q-row-container>
													<!-- End TABDSCPPPSEUDZNIDENTI -->
												</q-group-collapsible>
											</q-control-wrapper>
										</q-row-container>
										<q-row-container
											v-show="controls.TABDSCPPPSEUDNOVOGR01.isVisible"
											is-large>
											<q-control-wrapper
												v-show="controls.TABDSCPPPSEUDNOVOGR01.isVisible"
												class="row-line-group">
												<q-group-collapsible
													id="TABDSCPPPSEUDNOVOGR01"
													v-bind="controls.TABDSCPPPSEUDNOVOGR01"
													v-on="controls.TABDSCPPPSEUDNOVOGR01.handlers">
													<!-- Start TABDSCPPPSEUDNOVOGR01 -->
													<q-row-container v-show="controls.TABDSCPPDSCPPSECURCLA.isVisible || controls.TABDSCPPDSCPPSECCOCLA.isVisible || controls.TABDSCPPDSCPPSECCAVAT.isVisible">
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPSECURCLA.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPSECURCLA"
																v-on="controls.TABDSCPPDSCPPSECURCLA.handlers"
																:loading="controls.TABDSCPPDSCPPSECURCLA.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-select
																	v-if="controls.TABDSCPPDSCPPSECURCLA.isVisible"
																	v-bind="controls.TABDSCPPDSCPPSECURCLA.props"
																	@update:model-value="model.ValSecurcla.fnUpdateValue" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPSECCOCLA.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPSECCOCLA"
																v-on="controls.TABDSCPPDSCPPSECCOCLA.handlers"
																:loading="controls.TABDSCPPDSCPPSECCOCLA.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-select
																	v-if="controls.TABDSCPPDSCPPSECCOCLA.isVisible"
																	v-bind="controls.TABDSCPPDSCPPSECCOCLA.props"
																	@update:model-value="model.ValSeccocla.fnUpdateValue" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPSECCAVAT.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPSECCAVAT"
																v-on="controls.TABDSCPPDSCPPSECCAVAT.handlers"
																:loading="controls.TABDSCPPDSCPPSECCAVAT.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-select
																	v-if="controls.TABDSCPPDSCPPSECCAVAT.isVisible"
																	v-bind="controls.TABDSCPPDSCPPSECCAVAT.props"
																	@update:model-value="model.ValSeccavat.fnUpdateValue" />
															</base-input-structure>
														</q-control-wrapper>
													</q-row-container>
													<!-- End TABDSCPPPSEUDNOVOGR01 -->
												</q-group-collapsible>
											</q-control-wrapper>
										</q-row-container>
										<q-row-container
											v-show="controls.TABDSCPPPSEUDSTATUS__.isVisible"
											is-large>
											<q-control-wrapper
												v-show="controls.TABDSCPPPSEUDSTATUS__.isVisible"
												class="row-line-group">
												<q-group-collapsible
													id="TABDSCPPPSEUDSTATUS__"
													v-bind="controls.TABDSCPPPSEUDSTATUS__"
													v-on="controls.TABDSCPPPSEUDSTATUS__.handlers">
													<!-- Start TABDSCPPPSEUDSTATUS__ -->
													<q-row-container v-show="controls.TABDSCPPDSCPPDATADIST.isVisible || controls.TABDSCPPDSCPPEXPREGST.isVisible || controls.TABDSCPPDSCPPDATAHAND.isVisible || controls.TABDSCPPDSCPPDATADEST.isVisible || controls.TABDSCPPDSCPPDATADISC.isVisible || controls.TABDSCPPDSCPPCOPYRIGH.isVisible || controls.TABDSCPPDSCPPPOLICYST.isVisible || controls.TABDSCPPDSCPPDATACOND.isVisible || controls.TABDSCPPCOMPRNAME____.isVisible || controls.TABDSCPPCOMPONAME____.isVisible || controls.TABDSCPPPSEUDCOMPONEN.isVisible">
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPDATADIST.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPDATADIST"
																v-on="controls.TABDSCPPDSCPPDATADIST.handlers"
																:loading="controls.TABDSCPPDSCPPDATADIST.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPDATADIST.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPDATADIST, model.ValDatadistribution.value)"
																	@change="model.ValDatadistribution.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPEXPREGST.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-textarea"
																v-bind="controls.TABDSCPPDSCPPEXPREGST"
																v-on="controls.TABDSCPPDSCPPEXPREGST.handlers"
																:loading="controls.TABDSCPPDSCPPEXPREGST.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-area
																	v-if="controls.TABDSCPPDSCPPEXPREGST.isVisible"
																	v-bind="controls.TABDSCPPDSCPPEXPREGST.props"
																	v-on="controls.TABDSCPPDSCPPEXPREGST.handlers" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPDATAHAND.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPDATAHAND"
																v-on="controls.TABDSCPPDSCPPDATAHAND.handlers"
																:loading="controls.TABDSCPPDSCPPDATAHAND.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPDATAHAND.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPDATAHAND, model.ValDatahandling.value)"
																	@change="model.ValDatahandling.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPDATADEST.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPDATADEST"
																v-on="controls.TABDSCPPDSCPPDATADEST.handlers"
																:loading="controls.TABDSCPPDSCPPDATADEST.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPDATADEST.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPDATADEST, model.ValDatadestruction.value)"
																	@change="model.ValDatadestruction.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPDATADISC.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPDATADISC"
																v-on="controls.TABDSCPPDSCPPDATADISC.handlers"
																:loading="controls.TABDSCPPDSCPPDATADISC.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPDATADISC.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPDATADISC, model.ValDatadisclosure.value)"
																	@change="model.ValDatadisclosure.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPCOPYRIGH.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPCOPYRIGH"
																v-on="controls.TABDSCPPDSCPPCOPYRIGH.handlers"
																:loading="controls.TABDSCPPDSCPPCOPYRIGH.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-editor
																	v-if="controls.TABDSCPPDSCPPCOPYRIGH.isVisible"
																	v-bind="controls.TABDSCPPDSCPPCOPYRIGH.props"
																	v-on="controls.TABDSCPPDSCPPCOPYRIGH.handlers" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPPOLICYST.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPPOLICYST"
																v-on="controls.TABDSCPPDSCPPPOLICYST.handlers"
																:loading="controls.TABDSCPPDSCPPPOLICYST.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPPOLICYST.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPPOLICYST, model.ValPolicystatement.value)"
																	@change="model.ValPolicystatement.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPDSCPPDATACOND.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPDSCPPDATACOND"
																v-on="controls.TABDSCPPDSCPPDATACOND.handlers"
																:loading="controls.TABDSCPPDSCPPDATACOND.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-text-field
																	v-bind="controls.TABDSCPPDSCPPDATACOND.props"
																	@blur="onBlur(controls.TABDSCPPDSCPPDATACOND, model.ValDataconds.value)"
																	@change="model.ValDataconds.fnUpdateValueOnChange" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPCOMPRNAME____.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPCOMPRNAME____"
																v-on="controls.TABDSCPPCOMPRNAME____.handlers"
																:loading="controls.TABDSCPPCOMPRNAME____.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-lookup
																	v-if="controls.TABDSCPPCOMPRNAME____.isVisible"
																	v-bind="controls.TABDSCPPCOMPRNAME____.props"
																	v-on="controls.TABDSCPPCOMPRNAME____.handlers" />
																<q-see-more-tabdscppcomprname
																	v-if="controls.TABDSCPPCOMPRNAME____.seeMoreIsVisible"
																	v-bind="controls.TABDSCPPCOMPRNAME____.seeMoreParams"
																	v-on="controls.TABDSCPPCOMPRNAME____.handlers" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPCOMPONAME____.isVisible"
															class="control-join-group">
															<base-input-structure
																class="i-text"
																v-bind="controls.TABDSCPPCOMPONAME____"
																v-on="controls.TABDSCPPCOMPONAME____.handlers"
																:loading="controls.TABDSCPPCOMPONAME____.props.loading"
																:reporting-mode-on="reportingModeCAV"
																:suggestion-mode-on="suggestionModeOn">
																<q-lookup
																	v-if="controls.TABDSCPPCOMPONAME____.isVisible"
																	v-bind="controls.TABDSCPPCOMPONAME____.props"
																	v-on="controls.TABDSCPPCOMPONAME____.handlers" />
																<q-see-more-tabdscppcomponame
																	v-if="controls.TABDSCPPCOMPONAME____.seeMoreIsVisible"
																	v-bind="controls.TABDSCPPCOMPONAME____.seeMoreParams"
																	v-on="controls.TABDSCPPCOMPONAME____.handlers" />
															</base-input-structure>
														</q-control-wrapper>
														<q-control-wrapper
															v-show="controls.TABDSCPPPSEUDCOMPONEN.isVisible"
															class="control-join-group">
															<q-table
																v-show="controls.TABDSCPPPSEUDCOMPONEN.isVisible"
																v-bind="controls.TABDSCPPPSEUDCOMPONEN"
																v-on="controls.TABDSCPPPSEUDCOMPONEN.handlers" />
															<q-table-extra-extension
																:list-ctrl="controls.TABDSCPPPSEUDCOMPONEN"
																v-on="controls.TABDSCPPPSEUDCOMPONEN.handlers" />
														</q-control-wrapper>
													</q-row-container>
													<!-- End TABDSCPPPSEUDSTATUS__ -->
												</q-group-collapsible>
											</q-control-wrapper>
										</q-row-container>
										<q-row-container
											v-show="controls.TABDSCPPPSEUDAPPLICAB.isVisible"
											is-large>
											<q-control-wrapper
												v-show="controls.TABDSCPPPSEUDAPPLICAB.isVisible"
												class="row-line-group">
												<q-group-collapsible
													id="TABDSCPPPSEUDAPPLICAB"
													v-bind="controls.TABDSCPPPSEUDAPPLICAB"
													v-on="controls.TABDSCPPPSEUDAPPLICAB.handlers">
													<!-- Start TABDSCPPPSEUDAPPLICAB -->
													<q-row-container v-show="controls.TABDSCPPPSEUDAPPS____.isVisible">
														<q-control-wrapper
															v-show="controls.TABDSCPPPSEUDAPPS____.isVisible"
															class="control-join-group">
															<q-table
																v-show="controls.TABDSCPPPSEUDAPPS____.isVisible"
																v-bind="controls.TABDSCPPPSEUDAPPS____"
																v-on="controls.TABDSCPPPSEUDAPPS____.handlers" />
															<q-table-extra-extension
																:list-ctrl="controls.TABDSCPPPSEUDAPPS____"
																v-on="controls.TABDSCPPPSEUDAPPS____.handlers" />
														</q-control-wrapper>
													</q-row-container>
													<!-- End TABDSCPPPSEUDAPPLICAB -->
												</q-group-collapsible>
											</q-control-wrapper>
										</q-row-container>
									</div>
								</section>
								<section
									v-if="controls.RECDSCP_PSEUDTABRECOR.isVisible"
									v-show="controls.formTabs.selectedTab === 'RECDSCP_PSEUDTABRECOR'">
									<div
										id="RECDSCP_PSEUDTABRECOR"
										role="tabpanel"
										aria-labelledby="tab-container-RECDSCP_PSEUDTABRECOR">
										<q-row-container
											v-show="controls.TABRECORPSEUDNOVOGR01.isVisible"
											is-large>
											<q-control-wrapper
												v-show="controls.TABRECORPSEUDNOVOGR01.isVisible"
												class="row-line-group">
												<q-group-box-container
													id="TABRECORPSEUDNOVOGR01"
													v-bind="controls.TABRECORPSEUDNOVOGR01"
													no-border
													:is-visible="controls.TABRECORPSEUDNOVOGR01.isVisible">
													<!-- Start TABRECORPSEUDNOVOGR01 -->
													<!-- Cabeçalho com Toggle Switch -->
													<q-row-container
														v-show="controls.TABRECORPSEUDDOCUMS__.isVisible"
														is-large>
														<q-control-wrapper
															v-show="controls.TABRECORPSEUDDOCUMS__.isVisible"
															class="row-line-group">
															<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
																<h4 style="margin: 0; color: #333;">{{ controls.TABRECORPSEUDDOCUMS__.label }}</h4>
																<div style="display: flex; align-items: center; gap: 10px;">
																	<span style="font-size: 14px; color: #666;">Lista</span>
																	<label class="view-toggle-switch" @click="console.log('Toggle clicado!')">
																		<input
																			type="checkbox"
																			v-model="showTreeView"
																			@click="console.log('Input clicado! showTreeView:', showTreeView)" />
																		<span class="toggle-slider" :class="{ 'active': showTreeView }">
																			<span class="toggle-knob"></span>
																		</span>
																	</label>
																	<span style="font-size: 14px; color: #666;">Árvore</span>
																	<span style="font-size: 12px; color: #999;">({{ showTreeView ? 'ON' : 'OFF' }})</span>
																</div>
															</div>
														</q-control-wrapper>
													</q-row-container>

													<!-- Conteúdo da Tabela/Árvore -->
													<q-row-container
														v-show="controls.TABRECORPSEUDDOCUMS__.isVisible"
														is-large>
														<q-control-wrapper
															v-show="controls.TABRECORPSEUDDOCUMS__.isVisible"
															class="row-line-group">
															<!-- Vista de Lista (padrão) -->
															<template v-if="!showTreeView">
																<q-table
																	v-show="controls.TABRECORPSEUDDOCUMS__.isVisible"
																	v-bind="controls.TABRECORPSEUDDOCUMS__"
																	v-on="{
																		...controls.TABRECORPSEUDDOCUMS__.handlers,
																		'row-action': handleTableRowAction
																	}"
																	/>
																<q-table-extra-extension
																	:list-ctrl="controls.TABRECORPSEUDDOCUMS__"
																	v-on="controls.TABRECORPSEUDDOCUMS__.handlers" />
															</template>

															<!-- Vista de Árvore (explorador de ficheiros) -->
															<template v-else>
																<div style="background-color: white; border: 1px solid #e0e0e0; border-radius: 4px;">
																	<!-- Barra de ferramentas da vista de árvore -->
																	<div style="padding: 10px; border-bottom: 1px solid #e0e0e0; background-color: #f8f9fa; display: flex; justify-content: space-between; align-items: center;">
																		<div style="display: flex; gap: 10px; align-items: center;">
																			<button
																				@click="triggerFolderUpload"
																				style="padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
																				<i class="fas fa-folder-plus"></i>
																				Upload Pasta
																			</button>
																			<button
																				@click="refreshTreeView"
																				style="padding: 8px 16px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
																				<i class="fas fa-sync-alt"></i>
																				Atualizar
																			</button>
																		</div>
																		<span style="font-size: 14px; color: #666;">{{ treeViewData.length }} item(s)</span>
																	</div>

																	<!-- Input oculto para upload de pastas -->
																	<input
																		ref="folderInput"
																		type="file"
																		webkitdirectory
																		multiple
																		style="display: none;"
																		@change="onFolderUpload" />

																	<!-- Vista de árvore hierárquica -->
																	<div style="min-height: 300px; max-height: 500px; overflow-y: auto;">
																		<!-- Cabeçalho da tabela -->
																		<div style="display: grid; grid-template-columns: 1fr 100px 100px 150px; gap: 10px; padding: 10px; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold; position: sticky; top: 0; z-index: 1;">
																			<div>Nome</div>
																			<div style="text-align: center;">Tipo</div>
																			<div style="text-align: right;">Tamanho</div>
																			<div style="text-align: center;">Modificado</div>
																		</div>

																		<!-- Conteúdo da árvore -->
																		<div v-if="treeViewData.length === 0" style="text-align: center; padding: 40px; color: #999;">
																			<i class="fas fa-folder-open" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
																			<p>Nenhum documento encontrado</p>
																			<p style="font-size: 14px;">Use "Upload Pasta" para adicionar ficheiros com estrutura de pastas</p>
																		</div>

																		<!-- Renderização da árvore recursiva -->
																		<div v-else>
																			<tree-item
																				v-for="item in treeViewData"
																				:key="item.id"
																				:item="item"
																				:level="0"
																				@toggle="toggleFolder"
																				@format-size="formatFileSize" />
																		</div>
																	</div>
																</div>
															</template>
														</q-control-wrapper>
													</q-row-container>
													<!-- End TABRECORPSEUDNOVOGR01 -->
												</q-group-box-container>
											</q-control-wrapper>
										</q-row-container>
									</div>
								</section>
								<section
									v-if="controls.RECDSCP_PSEUDTABVERSI.isVisible"
									v-show="controls.formTabs.selectedTab === 'RECDSCP_PSEUDTABVERSI'">
									<div
										id="RECDSCP_PSEUDTABVERSI"
										role="tabpanel"
										aria-labelledby="tab-container-RECDSCP_PSEUDTABVERSI">
										<q-row-container
											v-show="controls.TABVERSIPSEUDEXPVRSN_.isVisible"
											is-large>
											<q-control-wrapper
												v-show="controls.TABVERSIPSEUDEXPVRSN_.isVisible"
												class="row-line-group">
												<q-table
													v-show="controls.TABVERSIPSEUDEXPVRSN_.isVisible"
													v-bind="controls.TABVERSIPSEUDEXPVRSN_"
													v-on="controls.TABVERSIPSEUDEXPVRSN_.handlers" />
												<q-table-extra-extension
													:list-ctrl="controls.TABVERSIPSEUDEXPVRSN_"
													v-on="controls.TABVERSIPSEUDEXPVRSN_.handlers" />
											</q-control-wrapper>
										</q-row-container>
									</div>
								</section>
							</template>
						</q-tab-container>
					</q-control-wrapper>
				</q-row-container>
			</template>
		</div>
	</teleport>

	<hr v-if="!isPopup && showFormFooter" />

	<teleport
		v-if="formModalIsReady && showFormFooter"
		:to="`#${uiContainersId.footer}`"
		:disabled="!isPopup || isNested">
		<q-row-container v-if="showFormFooter">
			<div id="footer-action-btns">
				<template
					v-for="btn in formButtons"
					:key="btn.id">
					<q-button
						v-if="btn.isActive && btn.isVisible && btn.showInFooter"
						:id="`bottom-${btn.id}`"
						:label="btn.text"
						:variant="btn.variant"
						:disabled="btn.disabled"
						:icon-pos="btn.iconPos"
						:class="btn.classes"
						@click="btn.action(); btn.emitAction ? $emit(btn.emitAction.name, btn.emitAction.params) : null">
						<q-icon
							v-if="btn.icon"
							v-bind="btn.icon" />
					</q-button>
				</template>
			</div>
		</q-row-container>
	</teleport>
	<template v-if="showMultiUploadModal">
		<QMultiFileUploadPanel :visible="showMultiUploadModal"
							   @close="showMultiUploadModal = false"
							   @uploaded="onMultiFilesUploaded"
							   :parent-id="model.ValCoddscrp.value"
							   table="DOCUM"
							   parent-table="DSCPP" />
	</template>
</template>

<script>
	/* eslint-disable no-unused-vars */
	import { computed, defineAsyncComponent, readonly } from 'vue'
	import { useRoute } from 'vue-router'

	import FormHandlers from '@/mixins/formHandlers.js'
	import formFunctions from '@/mixins/formFunctions.js'
	import genericFunctions from '@quidgest/clientapp/utils/genericFunctions'
	import listFunctions from '@/mixins/listFunctions.js'
	import listColumnTypes from '@/mixins/listColumnTypes.js'
	import modelFieldType from '@quidgest/clientapp/models/fields'
	import fieldControlClass from '@/mixins/fieldControl.js'
	import qEnums from '@quidgest/clientapp/constants/enums'
	import { resetProgressBar, setProgressBar } from '@/utils/layout.js'

	import hardcodedTexts from '@/hardcodedTexts.js'
	import netAPI from '@quidgest/clientapp/network'
	import asyncProcM from '@quidgest/clientapp/composables/async'
	import qApi from '@/api/genio/quidgestFunctions.js'
	import qFunctions from '@/api/genio/projectFunctions.js'
	import qProjArrays from '@/api/genio/projectArrays.js'
	/* eslint-enable no-unused-vars */

	import FormViewModel from './QFormRecdscpViewModel.js'

	const requiredTextResources = ['QFormRecdscp', 'hardcoded', 'messages']

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS FORM_INCLUDEJS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

	export default {
		name: 'QFormRecdscp',

		components: {
            QMultiFileUploadPanel: defineAsyncComponent(() => import('@/components/QMultiFileUploadPanel.vue')),
			QSeeMoreTabdscppcountcountry: defineAsyncComponent(() => import('@/views/forms/FormRecdscp/dbedits/TabdscppcountcountrySeeMore.vue')),
			QSeeMoreTabdscpplangulanguiso: defineAsyncComponent(() => import('@/views/forms/FormRecdscp/dbedits/TabdscpplangulanguisoSeeMore.vue')),
			QSeeMoreTabdscppdmcdrcomplcod: defineAsyncComponent(() => import('@/views/forms/FormRecdscp/dbedits/TabdscppdmcdrcomplcodSeeMore.vue')),
			QSeeMoreTabdscppcomprname: defineAsyncComponent(() => import('@/views/forms/FormRecdscp/dbedits/TabdscppcomprnameSeeMore.vue')),
			QSeeMoreTabdscppcomponame: defineAsyncComponent(() => import('@/views/forms/FormRecdscp/dbedits/TabdscppcomponameSeeMore.vue')),
		},

		mixins: [
			FormHandlers
		],

		props: {
			/**
			 * Parameters passed in case the form is nested.
			 */
			nestedRouteParams: {
				type: Object,
				default: () => ({
					name: 'RECDSCP',
					location: 'form-RECDSCP',
					params: {
						isNested: true
					}
				})
			}
		},

		expose: [
			'cancel',
			'initFormProperties',
			'navigationId'
		],

		setup(props)
		{
			const route = useRoute()

			return {
				/*
				 * As properties are reactive, when using $route.params, then when we exit it updates cached components.
				 * Properties have no value and this creates an error in new versions of vue-router.
				 * That's why the value has to be copied to a local property to be used in the router-link tag.
				 */
				currentRouteParams: props.isNested ? {} : route.params
			}
		},

		data()
		{
			// eslint-disable-next-line
			const vm = this
			return {
				componentOnLoadProc: asyncProcM.getProcListMonitor('QFormRecdscp', false),

				showMultiUploadModal: false, // Controla a visibilidade do modal de upload múltiplo
				showTreeView: false, // Controla se mostra a vista de árvore (true) ou lista (false)

				// Dados para a vista de árvore
				treeViewData: [], // Array com a estrutura hierárquica de ficheiros e pastas
				treeViewColumns: [
					{
						name: 'name',
						label: 'Nome',
						field: 'name',
						align: 'left',
						sortable: true,
						style: 'width: 50%'
					},
					{
						name: 'type',
						label: 'Tipo',
						field: 'type',
						align: 'center',
						sortable: true,
						style: 'width: 15%'
					},
					{
						name: 'size',
						label: 'Tamanho',
						field: 'size',
						align: 'right',
						sortable: true,
						style: 'width: 15%'
					},
					{
						name: 'modified',
						label: 'Modificado',
						field: 'modified',
						align: 'center',
						sortable: true,
						style: 'width: 20%'
					}
				],

				interfaceMetadata: {
					id: 'QFormRecdscp', // Used for resources
					requiredTextResources
				},

				formInfo: {
					type: 'normal',
					name: 'RECDSCP',
					route: 'form-RECDSCP',
					area: 'DSCPP',
					primaryKey: 'ValCoddscrp',
					designation: computed(() => this.Resources.RECORD64169),
					identifier: '', // Unique identifier received by route (when it's nested).
					mode: ''
				},

				formButtons: {
					changeToShow: {
						id: 'change-to-show-btn',
						icon: {
							icon: 'view',
							type: 'svg'
						},
						type: 'form-mode',
						text: computed(() => vm.Resources[hardcodedTexts.view]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.show === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && [vm.formModes.show, vm.formModes.edit, vm.formModes.delete].includes(vm.formInfo.mode)),
						action: vm.changeToShowMode
					},
					changeToEdit: {
						id: 'change-to-edit-btn',
						icon: {
							icon: 'pencil',
							type: 'svg'
						},
						type: 'form-mode',
						text: computed(() => vm.Resources[hardcodedTexts.edit]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.edit === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && [vm.formModes.show, vm.formModes.edit, vm.formModes.delete].includes(vm.formInfo.mode)),
						action: vm.changeToEditMode
					},
					changeToDuplicate: {
						id: 'change-to-dup-btn',
						icon: {
							icon: 'duplicate',
							type: 'svg'
						},
						type: 'form-mode',
						text: computed(() => vm.Resources[hardcodedTexts.duplicate]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.duplicate === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && vm.formModes.new !== vm.formInfo.mode),
						action: vm.changeToDupMode
					},
					changeToDelete: {
						id: 'change-to-delete-btn',
						icon: {
							icon: 'delete',
							type: 'svg'
						},
						type: 'form-mode',
						text: computed(() => vm.Resources[hardcodedTexts.delete]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.delete === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && [vm.formModes.show, vm.formModes.edit, vm.formModes.delete].includes(vm.formInfo.mode)),
						action: vm.changeToDeleteMode
					},
					changeToInsert: {
						id: 'change-to-insert-btn',
						icon: {
							icon: 'add',
							type: 'svg'
						},
						type: 'form-insert',
						text: computed(() => vm.Resources[hardcodedTexts.insert]),
						label: computed(() => vm.Resources[hardcodedTexts.insert]),
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isSelected: computed(() => vm.formModes.new === vm.formInfo.mode),
						isVisible: computed(() => vm.authData.isAllowed && vm.formModes.duplicate !== vm.formInfo.mode),
						action: vm.changeToInsertMode
					},
					repeatInsertBtn: {
						id: 'repeat-insert-btn',
						icon: {
							icon: 'save-new',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[hardcodedTexts.repeatInsert]),
						variant: 'bold',
						showInHeader: true,
						showInFooter: true,
						isActive: false,
						isVisible: computed(() => vm.authData.isAllowed && vm.formInfo.mode === vm.formModes.new),
						action: () => vm.saveForm(true)
					},
					saveBtn: {
						id: 'save-btn',
						icon: {
							icon: 'save',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources.GRAVAR45301),
						variant: 'bold',
						showInHeader: true,
						showInFooter: true,
						isActive: true,
						isVisible: computed(() => vm.authData.isAllowed && vm.isEditable),
						action: vm.saveForm
					},
					confirmBtn: {
						id: 'confirm-btn',
						icon: {
							icon: 'check',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[vm.isNested ? hardcodedTexts.delete : hardcodedTexts.confirm]),
						variant: 'bold',
						showInHeader: true,
						showInFooter: true,
						isActive: true,
						isVisible: computed(() => vm.authData.isAllowed && (vm.formInfo.mode === vm.formModes.delete || vm.isNested)),
						action: vm.deleteRecord
					},
					cancelBtn: {
						id: 'cancel-btn',
						icon: {
							icon: 'cancel',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources.CANCELAR49513),
						showInHeader: true,
						showInFooter: true,
						isActive: true,
						isVisible: computed(() => vm.authData.isAllowed && vm.isEditable),
						action: vm.leaveForm
					},
					resetCancelBtn: {
						id: 'reset-cancel-btn',
						icon: {
							icon: 'cancel',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[hardcodedTexts.cancel]),
						showInHeader: true,
						showInFooter: true,
						isActive: false,
						isVisible: computed(() => vm.authData.isAllowed && vm.isEditable),
						action: () => vm.model.resetValues(),
						emitAction: {
							name: 'deselect',
							params: {}
						}
					},
					editBtn: {
						id: 'edit-btn',
						icon: {
							icon: 'pencil',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[hardcodedTexts.edit]),
						variant: 'bold',
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isVisible: computed(() => vm.authData.isAllowed && vm.parentFormMode !== vm.formModes.show && vm.parentFormMode !== vm.formModes.delete),
						action: () => {},
						emitAction: {
							name: 'edit',
							params: {}
						}
					},
					deleteQuickBtn: {
						id: 'delete-btn',
						icon: {
							icon: 'bin',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.Resources[hardcodedTexts.delete]),
						variant: 'bold',
						showInHeader: true,
						showInFooter: false,
						isActive: false,
						isVisible: computed(() => vm.authData.isAllowed && vm.parentFormMode !== vm.formModes.show && (typeof vm.permissions.canDelete === 'boolean' ? vm.permissions.canDelete : true)),
						action: vm.deleteRecord
					},
					backBtn: {
						id: 'back-btn',
						icon: {
							icon: 'back',
							type: 'svg'
						},
						type: 'form-action',
						text: computed(() => vm.isPopup ? vm.Resources[hardcodedTexts.close] : vm.Resources[hardcodedTexts.goBack]),
						showInHeader: true,
						showInFooter: true,
						isActive: true,
						isVisible: computed(() => !vm.authData.isAllowed || !vm.isEditable),
						action: vm.leaveForm
					}
				},

				controls: {
					RECDSCP_PSEUDTABDSCPP: new fieldControlClass.TabControl({
						id: 'RECDSCP_PSEUDTABDSCPP',
						name: 'TABDSCPP',
						size: 'block',
						label: computed(() => this.Resources.METADATA28003),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						directChildren: ['TABDSCPPPSEUDDESCRIPT', 'TABDSCPPPSEUDZNIDENTI', 'TABDSCPPPSEUDNOVOGR01', 'TABDSCPPPSEUDSTATUS__', 'TABDSCPPPSEUDAPPLICAB'],
						mustBeFilled: true,
						controlLimits: [
						],
					}, this),
					RECDSCP_PSEUDTABRECOR: new fieldControlClass.TabControl({
						id: 'RECDSCP_PSEUDTABRECOR',
						name: 'TABRECOR',
						size: 'block',
						label: computed(() => this.Resources.ATTACHMENTS19612),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						directChildren: ['TABRECORPSEUDNOVOGR01'],
						controlLimits: [
						],
					}, this),
					RECDSCP_DSCPPTOGGLE__: new fieldControlClass.ArrayBooleanControl({
						modelField: 'ValToggle',
						valueChangeEvent: 'fieldChange:dscpp.toggle',
						id: 'RECDSCP_DSCPPTOGGLE__',
						name: 'TOGGLE',
						size: 'mini',
						label: computed(() => this.Resources.SHOW_FIELDS21841),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.right),
						maxIntegers: 1,
						maxDecimals: 0,
						arrayName: 'AToogle',
						trueLabel: '',
						falseLabel: '',
						controlLimits: [
						],
					}, this),
					RECDSCP_PSEUDTABVERSI: new fieldControlClass.TabControl({
						id: 'RECDSCP_PSEUDTABVERSI',
						name: 'TABVERSI',
						size: 'block',
						label: computed(() => this.Resources.VERSIONS21770),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						directChildren: ['TABVERSIPSEUDEXPVRSN_'],
						controlLimits: [
						],
					}, this),
					TABDSCPPPSEUDDESCRIPT: new fieldControlClass.GroupControl({
						id: 'TABDSCPPPSEUDDESCRIPT',
						name: 'DESCRIPT',
						size: 'block',
						label: computed(() => this.Resources.DESCRIPTION07438),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						tab: 'RECDSCP_PSEUDTABDSCPP',
						isCollapsible: false,
						anchored: false,
						directChildren: ['TABDSCPPDSCPPID______', 'TABDSCPPDSCPPDATE____', 'TABDSCPPDSCPPTITLE___', 'TABDSCPPDSCPPCREATOR_', 'TABDSCPPDSCPPSUBJECT_', 'TABDSCPPDSCPPPUBLISHE', 'TABDSCPPDSCPPCONTRIBU', 'TABDSCPPDSCPPTYPE____', 'TABDSCPPDSCPPFORMAT__', 'TABDSCPPDSCPPIDENTIFI', 'TABDSCPPDSCPPSOURCE__', 'TABDSCPPDSCPPRELATION', 'TABDSCPPDSCPPCOVERAGE', 'TABDSCPPCOUNTCOUNTRY_', 'TABDSCPPLANGULANGUISO', 'TABDSCPPDSCPPRIGHTS__'],
						controlLimits: [
						],
					}, this),
					TABDSCPPDSCPPID______: new fieldControlClass.NumberControl({
						modelField: 'ValId',
						valueChangeEvent: 'fieldChange:dscpp.id',
						id: 'TABDSCPPDSCPPID______',
						name: 'ID',
						size: 'mini',
						label: computed(() => this.Resources.ID36840),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxIntegers: 6,
						maxDecimals: 0,
						isSequencial: true,
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPDATE____: new fieldControlClass.DateControl({
						modelField: 'ValDate',
						valueChangeEvent: 'fieldChange:dscpp.date',
						id: 'TABDSCPPDSCPPDATE____',
						name: 'DATE',
						size: 'small',
						label: computed(() => this.Resources.DATE18475),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						format: 'date',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPTITLE___: new fieldControlClass.StringControl({
						modelField: 'ValTitle',
						valueChangeEvent: 'fieldChange:dscpp.title',
						id: 'TABDSCPPDSCPPTITLE___',
						name: 'TITLE',
						size: 'xxlarge',
						label: computed(() => this.Resources.TITLE21885),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPTITLE___',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPCREATOR_: new fieldControlClass.StringControl({
						modelField: 'ValCreator',
						valueChangeEvent: 'fieldChange:dscpp.creator',
						id: 'TABDSCPPDSCPPCREATOR_',
						name: 'CREATOR',
						size: 'xxlarge',
						label: computed(() => this.Resources.CREATOR00370),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPCREATOR_',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
						showWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								return netAPI.postData(
									'Dscpp',
									'RECDSCP_TABDSCPPDSCPPCREATOR__ShowWhen',
									this.serverObjModel,
									undefined,
									undefined,
									undefined,
									this.navigationId)
							},
							dependencyEvents: ['fieldChange:dscpp.toggle'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPSUBJECT_: new fieldControlClass.StringControl({
						modelField: 'ValSubject',
						valueChangeEvent: 'fieldChange:dscpp.subject',
						id: 'TABDSCPPDSCPPSUBJECT_',
						name: 'SUBJECT',
						size: 'xxlarge',
						label: computed(() => this.Resources.SUBJECT33942),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPSUBJECT_',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
						showWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								return netAPI.postData(
									'Dscpp',
									'RECDSCP_TABDSCPPDSCPPSUBJECT__ShowWhen',
									this.serverObjModel,
									undefined,
									undefined,
									undefined,
									this.navigationId)
							},
							dependencyEvents: ['fieldChange:dscpp.toggle'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPPUBLISHE: new fieldControlClass.StringControl({
						modelField: 'ValPublisher',
						valueChangeEvent: 'fieldChange:dscpp.publisher',
						id: 'TABDSCPPDSCPPPUBLISHE',
						name: 'PUBLISHE',
						size: 'xxlarge',
						label: computed(() => this.Resources.PUBLISHER59095),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 50,
						labelId: 'label_TABDSCPPDSCPPPUBLISHE',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPCONTRIBU: new fieldControlClass.StringControl({
						modelField: 'ValContributor',
						valueChangeEvent: 'fieldChange:dscpp.contributor',
						id: 'TABDSCPPDSCPPCONTRIBU',
						name: 'CONTRIBU',
						size: 'xxlarge',
						label: computed(() => this.Resources.CONTRIBUTOR40001),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPCONTRIBU',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPTYPE____: new fieldControlClass.StringControl({
						modelField: 'ValType',
						valueChangeEvent: 'fieldChange:dscpp.type',
						id: 'TABDSCPPDSCPPTYPE____',
						name: 'TYPE',
						size: 'xxlarge',
						label: computed(() => this.Resources.TYPE00312),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPTYPE____',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPFORMAT__: new fieldControlClass.StringControl({
						modelField: 'ValFormat',
						valueChangeEvent: 'fieldChange:dscpp.format',
						id: 'TABDSCPPDSCPPFORMAT__',
						name: 'FORMAT',
						size: 'xxlarge',
						label: computed(() => this.Resources.FORMAT00194),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 50,
						labelId: 'label_TABDSCPPDSCPPFORMAT__',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPIDENTIFI: new fieldControlClass.StringControl({
						modelField: 'ValIdentifier',
						valueChangeEvent: 'fieldChange:dscpp.identifier',
						id: 'TABDSCPPDSCPPIDENTIFI',
						name: 'IDENTIFI',
						size: 'xxlarge',
						label: computed(() => this.Resources.IDENTIFIER53792),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						isFormulaBlocked: true,
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPIDENTIFI',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPSOURCE__: new fieldControlClass.StringControl({
						modelField: 'ValSource',
						valueChangeEvent: 'fieldChange:dscpp.source',
						id: 'TABDSCPPDSCPPSOURCE__',
						name: 'SOURCE',
						size: 'xxlarge',
						label: computed(() => this.Resources.SOURCE49690),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPSOURCE__',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPRELATION: new fieldControlClass.StringControl({
						modelField: 'ValRelation',
						valueChangeEvent: 'fieldChange:dscpp.relation',
						id: 'TABDSCPPDSCPPRELATION',
						name: 'RELATION',
						size: 'xxlarge',
						label: computed(() => this.Resources.RELATION40306),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPRELATION',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPCOVERAGE: new fieldControlClass.StringControl({
						modelField: 'ValCoverage',
						valueChangeEvent: 'fieldChange:dscpp.coverage',
						id: 'TABDSCPPDSCPPCOVERAGE',
						name: 'COVERAGE',
						size: 'xxlarge',
						label: computed(() => this.Resources.COVERAGE10098),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPCOVERAGE',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPCOUNTCOUNTRY_: new fieldControlClass.LookupControl({
						modelField: 'TableCountCountry',
						valueChangeEvent: 'fieldChange:count.country',
						id: 'TABDSCPPCOUNTCOUNTRY_',
						name: 'COUNTRY',
						size: 'xxlarge',
						label: computed(() => this.Resources.COUNTRY64133),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCodcount',
							dependencyEvent: 'fieldChange:dscpp.codcount'
						},
						dependentFields: () => ({
							set 'count.codcount'(value) { vm.model.ValCodcount.updateValue(value) },
							set 'count.country'(value) { vm.model.TableCountCountry.updateValue(value) },
						}),
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPLANGULANGUISO: new fieldControlClass.LookupControl({
						modelField: 'TableLanguLanguiso',
						valueChangeEvent: 'fieldChange:langu.languageisocode',
						id: 'TABDSCPPLANGULANGUISO',
						name: 'LANGUISO',
						size: 'small',
						label: computed(() => this.Resources.LANGUAGE_ISO_CODE59219),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCodlangu',
							dependencyEvent: 'fieldChange:dscpp.codlangu'
						},
						dependentFields: () => ({
							set 'langu.codlangu'(value) { vm.model.ValCodlangu.updateValue(value) },
							set 'langu.languageisocode'(value) { vm.model.TableLanguLanguiso.updateValue(value) },
						}),
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPRIGHTS__: new fieldControlClass.StringControl({
						modelField: 'ValRights',
						valueChangeEvent: 'fieldChange:dscpp.rights',
						id: 'TABDSCPPDSCPPRIGHTS__',
						name: 'RIGHTS',
						size: 'xxlarge',
						label: computed(() => this.Resources.RIGHTS63991),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDDESCRIPT',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						isFormulaBlocked: true,
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPRIGHTS__',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPPSEUDZNIDENTI: new fieldControlClass.GroupControl({
						id: 'TABDSCPPPSEUDZNIDENTI',
						name: 'ZNIDENTI',
						size: 'block',
						label: computed(() => this.Resources.IDENTIFICATION40793),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						tab: 'RECDSCP_PSEUDTABDSCPP',
						isCollapsible: true,
						anchored: false,
						directChildren: ['TABDSCPPDSCPPEXTEPROD', 'TABDSCPPDSCPPEXTECODE', 'TABDSCPPDSCPPMODICODE', 'TABDSCPPDSCPPSYSTEMDI', 'TABDSCPPDMCDRCOMPLCOD', 'TABDSCPPDSCPPLEARNCOD', 'TABDSCPPDSCPPLEAREVCD', 'TABDSCPPDSCPPISSUENUM', 'TABDSCPPDSCPPINWORK__', 'TABDSCPPDSCPPTECHNAME', 'TABDSCPPDSCPPINFONAME', 'TABDSCPPDSCPPISSUETYP'],
						mustBeFilled: true,
						controlLimits: [
						],
					}, this),
					TABDSCPPDSCPPEXTEPROD: new fieldControlClass.StringControl({
						modelField: 'ValExtensionproducer',
						valueChangeEvent: 'fieldChange:dscpp.extensionproducer',
						id: 'TABDSCPPDSCPPEXTEPROD',
						name: 'EXTEPROD',
						size: 'xxlarge',
						label: computed(() => this.Resources.EXTENSION_PRODUCER50264),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 200,
						labelId: 'label_TABDSCPPDSCPPEXTEPROD',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPEXTECODE: new fieldControlClass.StringControl({
						modelField: 'ValExtensioncode',
						valueChangeEvent: 'fieldChange:dscpp.extensioncode',
						id: 'TABDSCPPDSCPPEXTECODE',
						name: 'EXTECODE',
						size: 'xxlarge',
						label: computed(() => this.Resources.EXTENSION_CODE33830),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 50,
						labelId: 'label_TABDSCPPDSCPPEXTECODE',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPMODICODE: new fieldControlClass.StringControl({
						modelField: 'ValModelidentcode',
						valueChangeEvent: 'fieldChange:dscpp.modelidentcode',
						id: 'TABDSCPPDSCPPMODICODE',
						name: 'MODICODE',
						size: 'medium',
						label: computed(() => this.Resources.MODEL_IDENTIFIER14950),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 16,
						labelId: 'label_TABDSCPPDSCPPMODICODE',
						mustBeFilled: true,
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [FormMode]!=[FormModeNew]
								return vm.formInfo.mode!==vm.formModes.new
							},
							dependencyEvents: ['form-mode-change'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPSYSTEMDI: new fieldControlClass.StringControl({
						modelField: 'ValSystemdiffcode',
						valueChangeEvent: 'fieldChange:dscpp.systemdiffcode',
						id: 'TABDSCPPDSCPPSYSTEMDI',
						name: 'SYSTEMDI',
						size: 'large',
						label: computed(() => this.Resources.SYSTEM_DIFFERENCE_CO01829),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 10,
						labelId: 'label_TABDSCPPDSCPPSYSTEMDI',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [FormMode]!=[FormModeNew]
								return vm.formInfo.mode!==vm.formModes.new
							},
							dependencyEvents: ['form-mode-change'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDMCDRCOMPLCOD: new fieldControlClass.LookupControl({
						modelField: 'TableDmcdrComplcod',
						valueChangeEvent: 'fieldChange:dmcdr.completecode',
						id: 'TABDSCPPDMCDRCOMPLCOD',
						name: 'COMPLCOD',
						size: 'xxlarge',
						label: computed(() => this.Resources.CODE49225),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCoddmcod',
							dependencyEvent: 'fieldChange:dscpp.coddmcod'
						},
						dependentFields: () => ({
							set 'dmcdr.coddmcod'(value) { vm.model.ValCoddmcod.updateValue(value) },
							set 'dmcdr.completecode'(value) { vm.model.TableDmcdrComplcod.updateValue(value) },
						}),
						mustBeFilled: true,
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [FormMode]!=[FormModeNew]
								return vm.formInfo.mode!==vm.formModes.new
							},
							dependencyEvents: ['form-mode-change'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPLEARNCOD: new fieldControlClass.StringControl({
						modelField: 'ValLearncode',
						valueChangeEvent: 'fieldChange:dscpp.learncode',
						id: 'TABDSCPPDSCPPLEARNCOD',
						name: 'LEARNCOD',
						size: 'xxlarge',
						label: computed(() => this.Resources.LEARN_CODE01421),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 50,
						labelId: 'label_TABDSCPPDSCPPLEARNCOD',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPLEAREVCD: new fieldControlClass.StringControl({
						modelField: 'ValLearneventcode',
						valueChangeEvent: 'fieldChange:dscpp.learneventcode',
						id: 'TABDSCPPDSCPPLEAREVCD',
						name: 'LEAREVCD',
						size: 'medium',
						label: computed(() => this.Resources.LEARN_EVENT_CODE58740),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 1,
						labelId: 'label_TABDSCPPDSCPPLEAREVCD',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPISSUENUM: new fieldControlClass.StringControl({
						modelField: 'ValIssuenumber',
						valueChangeEvent: 'fieldChange:dscpp.issuenumber',
						id: 'TABDSCPPDSCPPISSUENUM',
						name: 'ISSUENUM',
						size: 'medium',
						label: computed(() => this.Resources.ISSUE_NUMBER38761),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 3,
						labelId: 'label_TABDSCPPDSCPPISSUENUM',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPINWORK__: new fieldControlClass.StringControl({
						modelField: 'ValInwork',
						valueChangeEvent: 'fieldChange:dscpp.inwork',
						id: 'TABDSCPPDSCPPINWORK__',
						name: 'INWORK',
						size: 'mini',
						label: computed(() => this.Resources.IN_WORK08152),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 3,
						labelId: 'label_TABDSCPPDSCPPINWORK__',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPTECHNAME: new fieldControlClass.StringControl({
						modelField: 'ValTechname',
						valueChangeEvent: 'fieldChange:dscpp.techname',
						id: 'TABDSCPPDSCPPTECHNAME',
						name: 'TECHNAME',
						size: 'xxlarge',
						label: computed(() => this.Resources.TECHNICAL_NAME26958),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPTECHNAME',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPINFONAME: new fieldControlClass.StringControl({
						modelField: 'ValInfoname',
						valueChangeEvent: 'fieldChange:dscpp.infoname',
						id: 'TABDSCPPDSCPPINFONAME',
						name: 'INFONAME',
						size: 'xxlarge',
						label: computed(() => this.Resources.INFORMATION_NAME64067),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 150,
						labelId: 'label_TABDSCPPDSCPPINFONAME',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPISSUETYP: new fieldControlClass.ArrayStringControl({
						modelField: 'ValIssuetype',
						valueChangeEvent: 'fieldChange:dscpp.issuetype',
						id: 'TABDSCPPDSCPPISSUETYP',
						name: 'ISSUETYP',
						size: 'mini',
						label: computed(() => this.Resources.ISSUE_TYPE61054),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDZNIDENTI',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 2,
						labelId: 'label_TABDSCPPDSCPPISSUETYP',
						arrayName: 'aIssueTp',
						helpShortItem: '',
						helpDetailedItem: '',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPPSEUDNOVOGR01: new fieldControlClass.GroupControl({
						id: 'TABDSCPPPSEUDNOVOGR01',
						name: 'NOVOGR01',
						size: 'block',
						label: computed(() => this.Resources.SECURITY63893),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						tab: 'RECDSCP_PSEUDTABDSCPP',
						isCollapsible: true,
						anchored: false,
						directChildren: ['TABDSCPPDSCPPSECURCLA', 'TABDSCPPDSCPPSECCOCLA', 'TABDSCPPDSCPPSECCAVAT'],
						mustBeFilled: true,
						controlLimits: [
						],
					}, this),
					TABDSCPPDSCPPSECURCLA: new fieldControlClass.ArrayStringControl({
						modelField: 'ValSecurcla',
						valueChangeEvent: 'fieldChange:dscpp.securcla',
						id: 'TABDSCPPDSCPPSECURCLA',
						name: 'SECURCLA',
						size: 'mini',
						label: computed(() => this.Resources.SECURITY_CLASSIFICAT49492),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDNOVOGR01',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 2,
						labelId: 'label_TABDSCPPDSCPPSECURCLA',
						mustBeFilled: true,
						arrayName: 'aSecClas',
						helpShortItem: '',
						helpDetailedItem: '',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPSECCOCLA: new fieldControlClass.ArrayStringControl({
						modelField: 'ValSeccocla',
						valueChangeEvent: 'fieldChange:dscpp.seccocla',
						id: 'TABDSCPPDSCPPSECCOCLA',
						name: 'SECCOCLA',
						size: 'small',
						label: computed(() => this.Resources.COMERCIAL_CLASSIFICA26746),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDNOVOGR01',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 4,
						labelId: 'label_TABDSCPPDSCPPSECCOCLA',
						arrayName: 'aSCoClas',
						helpShortItem: '',
						helpDetailedItem: '',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPSECCAVAT: new fieldControlClass.ArrayStringControl({
						modelField: 'ValSeccavat',
						valueChangeEvent: 'fieldChange:dscpp.seccavat',
						id: 'TABDSCPPDSCPPSECCAVAT',
						name: 'SECCAVAT',
						size: 'small',
						label: computed(() => this.Resources.CAVEAT43987),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDNOVOGR01',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 4,
						labelId: 'label_TABDSCPPDSCPPSECCAVAT',
						arrayName: 'aCaveat',
						helpShortItem: '',
						helpDetailedItem: '',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPPSEUDSTATUS__: new fieldControlClass.GroupControl({
						id: 'TABDSCPPPSEUDSTATUS__',
						name: 'STATUS',
						size: 'block',
						label: computed(() => this.Resources.STATUS08858),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						tab: 'RECDSCP_PSEUDTABDSCPP',
						isCollapsible: true,
						anchored: false,
						directChildren: ['TABDSCPPDSCPPDATADIST', 'TABDSCPPDSCPPEXPREGST', 'TABDSCPPDSCPPDATAHAND', 'TABDSCPPDSCPPDATADEST', 'TABDSCPPDSCPPDATADISC', 'TABDSCPPDSCPPCOPYRIGH', 'TABDSCPPDSCPPPOLICYST', 'TABDSCPPDSCPPDATACOND', 'TABDSCPPCOMPRNAME____', 'TABDSCPPCOMPONAME____', 'TABDSCPPPSEUDCOMPONEN'],
						controlLimits: [
						],
					}, this),
					TABDSCPPDSCPPDATADIST: new fieldControlClass.StringControl({
						modelField: 'ValDatadistribution',
						valueChangeEvent: 'fieldChange:dscpp.datadistribution',
						id: 'TABDSCPPDSCPPDATADIST',
						name: 'DATADIST',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_DISTRIBUTION64392),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 200,
						labelId: 'label_TABDSCPPDSCPPDATADIST',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPEXPREGST: new fieldControlClass.MultilineStringControl({
						modelField: 'ValExportregistrationstmt',
						valueChangeEvent: 'fieldChange:dscpp.exportregistrationstmt',
						id: 'TABDSCPPDSCPPEXPREGST',
						name: 'EXPREGST',
						size: 'xxlarge',
						label: computed(() => this.Resources.EXPORT_REGISTRATION50113),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						rows: 1,
						cols: 500,
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPDATAHAND: new fieldControlClass.StringControl({
						modelField: 'ValDatahandling',
						valueChangeEvent: 'fieldChange:dscpp.datahandling',
						id: 'TABDSCPPDSCPPDATAHAND',
						name: 'DATAHAND',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_HANDLING32139),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 500,
						labelId: 'label_TABDSCPPDSCPPDATAHAND',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPDATADEST: new fieldControlClass.StringControl({
						modelField: 'ValDatadestruction',
						valueChangeEvent: 'fieldChange:dscpp.datadestruction',
						id: 'TABDSCPPDSCPPDATADEST',
						name: 'DATADEST',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_DESTRUCTION60928),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 500,
						labelId: 'label_TABDSCPPDSCPPDATADEST',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPDATADISC: new fieldControlClass.StringControl({
						modelField: 'ValDatadisclosure',
						valueChangeEvent: 'fieldChange:dscpp.datadisclosure',
						id: 'TABDSCPPDSCPPDATADISC',
						name: 'DATADISC',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_DISCLOSURE50470),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 500,
						labelId: 'label_TABDSCPPDSCPPDATADISC',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPCOPYRIGH: new fieldControlClass.TextEditorControl({
						modelField: 'ValCopyrigh',
						valueChangeEvent: 'fieldChange:dscpp.copyrigh',
						id: 'TABDSCPPDSCPPCOPYRIGH',
						name: 'COPYRIGH',
						size: 'xlarge',
						label: computed(() => this.Resources.COPYRIGHT58510),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPPOLICYST: new fieldControlClass.StringControl({
						modelField: 'ValPolicystatement',
						valueChangeEvent: 'fieldChange:dscpp.policystatement',
						id: 'TABDSCPPDSCPPPOLICYST',
						name: 'POLICYST',
						size: 'xxlarge',
						label: computed(() => this.Resources.POLICY_STATEMENT43958),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 500,
						labelId: 'label_TABDSCPPDSCPPPOLICYST',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPDSCPPDATACOND: new fieldControlClass.StringControl({
						modelField: 'ValDataconds',
						valueChangeEvent: 'fieldChange:dscpp.dataconds',
						id: 'TABDSCPPDSCPPDATACOND',
						name: 'DATACOND',
						size: 'xxlarge',
						label: computed(() => this.Resources.DATA_CONDITIONS08191),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						maxLength: 500,
						labelId: 'label_TABDSCPPDSCPPDATACOND',
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPCOMPRNAME____: new fieldControlClass.LookupControl({
						modelField: 'TableComprName',
						valueChangeEvent: 'fieldChange:compr.name',
						id: 'TABDSCPPCOMPRNAME____',
						name: 'NAME',
						size: 'xxlarge',
						label: computed(() => this.Resources.RESPONSIBLE_PARTNER_49442),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCodcompr',
							dependencyEvent: 'fieldChange:dscpp.codcompr'
						},
						dependentFields: () => ({
							set 'compr.codcompy'(value) { vm.model.ValCodcompr.updateValue(value) },
							set 'compr.name'(value) { vm.model.TableComprName.updateValue(value) },
						}),
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPCOMPONAME____: new fieldControlClass.LookupControl({
						modelField: 'TableCompoName',
						valueChangeEvent: 'fieldChange:compo.name',
						id: 'TABDSCPPCOMPONAME____',
						name: 'NAME',
						size: 'xxlarge',
						label: computed(() => this.Resources.ENTERPRISE_NAME50437),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						externalCallbacks: {
							getModelField: vm.getModelField,
							getModelFieldValue: vm.getModelFieldValue,
							setModelFieldValue: vm.setModelFieldValue
						},
						externalProperties: {
							modelKeys: computed(() => vm.modelKeys)
						},
						lookupKeyModelField: {
							name: 'ValCodcompo',
							dependencyEvent: 'fieldChange:dscpp.codcompo'
						},
						dependentFields: () => ({
							set 'compo.codcompy'(value) { vm.model.ValCodcompo.updateValue(value) },
							set 'compo.name'(value) { vm.model.TableCompoName.updateValue(value) },
						}),
						controlLimits: [
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPPSEUDCOMPONEN: new fieldControlClass.TableListControl({
						id: 'TABDSCPPPSEUDCOMPONEN',
						name: 'COMPONEN',
						size: '',
						label: computed(() => this.Resources.APPLICABILITY_CROSS_55316),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDSTATUS__',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						controller: 'DSCPP',
						action: 'Tabdscpp_ValComponen',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.TextColumn({
								order: 1,
								name: 'Dscrc.ValIdentifier',
								area: 'DSCRC',
								field: 'IDENTIFIER',
								label: computed(() => this.Resources.COMPONENT44370),
								dataLength: 150,
								scrollData: 30,
								pkColumn: 'ValCoddscrp',
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValComponen',
							serverMode: true,
							pkColumn: 'ValCodrldsc',
							tableAlias: 'RLDSC',
							tableNamePlural: computed(() => this.Resources.REFERENCES_DESCRIPTS60534),
							viewManagement: '',
							showLimitsInfo: true,
							tableTitle: computed(() => this.Resources.APPLICABILITY_CROSS_55316),
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							crudActions: [
								{
									id: 'show',
									name: 'show',
									title: computed(() => this.Resources.CONSULTAR57388),
									icon: {
										icon: 'view'
									},
									isInReadOnly: true,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'SHOW',
										isControlled: true
									}
								},
								{
									id: 'edit',
									name: 'edit',
									title: computed(() => this.Resources.EDITAR11616),
									icon: {
										icon: 'pencil'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'EDIT',
										isControlled: true
									}
								},
								{
									id: 'duplicate',
									name: 'duplicate',
									title: computed(() => this.Resources.DUPLICAR09748),
									icon: {
										icon: 'duplicate'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'DUPLICATE',
										isControlled: true
									}
								},
								{
									id: 'delete',
									name: 'delete',
									title: computed(() => this.Resources.ELIMINAR21155),
									icon: {
										icon: 'delete'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'DELETE',
										isControlled: true
									}
								}
							],
							generalActions: [
								{
									id: 'insert',
									name: 'insert',
									title: computed(() => this.Resources.INSERIR43365),
									icon: {
										icon: 'add'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'RLDSC',
										mode: 'NEW',
										repeatInsertion: false,
										isControlled: true
									}
								},
							],
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
								id: 'RCA__RLDSC',
								name: '_RLDSC',
								title: '',
								isInReadOnly: true,
								params: {
									isRoute: true,
									action: vm.openFormAction,
									type: 'form',
									formName: 'RLDSC',
									mode: 'SHOW',
									isControlled: true
								}
							},
							formsDefinition: {
								'RLDSC': {
									fnKeySelector: (row) => row.Fields.ValCodrldsc,
									isPopup: false
								},
							},
							defaultSearchColumnName: '',
							defaultSearchColumnNameOriginal: '',
							defaultColumnSorting: {
								columnName: '',
								sortOrder: 'asc'
							}
						},
						globalEvents: ['changed-RLDSC', 'changed-DSCRC', 'changed-DSCPP'],
						uuid: 'Tabdscpp_ValComponen',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABDSCPPPSEUDAPPLICAB: new fieldControlClass.GroupControl({
						id: 'TABDSCPPPSEUDAPPLICAB',
						name: 'APPLICAB',
						size: 'block',
						label: computed(() => this.Resources.APPLICABILITY25450),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						tab: 'RECDSCP_PSEUDTABDSCPP',
						isCollapsible: true,
						anchored: false,
						directChildren: ['TABDSCPPPSEUDAPPS____'],
						controlLimits: [
						],
					}, this),
					TABDSCPPPSEUDAPPS____: new fieldControlClass.TableListControl({
						id: 'TABDSCPPPSEUDAPPS____',
						name: 'APPS',
						size: '',
						label: computed(() => this.Resources.APPLIC_OF_THE_DESCRI27127),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABDSCPPPSEUDAPPLICAB',
						tab: 'RECDSCP_PSEUDTABDSCPP',
						controller: 'DSCPP',
						action: 'Tabdscpp_ValApps',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.TextColumn({
								order: 1,
								name: 'ValDisplaytext',
								area: 'APPLP',
								field: 'DISPLAYTEXT',
								label: computed(() => this.Resources.DISPLAY_TEXT26895),
								dataLength: 150,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValApps',
							serverMode: true,
							pkColumn: 'ValCodappli',
							tableAlias: 'APPLP',
							tableNamePlural: computed(() => this.Resources.APPLIC_OF_THE_DESCRI27127),
							viewManagement: '',
							showLimitsInfo: true,
							tableTitle: computed(() => this.Resources.APPLIC_OF_THE_DESCRI27127),
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							crudActions: [
								{
									id: 'show',
									name: 'show',
									title: computed(() => this.Resources.CONSULTAR57388),
									icon: {
										icon: 'view'
									},
									isInReadOnly: true,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'SHOW',
										isControlled: true
									}
								},
								{
									id: 'edit',
									name: 'edit',
									title: computed(() => this.Resources.EDITAR11616),
									icon: {
										icon: 'pencil'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'EDIT',
										isControlled: true
									}
								},
								{
									id: 'duplicate',
									name: 'duplicate',
									title: computed(() => this.Resources.DUPLICAR09748),
									icon: {
										icon: 'duplicate'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'DUPLICATE',
										isControlled: true
									}
								},
								{
									id: 'delete',
									name: 'delete',
									title: computed(() => this.Resources.ELIMINAR21155),
									icon: {
										icon: 'delete'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'DELETE',
										isControlled: true
									}
								}
							],
							generalActions: [
								{
									id: 'insert',
									name: 'insert',
									title: computed(() => this.Resources.INSERIR43365),
									icon: {
										icon: 'add'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'APPLP',
										mode: 'NEW',
										repeatInsertion: false,
										isControlled: true
									}
								},
							],
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
								id: 'RCA__APPLP',
								name: '_APPLP',
								title: '',
								isInReadOnly: true,
								params: {
									isRoute: true,
									action: vm.openFormAction,
									type: 'form',
									formName: 'APPLP',
									mode: 'SHOW',
									isControlled: true
								}
							},
							formsDefinition: {
								'APPLP': {
									fnKeySelector: (row) => row.Fields.ValCodappli,
									isPopup: false
								},
							},
							defaultSearchColumnName: '',
							defaultSearchColumnNameOriginal: '',
							defaultColumnSorting: {
								columnName: '',
								sortOrder: 'asc'
							}
						},
						globalEvents: ['changed-DSCPP', 'changed-APPLP'],
						uuid: 'Tabdscpp_ValApps',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
						blockWhen: {
							// eslint-disable-next-line no-unused-vars
							fnFormula(params)
							{
								// Formula: [DSCPP->COMPLIES]==1
								return (this.ValComplies.value ? 1 : 0)===1
							},
							dependencyEvents: ['fieldChange:dscpp.complies'],
							isServerRecalc: false,
						},
					}, this),
					TABRECORPSEUDNOVOGR01: new fieldControlClass.GroupControl({
						id: 'TABRECORPSEUDNOVOGR01',
						name: 'NOVOGR01',
						size: 'block',
						label: '',
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						tab: 'RECDSCP_PSEUDTABRECOR',
						isCollapsible: false,
						anchored: false,
						directChildren: ['TABRECORPSEUDDOCUMS__'],
						controlLimits: [
						],
					}, this),
					TABRECORPSEUDDOCUMS__: new fieldControlClass.TableListControl({
						id: 'TABRECORPSEUDDOCUMS__',
						name: 'DOCUMS',
						size: 'block',
						label: computed(() => this.Resources.DOCUMENTS14470),
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						container: 'TABRECORPSEUDNOVOGR01',
						tab: 'RECDSCP_PSEUDTABRECOR',
						controller: 'DSCPP',
						action: 'Tabrecor_ValDocums',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.DocumentColumn({
								order: 1,
								name: 'ValDocum',
								area: 'DOCUM',
								field: 'DOCUM',
								label: computed(() => this.Resources.DOCUMENT00695),
								dataLength: 255,
								scrollData: 30,
								sortable: false,
								viewType: qEnums.documentViewTypeMode.preview,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.TextColumn({
								order: 2,
								name: 'ValDocname',
								area: 'DOCUM',
								field: 'DOCNAME',
								label: computed(() => this.Resources.FILE_NAME37493),
								dataLength: 255,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.TextColumn({
								order: 3,
								name: 'ValLinkfile',
								area: 'DOCUM',
								field: 'LINKFILE',
								label: computed(() => this.Resources.LINK_TO_FILE35721),
								dataLength: 255,
								scrollData: 30,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValDocums',
							serverMode: true,
							pkColumn: 'ValCoddocum',
							tableAlias: 'DOCUM',
							tableNamePlural: computed(() => this.Resources.DOCUMENTS14470),
							viewManagement: '',
							showLimitsInfo: true,
							tableTitle: computed(() => this.Resources.DOCUMENTS14470),
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							crudActions: [
								{
									id: 'show',
									name: 'show',
									title: computed(() => this.Resources.CONSULTAR57388),
									icon: {
										icon: 'view'
									},
									isInReadOnly: true,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'DOCUM',
										mode: 'SHOW',
										isControlled: true
									}
								},
								{
									id: 'edit',
									name: 'edit',
									title: computed(() => this.Resources.EDITAR11616),
									icon: {
										icon: 'pencil'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'DOCUM',
										mode: 'EDIT',
										isControlled: true
									}
								},
								{
									id: 'duplicate',
									name: 'duplicate',
									title: computed(() => this.Resources.DUPLICAR09748),
									icon: {
										icon: 'duplicate'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'DOCUM',
										mode: 'DUPLICATE',
										isControlled: true
									}
								},
								{
									id: 'delete',
									name: 'delete',
									title: computed(() => this.Resources.ELIMINAR21155),
									icon: {
										icon: 'delete'
									},
									isInReadOnly: false,
									params: {
										action: vm.openFormAction,
										type: 'form',
										formName: 'DOCUM',
										mode: 'DELETE',
										isControlled: true
									}
								}
							],
							generalActions: [
								{
									id: 'insert',
									name: 'insert',
									title: 'Upload',
									icon: {
                                        icon: 'add'
									},
									isInReadOnly: false,
									click: function() {
										// Emite um evento personalizado que será capturado pelo componente pai
										return { action: 'open-multi-upload' };
									}
								},
							],
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
								id: 'RCA__DOCUM',
								name: '_DOCUM',
								title: '',
								isInReadOnly: true,
								params: {
									isRoute: true,
									action: vm.openFormAction,
									type: 'form',
									formName: 'DOCUM',
									mode: 'SHOW',
									isControlled: true
								}
							},
							formsDefinition: {
								'DOCUM': {
									fnKeySelector: (row) => row.Fields.ValCoddocum,
									isPopup: true
								},
							},
							defaultSearchColumnName: '',
							defaultSearchColumnNameOriginal: '',
							defaultColumnSorting: {
								columnName: 'ValDocname',
								sortOrder: 'asc'
							}
						},
						globalEvents: ['changed-DOCUM', 'changed-DSCPP'],
						uuid: 'Tabrecor_ValDocums',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
					}, this),
					TABVERSIPSEUDEXPVRSN_: new fieldControlClass.TableListControl({
						id: 'TABVERSIPSEUDEXPVRSN_',
						name: 'EXPVRSN',
						size: 'block',
						label: '',
						placeholder: '',
						labelPosition: computed(() => this.labelAlignment.topleft),
						tab: 'RECDSCP_PSEUDTABVERSI',
						controller: 'DSCPP',
						action: 'Tabversi_ValExpvrsn',
						hasDependencies: false,
						isInCollapsible: true,
						columnsOriginal: [
							new listColumnTypes.TextColumn({
								order: 1,
								name: 'ValIdentifier',
								area: 'DSCRC',
								field: 'IDENTIFIER',
								label: computed(() => this.Resources.IDENTIFIER53792),
								dataLength: 150,
								scrollData: 100,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
							new listColumnTypes.NumericColumn({
								order: 2,
								name: 'ValNumversi',
								area: 'DSCRC',
								field: 'NUMVERSI',
								label: computed(() => this.Resources.VERSION35066),
								scrollData: 15,
								maxDigits: 15,
								decimalPlaces: 0,
							}, computed(() => vm.model), computed(() => vm.internalEvents)),
						],
						config: {
							name: 'ValExpvrsn',
							serverMode: true,
							pkColumn: 'ValCoddscrp',
							tableAlias: 'DSCRC',
							tableNamePlural: computed(() => this.Resources.DESCRIPTIVES_CHILDRE19328),
							viewManagement: '',
							showLimitsInfo: true,
							showAlternatePagination: true,
							permissions: {
							},
							searchBarConfig: {
								visibility: false,
								searchOnPressEnter: true
							},
							filtersVisible: false,
							allowColumnFilters: false,
							allowColumnSort: true,
							generalCustomActions: [
							],
							groupActions: [
							],
							customActions: [
							],
							MCActions: [
							],
							rowClickAction: {
							},
							formsDefinition: {
							},
							defaultSearchColumnName: '',
							defaultSearchColumnNameOriginal: '',
							defaultColumnSorting: {
								columnName: 'ValNumversi',
								sortOrder: 'desc'
							}
						},
						globalEvents: ['changed-COMPR', 'changed-COMPO', 'changed-DSCRC', 'changed-LANGU', 'changed-COUNT', 'changed-DMCDR'],
						uuid: 'Tabversi_ValExpvrsn',
						allSelectedRows: 'false',
						controlLimits: [
							{
								identifier: ['id', 'dscpp'],
								dependencyEvents: ['fieldChange:dscpp.coddscrp'],
								dependencyField: 'DSCPP.CODDSCRP',
								fnValueSelector: (model) => model.ValCoddscrp.value
							},
						],
					}, this),
					formTabs: new fieldControlClass.TabsControl({
						tabControlsIds: readonly([
							'RECDSCP_PSEUDTABDSCPP',
							'RECDSCP_PSEUDTABRECOR',
							'RECDSCP_PSEUDTABVERSI',
						])
					}, this),
				},

				model: new FormViewModel(this, {
					callbacks: {
						onUpdate: this.onUpdate,
						setFormKey: this.setFormKey
					}
				}),

				groupFields: readonly([
					'RECDSCP_PSEUDTABDSCPP',
					'TABDSCPPPSEUDDESCRIPT',
					'TABDSCPPPSEUDZNIDENTI',
					'TABDSCPPPSEUDNOVOGR01',
					'TABDSCPPPSEUDSTATUS__',
					'TABDSCPPPSEUDAPPLICAB',
					'RECDSCP_PSEUDTABRECOR',
					'TABRECORPSEUDNOVOGR01',
					'RECDSCP_PSEUDTABVERSI',
				]),

				tableFields: readonly([
					'TABDSCPPPSEUDCOMPONEN',
					'TABDSCPPPSEUDAPPS____',
					'TABRECORPSEUDDOCUMS__',
					'TABVERSIPSEUDEXPVRSN_',
				]),

				timelineFields: readonly([
				]),

				/**
				 * The Data API for easy access to model variables.
				 */
				dataApi: {
					Compo: {
						get ValName() { return vm.model.TableCompoName.value },
						set ValName(value) { vm.model.TableCompoName.updateValue(value) },
					},
					Compr: {
						get ValName() { return vm.model.TableComprName.value },
						set ValName(value) { vm.model.TableComprName.updateValue(value) },
					},
					Count: {
						get ValCountry() { return vm.model.TableCountCountry.value },
						set ValCountry(value) { vm.model.TableCountCountry.updateValue(value) },
					},
					Dmcdr: {
						get ValCode() { return vm.model.TableDmcdrCode.value },
						set ValCode(value) { vm.model.TableDmcdrCode.updateValue(value) },
						get ValCompletecode() { return vm.model.TableDmcdrComplcod.value },
						set ValCompletecode(value) { vm.model.TableDmcdrComplcod.updateValue(value) },
					},
					Dscpp: {
						get ValCodcompo() { return vm.model.ValCodcompo.value },
						set ValCodcompo(value) { vm.model.ValCodcompo.updateValue(value) },
						get ValCodcompr() { return vm.model.ValCodcompr.value },
						set ValCodcompr(value) { vm.model.ValCodcompr.updateValue(value) },
						get ValCodcount() { return vm.model.ValCodcount.value },
						set ValCodcount(value) { vm.model.ValCodcount.updateValue(value) },
						get ValCoddmcod() { return vm.model.ValCoddmcod.value },
						set ValCoddmcod(value) { vm.model.ValCoddmcod.updateValue(value) },
						get ValCodlangu() { return vm.model.ValCodlangu.value },
						set ValCodlangu(value) { vm.model.ValCodlangu.updateValue(value) },
						get ValCodversi() { return vm.model.ValCodversi.value },
						set ValCodversi(value) { vm.model.ValCodversi.updateValue(value) },
						get ValComplies() { return vm.model.ValComplies.value },
						set ValComplies(value) { vm.model.ValComplies.updateValue(value) },
						get ValContributor() { return vm.model.ValContributor.value },
						set ValContributor(value) { vm.model.ValContributor.updateValue(value) },
						get ValCopyrigh() { return vm.model.ValCopyrigh.value },
						set ValCopyrigh(value) { vm.model.ValCopyrigh.updateValue(value) },
						get ValCoverage() { return vm.model.ValCoverage.value },
						set ValCoverage(value) { vm.model.ValCoverage.updateValue(value) },
						get ValCreator() { return vm.model.ValCreator.value },
						set ValCreator(value) { vm.model.ValCreator.updateValue(value) },
						get ValDataconds() { return vm.model.ValDataconds.value },
						set ValDataconds(value) { vm.model.ValDataconds.updateValue(value) },
						get ValDatadestruction() { return vm.model.ValDatadestruction.value },
						set ValDatadestruction(value) { vm.model.ValDatadestruction.updateValue(value) },
						get ValDatadisclosure() { return vm.model.ValDatadisclosure.value },
						set ValDatadisclosure(value) { vm.model.ValDatadisclosure.updateValue(value) },
						get ValDatadistribution() { return vm.model.ValDatadistribution.value },
						set ValDatadistribution(value) { vm.model.ValDatadistribution.updateValue(value) },
						get ValDatahandling() { return vm.model.ValDatahandling.value },
						set ValDatahandling(value) { vm.model.ValDatahandling.updateValue(value) },
						get ValDate() { return vm.model.ValDate.value },
						set ValDate(value) { vm.model.ValDate.updateValue(value) },
						get ValDay() { return vm.model.ValDay.value },
						set ValDay(value) { vm.model.ValDay.updateValue(value) },
						get ValExportregistrationstmt() { return vm.model.ValExportregistrationstmt.value },
						set ValExportregistrationstmt(value) { vm.model.ValExportregistrationstmt.updateValue(value) },
						get ValExtensioncode() { return vm.model.ValExtensioncode.value },
						set ValExtensioncode(value) { vm.model.ValExtensioncode.updateValue(value) },
						get ValExtensionproducer() { return vm.model.ValExtensionproducer.value },
						set ValExtensionproducer(value) { vm.model.ValExtensionproducer.updateValue(value) },
						get ValFormat() { return vm.model.ValFormat.value },
						set ValFormat(value) { vm.model.ValFormat.updateValue(value) },
						get ValId() { return vm.model.ValId.value },
						set ValId(value) { vm.model.ValId.updateValue(value) },
						get ValIdentifier() { return vm.model.ValIdentifier.value },
						set ValIdentifier(value) { vm.model.ValIdentifier.updateValue(value) },
						get ValInfoname() { return vm.model.ValInfoname.value },
						set ValInfoname(value) { vm.model.ValInfoname.updateValue(value) },
						get ValInwork() { return vm.model.ValInwork.value },
						set ValInwork(value) { vm.model.ValInwork.updateValue(value) },
						get ValIssuenumber() { return vm.model.ValIssuenumber.value },
						set ValIssuenumber(value) { vm.model.ValIssuenumber.updateValue(value) },
						get ValIssuetype() { return vm.model.ValIssuetype.value },
						set ValIssuetype(value) { vm.model.ValIssuetype.updateValue(value) },
						get ValLearneventcode() { return vm.model.ValLearneventcode.value },
						set ValLearneventcode(value) { vm.model.ValLearneventcode.updateValue(value) },
						get ValLearncode() { return vm.model.ValLearncode.value },
						set ValLearncode(value) { vm.model.ValLearncode.updateValue(value) },
						get ValModelidentcode() { return vm.model.ValModelidentcode.value },
						set ValModelidentcode(value) { vm.model.ValModelidentcode.updateValue(value) },
						get ValMonth() { return vm.model.ValMonth.value },
						set ValMonth(value) { vm.model.ValMonth.updateValue(value) },
						get ValPolicystatement() { return vm.model.ValPolicystatement.value },
						set ValPolicystatement(value) { vm.model.ValPolicystatement.updateValue(value) },
						get ValPublisher() { return vm.model.ValPublisher.value },
						set ValPublisher(value) { vm.model.ValPublisher.updateValue(value) },
						get ValRelation() { return vm.model.ValRelation.value },
						set ValRelation(value) { vm.model.ValRelation.updateValue(value) },
						get ValRights() { return vm.model.ValRights.value },
						set ValRights(value) { vm.model.ValRights.updateValue(value) },
						get ValSeccavat() { return vm.model.ValSeccavat.value },
						set ValSeccavat(value) { vm.model.ValSeccavat.updateValue(value) },
						get ValSeccocla() { return vm.model.ValSeccocla.value },
						set ValSeccocla(value) { vm.model.ValSeccocla.updateValue(value) },
						get ValSecurcla() { return vm.model.ValSecurcla.value },
						set ValSecurcla(value) { vm.model.ValSecurcla.updateValue(value) },
						get ValSource() { return vm.model.ValSource.value },
						set ValSource(value) { vm.model.ValSource.updateValue(value) },
						get ValSubject() { return vm.model.ValSubject.value },
						set ValSubject(value) { vm.model.ValSubject.updateValue(value) },
						get ValSystemdiffcode() { return vm.model.ValSystemdiffcode.value },
						set ValSystemdiffcode(value) { vm.model.ValSystemdiffcode.updateValue(value) },
						get ValTechname() { return vm.model.ValTechname.value },
						set ValTechname(value) { vm.model.ValTechname.updateValue(value) },
						get ValTitle() { return vm.model.ValTitle.value },
						set ValTitle(value) { vm.model.ValTitle.updateValue(value) },
						get ValToggle() { return vm.model.ValToggle.value },
						set ValToggle(value) { vm.model.ValToggle.updateValue(value) },
						get ValType() { return vm.model.ValType.value },
						set ValType(value) { vm.model.ValType.updateValue(value) },
						get ValYear() { return vm.model.ValYear.value },
						set ValYear(value) { vm.model.ValYear.updateValue(value) },
					},
					Langu: {
						get ValLanguageisocode() { return vm.model.TableLanguLanguiso.value },
						set ValLanguageisocode(value) { vm.model.TableLanguLanguiso.updateValue(value) },
					},
					keys: {
						/** The primary key of the DSCPP table */
						get dscpp() { return vm.model.ValCoddscrp },
						/** The foreign key to the DMCDR table */
						get dmcdr() { return vm.model.ValCoddmcod },
						/** The foreign key to the LANGU table */
						get langu() { return vm.model.ValCodlangu },
						/** The foreign key to the COUNT table */
						get count() { return vm.model.ValCodcount },
						/** The foreign key to the COMPR table */
						get compr() { return vm.model.ValCodcompr },
						/** The foreign key to the COMPO table */
						get compo() { return vm.model.ValCodcompo },
					},
					get extraProperties() { return vm.model.extraProperties },
				},
                showMultiUploadModal: false,
                showTreeView: false, // Controla se mostra a vista de árvore (true) ou lista (false)
			}
		},

		beforeRouteEnter(to, _, next)
		{
			// Called before the route that renders this component is confirmed.
			// Does NOT have access to `this` component instance, because
			// it has not been created yet when this guard is called!

			next((vm) => {
				vm.initFormProperties(to)
			})
		},

		beforeRouteLeave(to, _, next)
		{
			if (to.params.isControlled === 'true')
			{
				genericFunctions.setNavigationState(false)
				next()
			}
			else
				this.cancel(next)
		},

		beforeRouteUpdate(to, _, next)
		{
			if (to.params.isControlled === 'true')
				next()
			else
				this.cancel(next)
		},

		mounted()
		{
/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS FORM_CODEJS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
		},

		methods: {
			/**
			 * Called before form init.
			 */
			async beforeLoad()
			{
				let loadForm = true

				// Execute the "Before init" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.beforeInit)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('before-load-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_LOAD_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return loadForm
			},

			/**
			 * Called after form init.
			 */
			async afterLoad()
			{
				// Execute the "After init" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.afterInit)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('after-load-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS FORM_LOADED_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
			},

			/**
			 * Called before an apply action is performed.
			 */
			async beforeApply()
			{
				let applyForm = true // Set to 'false' to cancel form apply.

				// Execute the "Before apply" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.beforeApply)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				const canSetDocums = await this.model.updateFilesTickets(true)

				if (canSetDocums)
				{
					applyForm = await this.model.setDocumentChanges()

					if (applyForm)
					{
						const results = await this.model.saveDocuments()
						applyForm = results.every((e) => e === true)
					}
				}

				this.emitEvent('before-apply-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_APPLY_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return applyForm
			},

			/**
			 * Called after an apply action is performed.
			 */
			async afterApply()
			{
				// Execute the "After apply" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.afterApply)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('after-apply-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS AFTER_APPLY_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
			},

			/**
			 * Called before the record is saved.
			 */
			async beforeSave()
			{
				let saveForm = true // Set to 'false' to cancel form saving.

				// Execute the "Before save" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.beforeSave)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				const canSetDocums = await this.model.updateFilesTickets()

				if (canSetDocums)
				{
					saveForm = await this.model.setDocumentChanges()

					if (saveForm)
					{
						const results = await this.model.saveDocuments()
						saveForm = results.every((e) => e === true)
					}
				}

				this.emitEvent('before-save-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_SAVE_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return saveForm
			},

			/**
			 * Called after the record is saved.
			 */
			async afterSave()
			{
				let redirectPage = true // Set to 'false' to cancel page redirect.

				// Execute the "After save" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.afterSave)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('after-save-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
//Platform: VUE | Type: AFTER_SAVE_JS | Module: TDS | Parameter: RECDSCP | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:1d282e41-d9cc-4386-b3d2-0b1921a9d24a
				//created by [SF] at [2025.06.06]
                this.navigation.currentLevel.previousLevel.setEntryValue({ key: 'dscrp_aux', value: "1" });
//END_MANUALCODE
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return redirectPage
			},

			/**
			 * Called before the record is deleted.
			 */
			async beforeDel()
			{
				let deleteForm = true // Set to 'false' to cancel form delete.

				this.emitEvent('before-delete-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_DEL_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return deleteForm
			},

			/**
			 * Called after the record is deleted.
			 */
			async afterDel()
			{
				let redirectPage = true // Set to 'false' to cancel page redirect.

				this.emitEvent('after-delete-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS AFTER_DEL_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return redirectPage
			},

			/**
			 * Called before leaving the form.
			 */
			async beforeExit()
			{
				let leaveForm = true // Set to 'false' to cancel page redirect.

				// Execute the "Before exit" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.beforeExit)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('before-exit-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS BEFORE_EXIT_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				return leaveForm
			},

			/**
			 * Called after leaving the form.
			 */
			async afterExit()
			{
				// Execute the "After exit" triggers.
				const triggers = this.getTriggers(qEnums.triggerEvents.afterExit)
				for (let trigger of triggers)
					await formFunctions.executeTriggerAction(trigger)

				this.emitEvent('after-exit-form')

/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS AFTER_EXIT_JS RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
			},

			/**
			 * Called whenever a field's value is updated.
			 * @param {string} fieldName The name of the field in the format [table].[field] (ex: 'person.name')
			 * @param {object} fieldObject The object representing the field in the model
			 * @param {any} fieldValue The value of the field
			 * @param {any} oldFieldValue The previous value of the field
			 */
			// eslint-disable-next-line
			onUpdate(fieldName, fieldObject, fieldValue, oldFieldValue)
			{
/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS DLGUPDT RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				this.afterFieldUpdate(fieldName, fieldObject)
			},

			/**
			 * Called whenever a field is unfocused.
			 * @param {*} fieldObject The object representing the field in the model
			 * @param {*} fieldValue The value of the field
			 */
			// eslint-disable-next-line
			onBlur(fieldObject, fieldValue)
			{
/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS CTRLBLR RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				this.afterFieldUnfocus(fieldObject, fieldValue)
			},

			/**
			 * Called whenever a control's value is updated.
			 * @param {string} controlField The name of the field in the controls that will be updated
			 * @param {object} control The object representing the field in the controls
			 * @param {any} fieldValue The value of the field
			 */
			// eslint-disable-next-line
			onControlUpdate(controlField, control, fieldValue)
			{
/* eslint-disable indent, vue/html-indent, vue/script-indent */
// USE /[MANUAL TDS CTRLUPD RECDSCP]/
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */

				this.afterControlUpdate(controlField, fieldValue)
			},
/* eslint-disable indent, vue/html-indent, vue/script-indent */
//Platform: VUE | Type: FUNCTIONS_JS | Module: TDS | Parameter: RECDSCP | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:4cc65cb5-6c54-4bbf-a6bf-79ec61d76cf7
            // Created by [HG] at [2025.04.23]
            // Updated by [HG] at [2025.06.04]
            onMultiFilesUploaded() {
                // Refresh da tabela DOCUM após upload
                if (this.controls && this.controls.TABRECORPSEUDDOCUMS__ && this.controls.TABRECORPSEUDDOCUMS__.refresh) {
                    this.controls.TABRECORPSEUDDOCUMS__.refresh();
                }
                this.showMultiUploadModal = false;
                this.controls.TABRECORPSEUDDOCUMS__.reloadList();
            },

            /**
             * Manipulador de eventos de ação da tabela
             * @param {Object} action - Ação disparada pela tabela
             */
            handleTableRowAction(action) {
                console.log('Ação da tabela recebida:', action);
				if (action && action.id === 'insert') {
					console.log('Abrindo modal de upload múltiplo');
					this.showMultiUploadModal = true;
				}
				else if (action && action.params && action.params.formName) {
					this.navigateToRouteName("form-" + action.params.formName, { mode: action.params.mode, id: action.rowKey, modes: "", isDuplicate: true, isControlled: true });
				}
				else {
					console.log('Ação não reconhecida ou sem parâmetros necessários:', action);
				}
            },

            /**
             * Alterna a visibilidade do modal de upload múltiplo
             * @param {boolean} show - Define se o modal deve ser exibido ou ocultado
             */
            toggleMultiUploadModal(show) {
                console.log('toggleMultiUploadModal chamado com show =', show);
                console.log('Valor atual de showMultiUploadModal antes da alteração:', this.showMultiUploadModal);
                this.showMultiUploadModal = show;
                console.log('Novo valor de showMultiUploadModal:', this.showMultiUploadModal);
            },

            /**
             * Alterna entre a vista de lista e a vista de árvore
             */
            toggleViewMode() {
                console.log('toggleViewMode chamado - Vista atual:', this.showTreeView ? 'Árvore' : 'Lista');
                // O v-model já alterou o valor, apenas fazemos log aqui
                if (this.showTreeView) {
                    this.loadTreeViewData();
                }
            },

            /**
             * Carrega os dados para a vista de árvore
             */
            loadTreeViewData() {
                console.log('Carregando dados da vista de árvore...');

                // Obter dados da tabela atual
                const tableData = this.controls.TABRECORPSEUDDOCUMS__.rows || [];
                console.log('Dados da tabela (raw):', tableData);

                // Log detalhado dos campos disponíveis
                if (tableData.length > 0) {
                    console.log('Campos disponíveis no primeiro item:', Object.keys(tableData[0]));
                    console.log('Primeiro item completo:', tableData[0]);

                    // Log específico dos Fields
                    if (tableData[0].Fields) {
                        console.log('Campos em Fields:', Object.keys(tableData[0].Fields));
                        console.log('Valores em Fields:', tableData[0].Fields);

                        // Tentar encontrar campos de nome
                        const fields = tableData[0].Fields;
                        console.log('Possíveis campos de nome:');
                        Object.keys(fields).forEach(key => {
                            if (key.toLowerCase().includes('doc') || key.toLowerCase().includes('name') || key.toLowerCase().includes('nom')) {
                                console.log(`  ${key}:`, fields[key]);
                            }
                        });
                    }
                }

                // Converter para estrutura hierárquica
                this.treeViewData = this.buildTreeStructure(tableData);
                console.log('Estrutura de árvore construída:', this.treeViewData);
            },

            /**
             * Constrói a estrutura hierárquica a partir dos dados da tabela
             */
            buildTreeStructure(tableData) {
                console.log('Construindo estrutura de árvore com dados:', tableData);

                // Por agora, vamos criar uma estrutura simples para testar
                // e descobrir os nomes corretos dos campos
                const tree = [];

                console.log('=== ANÁLISE DOS DADOS ===');
                tableData.forEach((item, index) => {
                    const fields = item.Fields || {};

                    console.log(`Item ${index}:`, {
                        allFieldKeys: Object.keys(fields),
                        fieldsWithValues: Object.keys(fields).reduce((acc, key) => {
                            acc[key] = fields[key]?.value || fields[key];
                            return acc;
                        }, {})
                    });

                    // Tentar encontrar o nome do ficheiro em vários campos possíveis
                    let fileName = 'Ficheiro sem nome';
                    const possibleNameFields = ['ValDocum', 'ValDocname', 'ValNome', 'Nome', 'Name', 'Docum', 'Docname'];

                    for (const fieldName of possibleNameFields) {
                        if (fields[fieldName]?.value) {
                            fileName = fields[fieldName].value;
                            console.log(`Nome encontrado em ${fieldName}:`, fileName);
                            break;
                        }
                    }

                    // Criar item simples para teste
                    const fileItem = {
                        id: fields.ValCoddocum?.value || `file_${index}`,
                        name: fileName,
                        isFolder: false,
                        level: 0,
                        size: fields.ValTamanho?.value || 0,
                        modified: fields.ValCre_date?.value ? new Date(fields.ValCre_date.value).toLocaleDateString() : new Date().toLocaleDateString(),
                        originalData: item
                    };

                    tree.push(fileItem);
                });

                // Adicionar algumas pastas de exemplo para testar a estrutura
                if (tree.length > 0) {
                    tree.unshift({
                        id: 'folder_example',
                        name: 'Documentos',
                        isFolder: true,
                        level: 0,
                        expanded: false,
                        children: [
                            {
                                id: 'file_example',
                                name: 'exemplo.pdf',
                                isFolder: false,
                                level: 1,
                                size: 1024000,
                                modified: new Date().toLocaleDateString()
                            }
                        ],
                        size: 0,
                        modified: new Date().toLocaleDateString()
                    });
                }

                console.log('Estrutura final da árvore (simplificada para teste):', tree);
                return tree;
            },

            /**
             * Expande ou colapsa uma pasta
             */
			toggleFolder(item) {
                if (item.isFolder) {
                    console.log('Toggling folder:', item.name, 'expanded:', item.expanded);
                    item.expanded = !item.expanded;

                    // Forçar reatividade
                    this.$forceUpdate();
                }
            },

            /**
             * Dispara o input de upload de pasta
             */
            triggerFolderUpload() {
                console.log('Disparando upload de pasta...');
                this.$refs.folderInput && this.$refs.folderInput.click();
            },

            /**
             * Processa o upload de uma pasta inteira
             */
            async onFolderUpload(event) {
                const files = Array.from(event.target.files || []);
                console.log('Ficheiros selecionados para upload:', files);

                if (files.length === 0) return;

                // Processar cada ficheiro com o seu relative path
                for (const file of files) {
                    const relativePath = file.webkitRelativePath || '';
                    const pathParts = relativePath.split('/');
                    const fileName = pathParts.pop(); // Remove o nome do ficheiro
                    const folderPath = pathParts.join('/'); // Junta o caminho da pasta

                    console.log(`Processando: ${fileName} em ${folderPath}`);

                    // Aqui você pode chamar a API de upload passando o relativePath
                    // Por agora, vamos apenas simular
                    await this.uploadFileWithPath(file, folderPath);
                }

                // Limpar o input e recarregar a vista
                event.target.value = '';
                this.loadTreeViewData();
            },

            /**
             * Upload de ficheiro com caminho relativo
             */
            async uploadFileWithPath(file, relativePath) {
                console.log(`Uploading ${file.name} to path: ${relativePath}`);

                // TODO: Implementar chamada à API com relativePath
                // Esta função deve ser integrada com o sistema de upload existente
                // passando o relativePath para ser gravado no campo relpath

                return new Promise(resolve => {
                    setTimeout(() => {
                        console.log(`Upload simulado completo para ${file.name}`);
                        resolve();
                    }, 100);
                });
            },

            /**
             * Atualiza a vista de árvore
             */
            refreshTreeView() {
                console.log('Atualizando vista de árvore...');
                if (this.controls.TABRECORPSEUDDOCUMS__.refresh) {
                    this.controls.TABRECORPSEUDDOCUMS__.refresh();
                }
                this.loadTreeViewData();
            },

            /**
             * Formata o tamanho do ficheiro
             */
            formatFileSize(bytes) {
                if (!bytes || bytes === 0) return '0 B';

                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));

                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }
//END_MANUALCODE
// eslint-disable-next-line
/* eslint-enable indent, vue/html-indent, vue/script-indent */
		},

		watch: {
			// Watchers for changes in the state of tabs.
			'controls.formTabs.selectedTab'(newVal)
			{
				const data = {
					navigationId: this.navigationId,
					key: this.storeKey,
					formInfo: this.formInfo,
					fieldId: 'formTabs',
					containerState: newVal
				}
				this.storeContainerState(data)
			},

			// Watcher para mudanças no toggle de vista
			showTreeView(newVal, oldVal) {
				console.log('showTreeView mudou de', oldVal, 'para', newVal);
				this.toggleViewMode();
			},

			// Watcher para mudanças nos dados da tabela
			'controls.TABRECORPSEUDDOCUMS__.rows': {
				handler(newVal) {
					if (this.showTreeView && newVal) {
						console.log('Dados da tabela mudaram, atualizando vista de árvore...');
						this.loadTreeViewData();
					}
				},
				deep: true
			}
		}
	}
</script>

<style scoped>
/* Toggle Switch Styles */
.view-toggle-switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 24px;
	cursor: pointer;
}

.view-toggle-switch input {
	opacity: 0;
	width: 0;
	height: 0;
	position: absolute;
}

.toggle-slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	transition: all 0.4s ease;
	border-radius: 24px;
	border: 2px solid #ddd;
}

.toggle-slider.active {
	background-color: #2196F3;
	border-color: #2196F3;
}

.toggle-knob {
	position: absolute;
	height: 16px;
	width: 16px;
	left: 2px;
	top: 2px;
	background-color: white;
	transition: all 0.4s ease;
	border-radius: 50%;
	box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-slider.active .toggle-knob {
	transform: translateX(26px);
}

.view-toggle-switch:hover .toggle-slider {
	box-shadow: 0 0 8px rgba(33, 150, 243, 0.3);
}

/* Tree View Styles */
.hover-row:hover {
	background-color: #f8f9fa !important;
}

.tree-view-container {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.tree-item {
	transition: background-color 0.2s ease;
}

.tree-item:hover {
	background-color: #f0f8ff;
}

.folder-icon {
	transition: transform 0.2s ease;
}

.expanded .folder-icon {
	transform: rotate(90deg);
}
</style>
