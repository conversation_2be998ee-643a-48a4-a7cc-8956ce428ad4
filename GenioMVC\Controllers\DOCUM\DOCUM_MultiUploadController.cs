using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using System.IO;
using System;
using CSGenio.business;
using CSGenio.persistence;
using System.Xml.Linq;
using DocumentFormat.OpenXml.Wordprocessing;
using Quidgest.Persistence.GenericQuery;

namespace GenioMVC.Controllers
{
    public partial class DOCUM_MultiUploadController : ControllerBase
    {
        public DOCUM_MultiUploadController(UserContextService userContext) : base(userContext) { }

        /// <summary>
        /// DTO = Data Transfer Object
        /// É uma classe simples usada para transportar dados entre o frontend e o backend, agrupando vários parâmetros num único objeto.
        /// </summary>
        public class DocumMultiUploadDto
        {
            public IFormFile File { get; set; }
            public string Nome { get; set; }
            public string Descricao { get; set; }
            public string ParentId { get; set; }
            public string ParentTable { get; set; }
        }

        [HttpPost]
        [DisableRequestSizeLimit]
        public async Task<IActionResult> MultiUploadCreate()
        {
            try
            {
                var form = Request.Form;

                var file = form.Files.FirstOrDefault();
                if (file == null || file.Length == 0)
                    return BadRequest(new { success = false, message = "Ficheiro inválido" });

                var nome = form["Nome"].ToString();
                var descricao = form["Descricao"].ToString();
                var parentId = form["ParentId"].ToString();
                var parentTable = form["ParentTable"].ToString();

                byte[] fileBytes;
                using (var ms = new MemoryStream())
                {
                    await file.CopyToAsync(ms);
                    fileBytes = ms.ToArray();
                }

                // Transação, gravação etc. (sem alterações)
                PersistentSupport sp = m_userContext.PersistentSupport;
                sp.openTransaction();
                CSGenioAdocum docum = new CSGenioAdocum(m_userContext.User);
                docum.insertPseud(sp);

                docum.ValDocum = nome;
                docum.Zzstate = 0;
                if (parentTable == "DSCPP") docum.ValCoddscrp = parentId;
				//else if (parentTable == "LANGU") docum.ValCodlangu = parentId;
                docum.ValDocumfk = "";
                docum.ValCre_user = m_userContext.User.Name;
                docum.ValCre_date = DateTime.Now;

                docum.insertNameValueFileDB("docum", fileBytes, file.FileName + "_", "", sp, "1", null);
                docum.updateDirect(sp);
                sp.closeTransaction();

                return Ok(new
                {
                    success = true,
                    documId = docum.ValCoddocum,
                    fileName = nome
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }


        /// <summary>
        /// Cria um novo registo DOCUM com ficheiro associado (multi-upload).
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        //[HttpPost]
        //[DisableRequestSizeLimit]
        //public async Task<IActionResult> MultiUploadCreate([FromForm] DocumMultiUploadDto dto)
        //{
        //    if (dto.File == null || dto.File.Length == 0)
        //        return BadRequest(new { success = false, message = "Ficheiro inválido" });

        //    try
        //    {
        //        // 1. Ler o ficheiro binário
        //        byte[] fileBytes;
        //        using (var ms = new MemoryStream())
        //        {
        //            await dto.File.CopyToAsync(ms);
        //            fileBytes = ms.ToArray();
        //        }

        //        // 2. Gravar na base de dados (adapta ao teu contexto/ORM)
        //        // Guardar ficheiro numa pasta temporária (ajustar para destino real)
        //        PersistentSupport sp = m_userContext.PersistentSupport;
        //        sp.openTransaction();
        //        CSGenioAdocum docum = new CSGenioAdocum(m_userContext.User);
        //        docum.insertPseud(sp);

        //        docum.ValDocum = dto.Nome;
        //        docum.Zzstate = 0;
        //        switch (dto.ParentTable)
        //        {
        //            case "DSCRP":
        //                docum.ValCoddscrp = dto.ParentId;
        //                break;
        //        }
        //        docum.ValDocumfk = "";
        //        docum.ValCre_user = m_userContext.User.Name;
        //        docum.ValCre_date = DateTime.Now;
        //        docum.insertNameValueFileDB("docum", fileBytes, dto.File.FileName + "_", "", sp, "1", null);
        //        docum.updateDirect(sp);
        //        sp.closeTransaction();

        //        // 4. Devolver resultado
        //        return Ok(new
        //        {
        //            success = true,
        //            documId = docum.ValCoddocum,
        //            fileName = dto.Nome
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, new { success = false, message = ex.Message });
        //    }

        //}

        /// <summary>
        /// Returns the maximum allowed upload size (in MB).
        /// </summary>
        [HttpPost]
        public IActionResult GetMaxUploadSize()
        {
            try
            {
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "web.config");
                if (!System.IO.File.Exists(configPath))
                    return Ok(new { maxUploadSizeMB = 28 }); // fallback

                var doc = XDocument.Load(configPath);
                var ns = doc.Root.GetDefaultNamespace();

                // Caminho para <requestLimits>
                var requestLimits = doc.Descendants("requestLimits").FirstOrDefault();
                if (requestLimits != null && requestLimits.Attribute("maxAllowedContentLength") != null)
                {
                    var maxBytes = long.Parse(requestLimits.Attribute("maxAllowedContentLength").Value);
                    var maxMb = (int)(maxBytes / (1024 * 1024));
                    return Ok(new { maxUploadSizeMB = maxMb });
                }
                // fallback se não existir
                return Ok(new { maxUploadSizeMB = 28 });
            }
            catch
            {
                return Ok(new { maxUploadSizeMB = 28 });
            }
        }

        [HttpPost]
        public ActionResult GetDocumentTree(string coddscrp)
        {
            try
            {
                var _user = m_userContext.User;
                var values = new GlobalFunctions(_user, _user.CurrentModule).GetValues(new SelectQuery()
                        .Select(CSGenioAdocum.FldCoddocum)
                .From(Area.AreaDOCUM)
                    .Join("docums", TableJoinType.Inner).On(CriteriaSet.And().Equal(CSGenioAdocum.FldDocumfk, CSGenioAdocums.FldDocumid))
                        .Where(CriteriaSet.And()
                            .Equal(CSGenioAdocum.FldCoddscrp, coddscrp)
                            .Equal(CSGenioAdocum.FldZzstate, 0)
                            .Equal(CSGenioAdocums.FldZzstate, 0)));

                return Json(new { success = "OK", data = values });
            }
            catch (Exception ex)
            {
                return Json(new { success = "E", message = ex.Message});
            }
        }

    }

    // Exemplo de modelo DOCUM (adapta ao teu projeto Genio)
    public class DOCUM
    {
        public int Id { get; set; }
        public string Nome { get; set; }
        public string Descricao { get; set; }
        public string ParentId { get; set; }
        public byte[] Ficheiro { get; set; }
        public string TipoMime { get; set; }
        public long Tamanho { get; set; }
    }
}